part of 'res_brasil_beneficiary_cubit.dart';

abstract class ResBrasilBeneficiaryState extends Equatable {
  const ResBrasilBeneficiaryState();

  @override
  List<Object> get props => [];
}

class InitialResBrasilBeneficiaryState extends ResBrasilBeneficiaryState {}

class LoadingResBrasilBeneficiaryState extends ResBrasilBeneficiaryState {
  @override
  List<Object> get props => [];
}

class ErrorResBrasilBeneficiaryState extends ResBrasilBeneficiaryState {
  final String message;

  @override
  List<Object> get props => [message];

  const ErrorResBrasilBeneficiaryState({required this.message});
}

class ErrorResBrasilBeneficiaryNotFoundState
    extends ResBrasilBeneficiaryState {}

class LoadedResBrasilBeneficiaryState extends ResBrasilBeneficiaryState {
  final ResBrazilBeneficiaryModel resBrazilBeneficiaryModel;

  @override
  List<Object> get props => [resBrazilBeneficiaryModel];

  const LoadedResBrasilBeneficiaryState(
      {required this.resBrazilBeneficiaryModel});
}
