part of 'search_medicines_cubit.dart';

abstract class SearchMedicinesState extends Equatable {
  const SearchMedicinesState();
}

class SearchMedicinesInitial extends SearchMedicinesState {
  @override
  List<Object> get props => [];
}

class LoadingSearchMedicinesState extends SearchMedicinesState {
  @override
  List<Object?> get props => [];
}

class LoadedSearchListMedicinesState extends SearchMedicinesState {
  final List<MedicineModel> medicines;

  @override
  List<Object?> get props => [];

  const LoadedSearchListMedicinesState({required this.medicines});
}

class ErrorSearchMedicinesState extends SearchMedicinesState {
  final String message;

  @override
  List<Object> get props => [];

  const ErrorSearchMedicinesState(this.message);
}
