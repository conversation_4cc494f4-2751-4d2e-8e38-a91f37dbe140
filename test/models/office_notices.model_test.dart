import 'package:cooperado_minha_unimed/models/office_notices.model.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('OfficeNoticeModel', () {
    test('Deve criar uma instância com todos os valores nulos', () {
      final model = OfficeNoticeModel();

      expect(model.codNotificacao, isNull);
      expect(model.codUnimed, isNull);
      expect(model.codPrestador, isNull);
      expect(model.codPreSolic, isNull);
      expect(model.nomeBeneficiario, isNull);
      expect(model.unimedCarteira, isNull);
      expect(model.codCarteira, isNull);
      expect(model.dvCarteira, isNull);
      expect(model.codTipoNotificacao, isNull);
      expect(model.descTipoNotificacao, isNull);
      expect(model.codTipoDestNotif, isNull);
      expect(model.notificacao, isNull);
      expect(model.dataNotificacao, isNull);
      expect(model.dataLeitura, isNull);
      expect(model.dataOcultacao, isNull);
      expect(model.exibirRelatorio, isNull);
      expect(model.indicacaoClinicaGuia, isNull);
      expect(model.justificativaGuia, isNull);
      expect(model.codCid, isNull);
      expect(model.codHospital, isNull);
      expect(model.quantidadeDiarias, isNull);
      expect(model.codRegimeInternacao, isNull);
      expect(model.codTipoInternacao, isNull);
      expect(model.possuiPendencia, isNull);
    });

    test('Deve criar uma instância com valores preenchidos', () {
      final model = OfficeNoticeModel(
        codNotificacao: 123,
        codUnimed: 1,
        codPrestador: 2,
        codPreSolic: 3,
        nomeBeneficiario: 'Beneficiário Teste',
        unimedCarteira: 456,
        codCarteira: 789,
        dvCarteira: 'A',
        codTipoNotificacao: 10,
        descTipoNotificacao: 'Notificação Teste',
        codTipoDestNotif: 20,
        notificacao: 'Detalhes da notificação',
        dataNotificacao: '2023-10-01',
        dataLeitura: '2023-10-02',
        dataOcultacao: '2023-10-03',
        exibirRelatorio: true,
        indicacaoClinicaGuia: 'Indicação Teste',
        justificativaGuia: 'Justificativa Teste',
        codCid: 'CID10',
        codHospital: 300,
        quantidadeDiarias: 5,
        codRegimeInternacao: 2,
        codTipoInternacao: 1,
        possuiPendencia: true,
      );

      expect(model.codNotificacao, 123);
      expect(model.codUnimed, 1);
      expect(model.codPrestador, 2);
      expect(model.codPreSolic, 3);
      expect(model.nomeBeneficiario, 'Beneficiário Teste');
      expect(model.unimedCarteira, 456);
      expect(model.codCarteira, 789);
      expect(model.dvCarteira, 'A');
      expect(model.codTipoNotificacao, 10);
      expect(model.descTipoNotificacao, 'Notificação Teste');
      expect(model.codTipoDestNotif, 20);
      expect(model.notificacao, 'Detalhes da notificação');
      expect(model.dataNotificacao, '2023-10-01');
      expect(model.dataLeitura, '2023-10-02');
      expect(model.dataOcultacao, '2023-10-03');
      expect(model.exibirRelatorio, true);
      expect(model.indicacaoClinicaGuia, 'Indicação Teste');
      expect(model.justificativaGuia, 'Justificativa Teste');
      expect(model.codCid, 'CID10');
      expect(model.codHospital, 300);
      expect(model.quantidadeDiarias, 5);
      expect(model.codRegimeInternacao, 2);
      expect(model.codTipoInternacao, 1);
      expect(model.possuiPendencia, true);
    });

    test('Deve desserializar corretamente a partir de JSON', () {
      final json = {
        'codNotificacao': 123,
        'codUnimed': 1,
        'codPrestador': 2,
        'codPreSolic': 3,
        'nomeBeneficiario': 'Beneficiário Teste',
        'unimedCarteira': 456,
        'codCarteira': 789,
        'dvCarteira': 'A',
        'codTipoNotificacao': 10,
        'descTipoNotificacao': 'Notificação Teste',
        'codTipoDestNotif': 20,
        'notificacao': 'Detalhes da notificação',
        'dataNotificacao': '2023-10-01',
        'dataLeitura': '2023-10-02',
        'dataOcultacao': '2023-10-03',
        'exibirRelatorio': true,
        'indicacaoClinicaGuia': 'Indicação Teste',
        'justificativaGuia': 'Justificativa Teste',
        'codCid': 'CID10',
        'codHospital': 300,
        'quantidadeDiarias': 5,
        'codRegimeInternacao': 2,
        'codTipoInternacao': 1,
        'possuiPendencia': true,
      };

      final model = OfficeNoticeModel.fromJson(json);

      expect(model.codNotificacao, 123);
      expect(model.codUnimed, 1);
      expect(model.codPrestador, 2);
      expect(model.codPreSolic, 3);
      expect(model.nomeBeneficiario, 'Beneficiário Teste');
      expect(model.unimedCarteira, 456);
      expect(model.codCarteira, 789);
      expect(model.dvCarteira, 'A');
      expect(model.codTipoNotificacao, 10);
      expect(model.descTipoNotificacao, 'Notificação Teste');
      expect(model.codTipoDestNotif, 20);
      expect(model.notificacao, 'Detalhes da notificação');
      expect(model.dataNotificacao, '2023-10-01');
      expect(model.dataLeitura, '2023-10-02');
      expect(model.dataOcultacao, '2023-10-03');
      expect(model.exibirRelatorio, true);
      expect(model.indicacaoClinicaGuia, 'Indicação Teste');
      expect(model.justificativaGuia, 'Justificativa Teste');
      expect(model.codCid, 'CID10');
      expect(model.codHospital, 300);
      expect(model.quantidadeDiarias, 5);
      expect(model.codRegimeInternacao, 2);
      expect(model.codTipoInternacao, 1);
      expect(model.possuiPendencia, true);
    });

    test('Deve serializar corretamente para JSON', () {
      final model = OfficeNoticeModel(
        codNotificacao: 123,
        codUnimed: 1,
        codPrestador: 2,
        codPreSolic: 3,
        nomeBeneficiario: 'Beneficiário Teste',
        unimedCarteira: 456,
        codCarteira: 789,
        dvCarteira: 'A',
        codTipoNotificacao: 10,
        descTipoNotificacao: 'Notificação Teste',
        codTipoDestNotif: 20,
        notificacao: 'Detalhes da notificação',
        dataNotificacao: '2023-10-01',
        dataLeitura: '2023-10-02',
        dataOcultacao: '2023-10-03',
        exibirRelatorio: true,
        indicacaoClinicaGuia: 'Indicação Teste',
        justificativaGuia: 'Justificativa Teste',
        codCid: 'CID10',
        codHospital: 300,
        quantidadeDiarias: 5,
        codRegimeInternacao: 2,
        codTipoInternacao: 1,
        possuiPendencia: true,
      );

      final json = model.toJson();

      expect(json['codNotificacao'], 123);
      expect(json['codUnimed'], 1);
      expect(json['codPrestador'], 2);
      expect(json['codPreSolic'], 3);
      expect(json['nomeBeneficiario'], 'Beneficiário Teste');
      expect(json['unimedCarteira'], 456);
      expect(json['codCarteira'], 789);
      expect(json['dvCarteira'], 'A');
      expect(json['codTipoNotificacao'], 10);
      expect(json['descTipoNotificacao'], 'Notificação Teste');
      expect(json['codTipoDestNotif'], 20);
      expect(json['notificacao'], 'Detalhes da notificação');
      expect(json['dataNotificacao'], '2023-10-01');
      expect(json['dataLeitura'], '2023-10-02');
      expect(json['dataOcultacao'], '2023-10-03');
      expect(json['exibirRelatorio'], true);
      expect(json['indicacaoClinicaGuia'], 'Indicação Teste');
      expect(json['justificativaGuia'], 'Justificativa Teste');
      expect(json['codCid'], 'CID10');
      expect(json['codHospital'], 300);
      expect(json['quantidadeDiarias'], 5);
      expect(json['codRegimeInternacao'], 2);
      expect(json['codTipoInternacao'], 1);
      expect(json['possuiPendencia'], true);
    });
  });
}
