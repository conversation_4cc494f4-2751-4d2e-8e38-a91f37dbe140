const unimedPrestador = 63;
const idCounsil = 'CONSELHO_FISCAL';
const diretoria = 'DIRETORIA';
const servicos = 'SERVICOS';
const transparencia = 'TRANSPARENCIA';

const cleiton = 'Personal Assistente Digital';

abstract class TipoRetornoFaturas {
  static const int pdf = 1;
  static const int codigoDeBarras = 2;
}

abstract class FaturaListaSituacao {
  static const int naoPadoDCO = -2;
  static const int chequeSemFundos = -1;
  static const int emAberto = 0;
  static const int pago = 1;
  static const int cancelado = 2;
}
