import 'package:intl/intl.dart';

class ResLabExamDetailModel {
  late String material;
  late String resultScheduledDate;
  late String collectionDate;
  late String samplePendency;
  late String testId;
  late String testName;
  String? testPdfUrl;
  late String testStatus;

  String get dateCollectionFormatted {
    return DateFormat('dd/MM/yyyy').format(DateTime.parse(collectionDate));
  }

  String get dateScheduledFormatted {
    return DateFormat('dd/MM/yyyy').format(DateTime.parse(resultScheduledDate));
  }

  ResLabExamDetailModel(
      {required this.material,
      required this.resultScheduledDate,
      required this.collectionDate,
      required this.samplePendency,
      required this.testId,
      required this.testName,
      this.testPdfUrl,
      required this.testStatus});

  ResLabExamDetailModel.fromJson(Map<String, dynamic> json) {
    material = json['material'];
    resultScheduledDate = json['resultScheduledDate'];
    collectionDate = json['collectionDate'];
    samplePendency = json['samplePendency'];
    testId = json['testId'];
    testName = json['testName'];
    testPdfUrl = json['testPdfUrl'];
    testStatus = json['testStatus'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['material'] = material;
    data['resultScheduledDate'] = resultScheduledDate;
    data['collectionDate'] = collectionDate;
    data['samplePendency'] = samplePendency;
    data['testId'] = testId;
    data['testName'] = testName;
    data['testPdfUrl'] = testPdfUrl;
    data['testStatus'] = testStatus;
    return data;
  }
}
