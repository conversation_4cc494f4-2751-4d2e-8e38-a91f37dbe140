import 'package:cooperado_minha_unimed/colors.dart';
import 'package:evaluation/colors.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class SearchTextField extends StatelessWidget {
  final TextEditingController controller;
  final ValueChanged<String> onChanged;
  final String hintText;
  final Icon? prefixIcon;
  final VoidCallback onClearPressed;
  final List<TextInputFormatter>? inputFormatters;

  const SearchTextField({
    super.key,
    required this.controller,
    required this.onChanged,
    required this.onClearPressed,
    this.hintText = '',
    this.prefixIcon,
    this.inputFormatters,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.all(
          Radius.circular(24),
        ),
      ),
      child: TextFormField(
        enableInteractiveSelection: false,
        autofocus: false,
        controller: controller,
        textInputAction: TextInputAction.search,
        onChanged: onChanged,
        maxLines: 1,
        inputFormatters: inputFormatters,
        keyboardType: TextInputType.number,
        style: const TextStyle(
          color: UnimedColors.discoveryOverlay,
          fontSize: 14,
          fontWeight: FontWeight.w400,
        ),
        decoration: InputDecoration(
          hintText: hintText,
          hintStyle: const TextStyle(
            color: CooperadoColors.opcionalGray3,
            fontSize: 14,
            fontWeight: FontWeight.w400,
          ),
          hintMaxLines: null,
          suffixIcon: IconButton(
            onPressed: onClearPressed,
            icon: const Icon(
              Icons.clear,
              size: 16,
            ),
          ),
          prefixIcon: prefixIcon ??
              const Icon(
                Icons.search,
                size: 24,
                color: UnimedColors.greenDark,
              ),
          contentPadding: const EdgeInsets.only(
            top: 12,
            left: 10,
            right: 10,
            bottom: 6,
          ),
          border: InputBorder.none,
        ),
      ),
    );
  }
}
