import 'package:bloc/bloc.dart';
import 'package:cooperado_minha_unimed/shared/api/notice.api.dart';
import 'package:cooperado_minha_unimed/shared/locator.dart';
import 'package:cooperado_minha_unimed/shared/vo/transparencia/demostrativo_resultado.dart';
import 'package:equatable/equatable.dart';

part 'news_state.dart';

class NewsCubit extends Cubit<NewsState> {
  NewsCubit() : super(NewsInitial());
  List<Noticia> _listAllNews = [];
  List<Noticia> _listAllNewsSearch = [];
  int _pageCurrent = 0;

  getListNewsEvent({
    required List<String> categories,
    required int page,
  }) async {
    try {
      emit(LoadingGetListNewsState());
      final List<Noticia> listPortal = await Locator.instance!<NoticeApi>()
          .getNoticeByType(categories: categories, page: page);
      emit(DoneGetListNewsState(list: listPortal));
    } catch (ex) {
      emit(ErrorGetListNewsState('$ex'));
    }
  }

  getListNewsEventPagination(
      {required List<String> categories, required int page}) async {
    try {
      if (page == 1) {
        _clearNews();
        emit(LoadingGetListNewsState());
      } else {
        emit(LoadingGetListNewsStatePagination(list: _listAllNews));
      }
      if (page > _pageCurrent) {
        final List<Noticia> listPortal = await Locator.instance!<NoticeApi>()
            .getNoticeByType(categories: categories, page: page);
        _addItensPagination(newsResponse: listPortal, page: page);
        _pageCurrent = page;
      }

      emit(DoneGetListNewsState(list: _listAllNews));
    } catch (ex) {
      emit(ErrorGetListNewsState('$ex'));
    }
  }

  getListNewsSearch({String search = ''}) async {
    try {
      emit(LoadingGetListNewsStatePagination(list: _listAllNewsSearch));

      _searchNewsEvent(search: search);
      await Future.delayed(const Duration(milliseconds: 100));
      emit(DoneGetListNewsState(list: _listAllNewsSearch));
    } catch (ex) {
      emit(ErrorGetListNewsState('$ex'));
    }
  }

  _clearNews() {
    _listAllNewsSearch = [];
    _listAllNews = [];
    _pageCurrent = 0;
  }

  _addItensPagination(
      {required List<Noticia> newsResponse, required int page}) {
    if (page > 1) {
      _listAllNews.addAll(newsResponse);
    } else {
      _listAllNews.clear();
      _listAllNews.addAll(newsResponse);
    }
  }

  _searchNewsEvent({required String search}) {
    _listAllNewsSearch.clear();
    if (search.isEmpty) {
      _listAllNewsSearch.addAll(_listAllNews);
    } else {
      for (Noticia element in _listAllNews) {
        if (element.titulo!.toUpperCase().contains(search.toUpperCase())) {
          _listAllNewsSearch.add(element);
        }
      }
    }
  }
}
