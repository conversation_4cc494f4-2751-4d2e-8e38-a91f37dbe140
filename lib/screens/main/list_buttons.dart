// ignore_for_file: use_build_context_synchronously

import 'dart:math';

import 'package:cooperado_minha_unimed/bloc/auth/auth_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/beneficiary/beneficiary_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/profile/profile_cubit.dart';
import 'package:cooperado_minha_unimed/colors.dart';
import 'package:cooperado_minha_unimed/models/general_config_model.dart';
import 'package:cooperado_minha_unimed/screens/apoio-cooperado/cooperative_support.dart';
import 'package:cooperado_minha_unimed/screens/club-more-benefits/main.dart';
import 'package:cooperado_minha_unimed/screens/consent-terms/main.dart';
import 'package:cooperado_minha_unimed/screens/directorship/main.dart';
import 'package:cooperado_minha_unimed/screens/e-card/ecard_screen.dart';
import 'package:cooperado_minha_unimed/screens/economic-indicators/main.dart';
import 'package:cooperado_minha_unimed/screens/financial/faturas.dart';
import 'package:cooperado_minha_unimed/screens/fiscal-council/main.dart';
import 'package:cooperado_minha_unimed/screens/indicadores/main.dart';
import 'package:cooperado_minha_unimed/screens/insurance-for-you/insurance_screen.dart';
import 'package:cooperado_minha_unimed/screens/main/button_home_card.dart';
import 'package:cooperado_minha_unimed/screens/medical-production/main.dart';
import 'package:cooperado_minha_unimed/screens/news/main.dart';
import 'package:cooperado_minha_unimed/screens/office-notices/main.dart';
import 'package:cooperado_minha_unimed/screens/res-internal/main.dart';
import 'package:cooperado_minha_unimed/screens/res/journey_clinic_screen.dart';
import 'package:cooperado_minha_unimed/screens/servicos/main.dart';
import 'package:cooperado_minha_unimed/screens/transparencia/main.dart';
import 'package:cooperado_minha_unimed/shared/api/auth.api.dart';
import 'package:cooperado_minha_unimed/shared/api/redirect_buttons.api.dart';
import 'package:cooperado_minha_unimed/shared/constants.dart';
import 'package:cooperado_minha_unimed/shared/flavor_config.dart';
import 'package:cooperado_minha_unimed/shared/locator.dart';
import 'package:cooperado_minha_unimed/shared/screen-transitions/fade.transition.dart';
import 'package:cooperado_minha_unimed/shared/utils/uicons.dart';
import 'package:cooperado_minha_unimed/shared/utils/url_launcher.utils.dart';
import 'package:cooperado_minha_unimed/shared/widgets/alert.dart';
import 'package:cooperado_minha_unimed/shared/widgets/show_up_animation.dart';
import 'package:evaluation/evaluation.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:personal_assistente_digital/models/personal_assistant_evaluation.dart';
import 'package:personal_assistente_digital/models/user.model.dart';
import 'package:personal_assistente_digital/personal_assistente_digital.dart';
import 'package:remote_log_elastic/remote_log_elastic.dart';
import 'package:url_launcher/url_launcher.dart';

class ListButtons extends StatefulWidget {
  final FirebaseAnalytics analytics;
  final FirebaseAnalyticsObserver observer;

  const ListButtons({
    super.key,
    required this.analytics,
    required this.observer,
  });

  @override
  ListButtonsState createState() => ListButtonsState();
}

class ListButtonsState extends State<ListButtons>
    with SingleTickerProviderStateMixin {
  late AnimationController _arrowAnimationController;
  late Animation _arrowAnimation;

  double _animatedHeight = 0.0;
  bool _expandedMode = false;

  late Buttons _configHomeButton;

  late bool _visibleBackgroundButtons;

  late bool _visibleButtonExpanded;

  List<Widget> listButton = [];

  @override
  void initState() {
    _configHomeButton =
        context.read<AuthCubit>().modelGeneralConfigModel.home.buttons;

    _createListButton(context);

    _visibleBackgroundButtons = context
        .read<AuthCubit>()
        .modelGeneralConfigModel
        .home
        .buttons
        .thereIsAnInvisibleButton();

    _visibleButtonExpanded = context
        .read<AuthCubit>()
        .modelGeneralConfigModel
        .home
        .buttons
        .buttonListExpandedButtonVisibility();

    _arrowAnimationController = AnimationController(
        vsync: this, duration: const Duration(milliseconds: 300));
    _arrowAnimation =
        Tween(begin: 0.0, end: pi).animate(_arrowAnimationController);

    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Visibility(
      visible: _visibleBackgroundButtons,
      child: Column(
        children: <Widget>[
          GridView.count(
            primary: true,
            physics: const NeverScrollableScrollPhysics(),
            shrinkWrap: true,
            scrollDirection: Axis.vertical,
            crossAxisCount: 3,
            padding: const EdgeInsets.only(bottom: 8, left: 8, right: 8),
            children: [
              ...listButton.take(6).map((item) {
                return item;
              }),
              if (_expandedMode) ...listButton.skip(6),
            ],
          ),
          Visibility(
            visible: _visibleButtonExpanded,
            child: GestureDetector(
              onTap: () => setState(() {
                _arrowAnimationController.isCompleted
                    ? _arrowAnimationController.reverse()
                    : _arrowAnimationController.forward();
                _animatedHeight != 0.0
                    ? _animatedHeight = 0.0
                    : _animatedHeight = 150;
                _expandedMode = !_expandedMode;
              }),
              child: AnimatedBuilder(
                animation: _arrowAnimationController,
                builder: (context, child) => Transform.rotate(
                  angle: _arrowAnimation.value,
                  child: const Icon(
                    Icons.expand_more,
                    size: 40,
                    color: CooperadoColors.tealGreen,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _createListButton(BuildContext context) {
    if (_configHomeButton.insurance) {
      listButton.add(
        ShowUp(
          delay: 350,
          duration: 500,
          child: ButtonHome(
            text: 'Seguros',
            subtitle: 'Proteja seu patrimônio',
            pathIcon: 'assets/svg/icon_insecure.svg',
            bold: true,
            backgroundColor: CooperadoColors.tealGreen,
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const InsuranceScreen(),
                ),
              );
            },
          ),
        ),
      );
    }

    if (_configHomeButton.clubMaisVantagens) {
      listButton.add(
        ShowUp(
          delay: 350,
          duration: 500,
          child: ButtonHome(
            pathIcon: 'assets/svg/icon_clube_mais_vantagens.svg',
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => ClubMoreBenefitsScreen(
                    analytics: widget.analytics,
                    observer: widget.observer,
                  ),
                ),
              );
            },
          ),
        ),
      );
    }
    if (_configHomeButton.financialServiceUniverse) {
      listButton.add(
        ShowUp(
          delay: 350,
          duration: 500,
          child: ButtonHome(
            text: 'Antecipa',
            subtitle: 'Antecipe sua produção',
            bold: true,
            pathIcon: 'assets/svg/antecipa.svg',
            onPressed: () {
              if (context
                      .read<AuthCubit>()
                      .modelGeneralConfigModel
                      .links
                      ?.antecipa !=
                  null) {
                UrlLaucherUtils.launchURL(context
                        .read<AuthCubit>()
                        .modelGeneralConfigModel
                        .links
                        ?.antecipa ??
                    "");
              } else {
                showDialog(
                  context: context,
                  builder: (BuildContext context) {
                    return AlertDialog(
                      title: const Text(
                        'Universo Soluções Financeiras',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 18,
                        ),
                      ),
                      content: const Text(
                        'Essa funcionalidade estará disponível em breve.',
                        style: TextStyle(fontSize: 16),
                      ),
                      actions: [
                        ElevatedButton(
                          onPressed: () {
                            Navigator.of(context).pop();
                          },
                          style: ElevatedButton.styleFrom(
                            backgroundColor:
                                const Color(0xFF0F4C47), // Verde escuro Unimed
                            foregroundColor: Colors.white, // Texto branco
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                            elevation: 2,
                            padding: const EdgeInsets.symmetric(
                                horizontal: 16, vertical: 8),
                          ),
                          child: const Text(
                            'Ok',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        )
                      ],
                    );
                  },
                );
              }
              //TODO - COMENTADO PARA QUANDO TIVER O TERMO REDIRECIONAR PARA O LINK
              // Navigator.push(
              //   context,
              //   MaterialPageRoute(
              //     builder: (context) => const FinancialServiceScreen(),
              //   ),
              // );
            },
          ),
        ),
      );
    }

    if (_configHomeButton.onlineConsultation) {
      listButton.add(
        ShowUp(
          delay: 350,
          duration: 500,
          child: ButtonHome(
            text: 'Consultório Online',
            icomoon: Icons.dvr,
            backgroundColor: CooperadoColors.tealGreen,
            onPressed: () {
              Locator.instance!.get<RedirectButtonsApi>().goToConsultorioOnline(
                    context,
                    analytics: widget.analytics,
                    observer: widget.observer,
                  );
            },
          ),
        ),
      );
    }

    if (_configHomeButton.medicalProduction) {
      listButton.add(
        Visibility(
          visible: _configHomeButton.medicalProduction,
          child: ShowUp(
            delay: 350,
            duration: 500,
            child: ButtonHome(
              text: 'Produção Médica',
              icomoon: Icons.assessment,
              backgroundColor: CooperadoColors.tealGreen,
              onPressed: () => Navigator.push(
                context,
                FadeRoute(
                  page: MedicalProductionScreen(
                    analytics: widget.analytics,
                    observer: widget.observer,
                  ),
                ),
              ),
            ),
          ),
        ),
      );
    }

    if (_configHomeButton.supportCooperative) {
      listButton.add(
        ShowUp(
          delay: 350,
          duration: 500,
          child: ButtonHome(
            text: 'Apoio ao Cooperado',
            pathIcon: 'assets/svg/apoio-cooperado.svg',
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => SupportCooperativeScreen(
                    analytics: widget.analytics,
                    observer: widget.observer,
                  ),
                ),
              );
            },
          ),
        ),
      );
    }

    if (_configHomeButton.ecard) {
      listButton.add(
        ShowUp(
          delay: 350,
          duration: 500,
          child: ButtonHome(
            text: 'E-card',
            icomoon: Icons.contact_mail_outlined,
            backgroundColor: CooperadoColors.tealGreen,
            onPressed: () {
              Navigator.push(
                context,
                FadeRoute(
                  page: EcardScreen(
                    analytics: widget.analytics,
                    observer: widget.observer,
                  ),
                ),
              );
            },
          ),
        ),
      );
    }

    if (_configHomeButton.res.resExternal.resExternal ||
        _configHomeButton.res.resInternal) {
      listButton.add(
        ShowUp(
          delay: 350,
          duration: 500,
          child: ButtonHome(
            text: 'Histórico de Saúde (RES)',
            pathIcon: 'assets/svg/stethoscope.svg',
            backgroundColor: CooperadoColors.tealGreen,
            onPressed: () {
              if (_configHomeButton.res.resExternal.resExternal &&
                  _configHomeButton.res.resInternal) {
                _showOptionsModalRes(configHomeButton: _configHomeButton);
              } else if (_configHomeButton.res.resInternal) {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const ResInternalScreen(),
                  ),
                );
              } else {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const JourneyClinicScreen(),
                  ),
                );
              }
            },
          ),
        ),
      );
    }

    if (_configHomeButton.services) {
      listButton.add(
        ShowUp(
          delay: 350,
          duration: 500,
          child: ButtonHome(
            text: 'Serviços',
            icomoon: Icons.phonelink_setup,
            backgroundColor: CooperadoColors.tealGreen,
            onPressed: () {
              Navigator.push(
                context,
                FadeRoute(
                  page: ServicesScreen(
                    analytics: widget.analytics,
                    observer: widget.observer,
                  ),
                ),
              );
            },
          ),
        ),
      );
    }

    if (_configHomeButton.transparency) {
      listButton.add(
        ShowUp(
          delay: 350,
          duration: 500,
          child: ButtonHome(
            text: 'Transparência',
            icomoon: Icons.computer,
            backgroundColor: CooperadoColors.tealGreen,
            onPressed: () {
              Navigator.push(
                context,
                FadeRoute(
                  page: TransparenciaScreen(
                    analytics: widget.analytics,
                    observer: widget.observer,
                  ),
                ),
              );
            },
          ),
        ),
      );
    }

    if (_configHomeButton.terms) {
      listButton.add(
        ShowUp(
          delay: 350,
          duration: 500,
          child: ButtonHome(
            text: 'Termos',
            icomoon: Icons.assignment,
            backgroundColor: CooperadoColors.tealGreen,
            onPressed: () {
              Navigator.push(
                context,
                FadeRoute(
                  page: ConsentTermsScreen(
                    analytics: widget.analytics,
                    observer: widget.observer,
                  ),
                ),
              );
            },
          ),
        ),
      );
    }

    if (_configHomeButton.guide) {
      listButton.add(
        ShowUp(
          delay: 350,
          duration: 500,
          child: ButtonHome(
            text: 'Guia',
            icomoon: Icons.timeline,
            backgroundColor: CooperadoColors.tealGreen,
            onPressed: () {
              Locator.instance!.get<RedirectButtonsApi>().goToGuia(
                    context,
                    analytics: widget.analytics,
                    observer: widget.observer,
                  );
            },
          ),
        ),
      );
    }

    if (_configHomeButton.direction) {
      listButton.add(
        ShowUp(
          delay: 350,
          duration: 500,
          child: ButtonHome(
            text: 'Diretoria',
            icomoon: UIcons.group,
            backgroundColor: CooperadoColors.tealGreen,
            onPressed: () {
              Navigator.push(
                context,
                FadeRoute(
                  page: DiretoriaScreen(
                    analytics: widget.analytics,
                    observer: widget.observer,
                  ),
                ),
              );
            },
          ),
        ),
      );
    }

    if (_configHomeButton.fiscalCouncil) {
      listButton.add(
        ShowUp(
          delay: 350,
          duration: 500,
          child: ButtonHome(
            text: 'Conselho Fiscal',
            icomoon: UIcons.conselhoFiscal,
            backgroundColor: CooperadoColors.tealGreen,
            onPressed: () {
              Navigator.push(
                context,
                FadeRoute(
                  page: FiscalCouncilScreen(
                    analytics: widget.analytics,
                    observer: widget.observer,
                  ),
                ),
              );
            },
          ),
        ),
      );
    }

    if (_configHomeButton.myIndicators) {
      listButton.add(
        ShowUp(
          delay: 350,
          duration: 500,
          child: ButtonHome(
            text: 'Meus Indicadores',
            icomoon: Icons.donut_large,
            backgroundColor: CooperadoColors.tealGreen,
            onPressed: () {
              Navigator.push(
                context,
                FadeRoute(
                  page: IndicadoresScreen(
                    analytics: widget.analytics,
                    observer: widget.observer,
                  ),
                ),
              );
            },
          ),
        ),
      );
    }

    if (_configHomeButton.news) {
      listButton.add(
        ShowUp(
          delay: 350,
          duration: 500,
          child: ButtonHome(
            text: 'Notícias',
            icomoon: Icons.comment,
            backgroundColor: CooperadoColors.tealGreen,
            onPressed: () => Navigator.push(
              context,
              FadeRoute(
                page: NewsScreen(
                  analytics: widget.analytics,
                  observer: widget.observer,
                ),
              ),
            ),
          ),
        ),
      );
    }

    if (_configHomeButton.personalAssistant) {
      listButton.add(
        ShowUp(
          delay: 350,
          duration: 500,
          child: ButtonHome(
              text: cleiton,
              icomoon: UIcons.personalAssistant,
              backgroundColor: CooperadoColors.tealGreen,
              onPressed: () async {
                String token =
                    await (Locator.instance!.get<AuthApi>().tokenPerfilApps());
                Navigator.push(
                  context,
                  FadeRoute(
                    page: PersonalAssistenteDigital(
                      baseUrl:
                          FlavorConfig.instance?.values.personalAssistant.url ??
                              "",
                      user: UserPersonalDigital(
                          login:
                              context.read<ProfileCubit>().user.crmOnlyNumbers,
                          nome: context.read<ProfileCubit>().user.nome ?? "",
                          email: context.read<ProfileCubit>().user.email,
                          password:
                              context.read<AuthCubit>().credentials.password ??
                                  ""),
                      remoteLog: Locator.instance!<RemoteLog>(),
                      personalAssistenteEvaluation:
                          PersonalAssistenteEvaluation(
                        perfilAppsUrl: FlavorConfig
                                .instance?.values.profilePermissions.url ??
                            "",
                        tokenPerfilApps: token,
                        evaluation: Evaluation(
                            showLogs: true,
                            environment:
                                FlavorConfig.instance!.values.evaluationEnv,
                            userId: context
                                .read<ProfileCubit>()
                                .user
                                .crmOnlyNumbers,
                            username:
                                context.read<ProfileCubit>().user.nome ?? ""),
                      ),
                      analytics: widget.analytics,
                      observer: widget.observer,
                    ),
                  ),
                );
              }),
        ),
      );
    }
    if (_configHomeButton.economicIndicators) {
      listButton.add(
        ShowUp(
          delay: 350,
          duration: 500,
          child: ButtonHome(
            text: 'Indicadores Econômicos',
            icomoon: Icons.personal_video,
            backgroundColor: CooperadoColors.tealGreen,
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => EconomicIndicatorsScreen(
                    analytics: widget.analytics,
                    observer: widget.observer,
                  ),
                ),
              );
            },
          ),
        ),
      );
    }
    if (_configHomeButton.financial) {
      listButton.add(
        BlocConsumer<BeneficiaryCubit, BeneficiaryState>(
          listener: (context, state) {
            if (state is LoadedBeneficiaryState) {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const FaturasScreen(),
                ),
              );
            } else if (state is ErrorBeneficiaryState) {
              showDialog(
                context: context,
                builder: (context) => CooperadoAlertDialog(
                  textWidget: Text(state.message, textAlign: TextAlign.center),
                  onPressed: () => Navigator.of(context).pop(),
                ),
              );
            }
          },
          builder: (context, state) {
            return ShowUp(
              delay: 350,
              duration: 500,
              child: ButtonHome(
                text: 'Minhas Faturas',
                icomoon: UIcons.myInvoices,
                backgroundColor: CooperadoColors.tealGreen,
                loading: state is LoadingBeneficiaryState,
                onPressed: () {
                  BlocProvider.of<BeneficiaryCubit>(context).getCardsData(
                    cpf: BlocProvider.of<AuthCubit>(context)
                        .userLogged
                        .cpf
                        .toString(),
                  );
                },
              ),
            );
          },
        ),
      );
    }

    if (_configHomeButton.benefits) {
      listButton.add(
        ShowUp(
          delay: 350,
          duration: 500,
          child: ButtonHome(
            text: 'Benefícios',
            icomoon: Icons.download_done_sharp,
            backgroundColor: CooperadoColors.tealGreen,
            onPressed: () => _showOptionsModal(),
          ),
        ),
      );
    }

    if (_configHomeButton.officeNotices) {
      listButton.add(
        ShowUp(
          delay: 350,
          duration: 500,
          child: ButtonHome(
            text: 'Avisos do Consultório',
            icomoon: Icons.warning_amber_rounded,
            backgroundColor: CooperadoColors.tealGreen,
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const OfficeNoticesScreen(),
                ),
              );
            },
          ),
        ),
      );
    }
  }

  void _showOptionsModalRes({required Buttons configHomeButton}) {
    showModalBottomSheet<void>(
      context: context,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(15)),
      builder: (BuildContext context) {
        return Padding(
          padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Align(
                alignment: Alignment.topRight,
                child: IconButton(
                  color: CooperadoColors.tealGreen,
                  icon: const Icon(Icons.close),
                  onPressed: () => Navigator.pop(context),
                ),
              ),
              Visibility(
                visible: configHomeButton.res.resInternal,
                child: _optionTile(
                  icon: Icons.browser_updated_outlined,
                  label: 'Histórico de Saúde interna',
                  onPressed: () {
                    _close(context);
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const ResInternalScreen(),
                      ),
                    );
                  },
                ),
              ),
              Visibility(
                visible: configHomeButton.res.resExternal.resExternal,
                child: _optionTile(
                  icon: Icons.sticky_note_2_outlined,
                  label: 'Histórico de Saúde (RES)',
                  onPressed: () {
                    _close(context);
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const JourneyClinicScreen(),
                      ),
                    );
                  },
                ),
              ),
              const SizedBox(height: 30),
            ],
          ),
        );
      },
    );
  }

  _showOptionsModal() {
    showModalBottomSheet<void>(
      context: context,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(15)),
      builder: (BuildContext context) {
        return Padding(
          padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Align(
                alignment: Alignment.topRight,
                child: IconButton(
                  color: CooperadoColors.tealGreen,
                  icon: const Icon(Icons.close),
                  onPressed: () => Navigator.pop(context),
                ),
              ),
              _optionTile(
                icon: Icons.browser_updated_outlined,
                label: 'Plataforma Mais Unimed',
                onPressed: () {
                  _close(context);
                  _launchURL(BlocProvider.of<AuthCubit>(context)
                      .modelGeneralConfigModel
                      .links
                      ?.plataformaMaisUnimed);
                },
              ),
              const SizedBox(height: 30),
            ],
          ),
        );
      },
    );
  }

  Future _launchURL(url) async {
    if (await canLaunchUrl(Uri.parse(url))) {
      await launchUrl(Uri.parse(url), mode: LaunchMode.externalApplication);
    } else {
      Alert.open(
        context,
        title: "Falha ao abrir URL externa",
        text: "Nenhum navegador de internet detectado no dispositivo.",
      );
      throw 'Unable to open url : $url';
    }
  }

  void _close(BuildContext context) {
    if (Navigator.canPop(context)) {
      Navigator.pop(context);
    }
  }

  Widget _optionTile({
    required IconData icon,
    required String label,
    required VoidCallback onPressed,
    double iconSize = 24,
  }) {
    return InkWell(
      onTap: onPressed,
      child: Column(
        children: [
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 8.0),
            child: Row(
              children: [
                Icon(icon, color: CooperadoColors.tealGreen, size: iconSize),
                const SizedBox(width: 16.0),
                Expanded(
                    child: Text(
                  label,
                  style: const TextStyle(color: CooperadoColors.tealGreen),
                )),
              ],
            ),
          ),
          const Divider(color: Colors.grey)
        ],
      ),
    );
  }
}
