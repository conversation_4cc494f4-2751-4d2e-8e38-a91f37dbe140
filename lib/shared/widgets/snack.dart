import 'package:flutter/material.dart';

class Snack {
  static SnackBar error(String message) {
    return SnackBar(
      content: Text(
        message,
        style: const TextStyle(
          color: Colors.white,
          fontWeight: FontWeight.bold,
        ),
      ),
      backgroundColor: Colors.red,
    );
  }

  static SnackBar success(String message) {
    return SnackBar(
      content: Text(
        message,
        style: const TextStyle(
          color: Colors.white,
          fontWeight: FontWeight.bold,
        ),
      ),
      backgroundColor: Colors.green,
    );
  }

  static SnackBar warning(String message, {Duration? duration}) {
    duration ??= const Duration(seconds: 5);

    return SnackBar(
      duration: duration,
      content: Text(
        message,
        style: const TextStyle(
          color: Colors.black,
          fontWeight: FontWeight.bold,
        ),
      ),
      backgroundColor: Colors.yellow,
    );
  }
}
