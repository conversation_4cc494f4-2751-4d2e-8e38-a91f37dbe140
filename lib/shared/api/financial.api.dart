import 'dart:convert';

import 'package:cooperado_minha_unimed/models/invoice.model.dart';
import 'package:cooperado_minha_unimed/shared/api/auth.api.dart';
import 'package:cooperado_minha_unimed/shared/constants.dart';
import 'package:cooperado_minha_unimed/shared/exceptions.dart';
import 'package:cooperado_minha_unimed/shared/flavor_config.dart';
import 'package:http_client/http_client.dart';
import 'package:cooperado_minha_unimed/shared/locator.dart';
import 'package:cooperado_minha_unimed/shared/utils/logger_print.dart';

class FinancialApi {
  final UnimedHttpClient httpClient;

  FinancialApi(this.httpClient);

  final logger = UnimedLogger(className: 'FinancialApi');
  final Duration timeout = const Duration(seconds: 30);

  Future<List<InvoiceModel>> getAllInvoice({
    required String carteira,
  }) async {
    final tokenPerfilApps =
        await Locator.instance!.get<AuthApi>().tokenPerfilApps();

    try {
      final url =
          '${FlavorConfig.instance!.values.profilePermissions.url}app/cooperado/faturas/$carteira/carteira';

      final response = await httpClient.get(
        Uri.parse(url),
        headers: {
          "Content-Type": "application/json",
          "Authorization": "Bearer $tokenPerfilApps",
        },
      );

      final data = jsonDecode(response.body);
      if (response.statusCode == 200) {
        final collection = (data["retorno"] as List)
            .map<InvoiceModel>((e) => InvoiceModel.fromJson(e))
            .toList();

        logger.d('getAllInvoice success list ${collection.length}');
        return collection;
      } else {
        final message = data['message'] ?? 'Não foi possível no momento.';
        logger.e(
            'getAllInvoice statusCode : ${response.statusCode} ${response.body}');
        throw UnimedException('$message');
      }
    } on UnimedException {
      rethrow;
    } catch (ex) {
      logger.e('getAllInvoice exception : $ex');
      throw UnimedException('Não foi possível no momento.');
    }
  }

  Future<String> getInvoice({
    required String carteira,
    required String dataReferencia,
    int? tipoRetorno,
    String? imprimirTodasFaturas,
  }) async {
    final tokenPerfilApps =
        await Locator.instance!.get<AuthApi>().tokenPerfilApps();

    try {
      final url =
          '${FlavorConfig.instance!.values.profilePermissions.url}app/cooperado/fatura/$carteira/carteira/$dataReferencia/data?tipoRetorno=${tipoRetorno.toString()}';

      final response = await httpClient.get(
        Uri.parse(url),
        headers: {
          "Content-Type": "application/json",
          "Authorization": "Bearer $tokenPerfilApps",
        },
      );

      final data = jsonDecode(response.body);
      if (response.statusCode == 200) {
        if (tipoRetorno == TipoRetornoFaturas.pdf) {
          logger.d(
              '$getInvoice base64 generated length ${data['retorno'].length}');
        } else {
          logger.d('$getInvoice reponse => $data');
        }

        return data['retorno'];
      } else {
        final message = data['message'] ?? 'Não foi possível no momento.';
        logger.e(
            'getInvoice statusCode : ${response.statusCode} ${response.body}');
        throw UnimedException('$message');
      }
    } on UnimedException {
      rethrow;
    } catch (ex) {
      logger.e('getInvoice exception : $ex');
      throw UnimedException('Não foi possível no momento.');
    }
  }
}
