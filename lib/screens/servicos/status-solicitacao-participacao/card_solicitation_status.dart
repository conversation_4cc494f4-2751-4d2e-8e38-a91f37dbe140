import 'package:cooperado_minha_unimed/bloc/auth/auth_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/sensitive-data/sensitive_data_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/sensitive-data/sensitive_data_state.dart';
import 'package:cooperado_minha_unimed/bloc/servicos/status-solicitacao-participacao/glose_resource/glosa_resource_bloc.dart';
import 'package:cooperado_minha_unimed/bloc/servicos/status-solicitacao-participacao/participation_solicitation_status_bloc.dart';
import 'package:cooperado_minha_unimed/bloc/servicos/status-solicitacao-participacao/participation_solicitation_status_state.dart';
import 'package:cooperado_minha_unimed/colors.dart';
import 'package:cooperado_minha_unimed/screens/servicos/status-solicitacao-participacao/details_solicitation_screen.dart';
import 'package:cooperado_minha_unimed/shared/messages.exceptions.dart';
import 'package:cooperado_minha_unimed/shared/screen-transitions/fade.transition.dart';
import 'package:cooperado_minha_unimed/shared/vo/servicos/honorary_solicitation.vo.dart';
import 'package:cooperado_minha_unimed/shared/widgets/alert.dart';
import 'package:cooperado_minha_unimed/shared/widgets/error-banner/error_banner.component.dart';
import 'package:cooperado_minha_unimed/shared/widgets/snack.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';

import 'details_list_screen.dart';

class CardSolicitationParticipationStatus extends StatefulWidget {
  const CardSolicitationParticipationStatus({super.key});

  @override
  CardSolicitationParticipationStatusState createState() =>
      CardSolicitationParticipationStatusState();
}

class CardSolicitationParticipationStatusState
    extends State<CardSolicitationParticipationStatus> {
  @override
  void initState() {
    context
        .read<SolicitationParticipationStatusCubit>()
        .getSolicitationParticipationStatusEvent();

    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<SolicitationParticipationStatusCubit,
        SolicitationParticipationStatusState>(
      builder: (context, state) {
        if (state is DoneGetSolicitationParticipationStatusState) {
          return Card(
            child: Container(
              padding: const EdgeInsets.only(top: 8.0, left: 8.0, right: 8.0),
              child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: <Widget>[
                    _headerCard(context),
                    Text(state.list != null && state.list!.isNotEmpty
                        ? "Solicitações Recentes:"
                        : "Sem solicitações recentes"),
                    const SizedBox(
                      height: 10,
                    ),
                    state.list != null && state.list!.isNotEmpty
                        ? _recentSolicitation(list: state.list?.toList() ?? [])
                        : Container(),
                    const SizedBox(
                      height: 20,
                    ),
                    _buttonDetails(state.list),
                    //SizedBox(height: 1,)
                  ]),
            ),
          );
        } else if (state is LoadingGetSolicitationParticipationStatusState) {
          return Card(
              child: Container(
                  padding: const EdgeInsets.all(8.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: <Widget>[
                      _headerCard(context),
                      const SpinKitCircle(
                        color: CooperadoColors.tealGreen,
                      )
                    ],
                  )));
        } else if (state is ErrorGetSolicitationParticipationStatusState) {
          return Card(
              child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: <Widget>[
              _headerCard(context),
              Center(child: ErrorBanner(message: state.message)),
              const SizedBox(
                height: 20,
              ),
              _buttonDetails([]),
            ],
          ));
        } else {
          return Container();
        }
      },
    );
  }

  Widget _headerCard(context) {
    return const Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: <Widget>[
        Padding(
          padding: EdgeInsets.all(8.0),
          child: Text(
            "Status de Solicitação de Participação",
            style: TextStyle(
                color: CooperadoColors.blackText,
                fontSize: 16,
                fontWeight: FontWeight.bold),
          ),
        ),
        //SizedBox(height: 300, child: ProducaoChart())
      ],
    );
  }

  Widget _recentSolicitation({required List<HonorarySolicitation> list}) {
    const maxRecentDisplayed = 2;
    List<Widget> widgets = [];

    int itensDisplay =
        list.length > maxRecentDisplayed ? maxRecentDisplayed : list.length;

    for (int i = 0; i < itensDisplay; i++) {
      widgets.add(_modelShowInfo(
          list[i],
          list[i].dataSolicitacao ?? '-',
          list[i].participacao != null
              ? list[i].participacao!.descricao ?? '-'
              : '-',
          list[i].situacao != null ? list[i].situacao ?? '-' : '-'));

      widgets.add(const SizedBox(height: 10));
    }

    return Column(
        crossAxisAlignment: CrossAxisAlignment.start, children: widgets);
  }

  Widget _modelShowInfo(HonorarySolicitation? honorarySolicitation, String data,
      String description, String status) {
    return InkWell(
      onTap: () {
        if (honorarySolicitation != null) {
          debugPrint("TEste: ${honorarySolicitation.toJson()}");
          if (honorarySolicitation.isGlosa) {
            context.read<GlosaResourceCubit>().getGlosaResourceEvent(
                crm: context.read<AuthCubit>().credentials.crm,
                guide: honorarySolicitation.guiaReferencia?.toString() ?? '');
          }

          Navigator.push(
            context,
            FadeRoute(
              page: DetailsSocitationScreen(
                solicitation: honorarySolicitation,
              ),
            ),
          );
        } else {
          _showErrorMessage(message: MessageException.general);
        }
      },
      child: Container(
        decoration: const BoxDecoration(
            border: Border(
          left: BorderSide(width: 1.0, color: CooperadoColors.greenLight3),
        )),
        child: Padding(
          padding: const EdgeInsets.only(left: 10),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: <Widget>[
              Text("$data - $description",
                  style: const TextStyle(fontWeight: FontWeight.bold)),
              BlocBuilder<SensitiveDataCubit, SensitiveDataState>(
                builder: (context, sensitiveState) {
                  bool isSensitiveDataVisible =
                      sensitiveState.isSensitiveDataVisible;
                  String displayStatus =
                      isSensitiveDataVisible ? status : '*' * status.length;
                  return Text(
                    "Status: $displayStatus",
                  );
                },
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buttonDetails(Iterable<HonorarySolicitation>? list) {
    return Align(
      child: InkWell(
        onTap: () {
          list == null || list.isEmpty
              ? ScaffoldMessenger.of(context).showSnackBar(
                  Snack.warning(
                    'Nenhum registro encontrado para exibir detalhes',
                    duration: const Duration(seconds: 1),
                  ),
                )
              : Navigator.push(
                  context,
                  FadeRoute(
                      page: DetailsListSocitationsScreen(
                          list: list as List<HonorarySolicitation>)));
        },
        child: Container(
          padding: const EdgeInsets.only(
              top: 12.0, bottom: 12.0, left: 40, right: 40),
          decoration: const BoxDecoration(
              color: CooperadoColors.tealGreen,
              borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(20), topRight: Radius.circular(20))),
          child: const Text(
            "VER MAIS",
            style: TextStyle(
              //fontSize: 18,
              color: Colors.white,
            ),
          ),
        ),
      ),
    );
  }

  void _showErrorMessage({required String message}) {
    Alert.open(context, title: "Atenção", text: message);
  }
}
