import 'package:cooperado_minha_unimed/models/indicadores.model.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  RetornoHistoricoProdMed? modelTest;
  RetornoComparativoAtendimento? modelTest2;
  Map? json;
  Map? json2;

  setUpAll(
    () {
      modelTest = RetornoHistoricoProdMed(
        ano: 2021,
        meses: [
          Meses(
              mes: 1,
              producoes: [
                Producoes(
                  descricao: 'descricao',
                  tipoProducao: 'tipoProducao',
                  dataProducao: '21/09/2021',
                  dataProducaoFim: '30/09/2021',
                  dataAtendimento: "01/10/2021",
                  valor: 100.00,
                )
              ],
              total: 1.0),
        ],
      );

      modelTest2 = RetornoComparativoAtendimento(
          dataFim: '30/09/2021',
          dataInicio: '10/09/2021',
          atendimentosEspecialidade: [
            AtendimentosEspecialidade(
              mes: 1,
              ano: 2021,
              procedimentos: [
                Procedimentos(
                  quantidade: 1,
                  tipo: Tipo(descricao: "descricao"),
                )
              ],
              media: 1,
              total: 1,
            )
          ],
          atendimentosPrestador: [
            AtendimentosPrestador(
              mes: 1,
              ano: 2021,
              procedimentos: [
                Procedimentos(
                  quantidade: 1,
                  tipo: Tipo(descricao: "descricao"),
                )
              ],
              media: 1,
              total: 1,
            )
          ]);

      json = {
        "ano": 2022,
        "meses": [
          {
            "mes": 2,
            "producoes": [
              {
                "descricao": null,
                "tipoProducao": "D",
                "dataProducao": null,
                "dataProducaoFim": null,
                "dataAtendimento": null,
                "valor": 0.0
              }
            ],
            "total": 0.0
          },
          {
            "mes": 3,
            "producoes": [
              {
                "descricao": null,
                "tipoProducao": "D",
                "dataProducao": null,
                "dataProducaoFim": null,
                "dataAtendimento": null,
                "valor": 0.0
              }
            ],
            "total": 0.0
          }
        ]
      };
      json2 = {
        "dataInicio": "01/10/2021",
        "dataFim": "01/11/2021",
        "atendimentosPrestador": [],
        "atendimentosEspecialidade": [
          {
            "mes": 10,
            "ano": 2021,
            "prestador": {
              "codigo": null,
              "codigoUnimed": null,
              "nome": null,
              "nomeFantasia": null,
              "suspensaoCovid": false,
              "enderecos": [],
              "especialidades": [
                {
                  "codigo": null,
                  "descricao": "GINECOLOGIA E OBSTETRÍCIA",
                  "qualificacoes": null,
                  "qualificacao": null,
                  "especialidadePrincipal": null
                }
              ],
              "produtosAtendidos": null,
              "qualificacoes": null,
              "grupoPrestador": null,
              "mensagens": null,
              "crm": null,
              "cpf": null,
              "dataNascimento": null,
              "usaApp": null,
              "especialidadePrincipal": null
            },
            "procedimentos": [
              {"valor": 717.39},
              {"valor": 2871.96},
              {"valor": 1228.3},
              {"valor": 1002.81},
              {"valor": 1481.93},
              {"valor": 1488.9}
            ],
            "media": 4204.28333870968,
            "total": 2606655.6700000013
          }
        ]
      };
    },
  );

  group(
    "isInstanceOf RetornoHistoricoProdMed model tests",
    () {
      test("Should be return instance of RetornoHistoricoProdMed", () {
        expect(modelTest, isInstanceOf<RetornoHistoricoProdMed>());
      });

      test("Should be return instance of Int", () {
        expect(modelTest!.ano, isInstanceOf<int>());
      });

      test("Should be return instance of List<Meses>", () {
        expect(modelTest!.meses, isInstanceOf<List<Meses>>());
      });

      test("Should be return instance of Meses", () {
        expect(modelTest!.meses![0], isInstanceOf<Meses>());
      });
      test("Should be return instance of Meses int", () {
        expect(modelTest!.meses![0].mes, isInstanceOf<int>());
      });

      test("Should be return instance of Meses double", () {
        expect(modelTest!.meses![0].total, isInstanceOf<double>());
      });

      test("Should be return instance of Meses List<Producoes>", () {
        expect(modelTest!.meses![0].producoes, isInstanceOf<List<Producoes>>());
      });
      test("Should be return instance of Meses Producoes", () {
        expect(modelTest!.meses![0].producoes![0], isInstanceOf<Producoes>());
      });

      test("Should be return instance of Meses Producoes double", () {
        expect(
            modelTest!.meses![0].producoes![0].valor, isInstanceOf<double>());
      });

      test("Should be return instance of Meses Producoes String", () {
        expect(modelTest!.meses![0].producoes![0].dataAtendimento,
            isInstanceOf<String>());
      });
    },
  );

  group("Json test", () {
    test("Should be return instance of RetornoHistoricoProdMed to json", () {
      expect(modelTest!.toJson(), isInstanceOf<Map<dynamic, dynamic>>());
    });
    test("Should be return instance of RetornoHistoricoProdMed from json", () {
      final json = modelTest!.toJson();
      expect(RetornoHistoricoProdMed.fromJson(json),
          isInstanceOf<RetornoHistoricoProdMed>());
    });

    test("Should be return instance of Meses to json", () {
      expect(
          modelTest!.meses![0].toJson(), isInstanceOf<Map<dynamic, dynamic>>());
    });
    test("Should be return instance of Meses from json", () {
      final json = modelTest!.meses![0].toJson();
      expect(Meses.fromJson(json), isInstanceOf<Meses>());
    });
    test("Should be return instance of Producoes to json", () {
      expect(modelTest!.meses![0].producoes![0].toJson(),
          isInstanceOf<Map<dynamic, dynamic>>());
    });
    test("Should be return instance of Producoes from json", () {
      final json = modelTest!.meses![0].producoes![0].toJson();
      expect(Producoes.fromJson(json), isInstanceOf<Producoes>());
    });
  });
  group(
    "Other tests",
    () {
      test("Should be return true", () {
        expect(modelTest!.meses![0].producoes![0].dataProducao!.contains("/"),
            true);
      });
    },
  );

  group(
    "isInstanceOf RetornoComparativoAtendimento model tests",
    () {
      test("Should be return instance of RetornoComparativoAtendimento", () {
        expect(modelTest2, isInstanceOf<RetornoComparativoAtendimento>());
      });

      test("Should be return instance of String", () {
        expect(modelTest2!.dataInicio, isInstanceOf<String>());
      });

      test("Should be return instance of List<AtendimentosEspecialidade>", () {
        expect(modelTest2!.atendimentosEspecialidade,
            isInstanceOf<List<AtendimentosEspecialidade>>());
      });

      test("Should be return instance of AtendimentosEspecialidade", () {
        expect(modelTest2!.atendimentosEspecialidade![0],
            isInstanceOf<AtendimentosEspecialidade>());
      });
      test("Should be return instance of AtendimentosEspecialidade int", () {
        expect(
            modelTest2!.atendimentosEspecialidade![0].mes, isInstanceOf<int>());
      });

      test(
          "Should be return instance of AtendimentosEspecialidade List<Procedimentos>",
          () {
        expect(modelTest2!.atendimentosEspecialidade![0].procedimentos,
            isInstanceOf<List<Procedimentos>>());
      });
      test(
          "Should be return instance of AtendimentosEspecialidade Procedimentos",
          () {
        expect(modelTest2!.atendimentosEspecialidade![0].procedimentos![0],
            isInstanceOf<Procedimentos>());
      });

      test(
          "Should be return instance ofAtendimentosEspecialidade Procedimentos int",
          () {
        expect(
            modelTest2!
                .atendimentosEspecialidade![0].procedimentos![0].quantidade,
            isInstanceOf<int>());
      });

      test(
          "Should be return instance of AtendimentosEspecialidade Procedimentos Tipo",
          () {
        expect(modelTest2!.atendimentosEspecialidade![0].procedimentos![0].tipo,
            isInstanceOf<Tipo>());
      });

      test(
          "Should be return instance of AtendimentosPrestador List<Procedimentos>",
          () {
        expect(modelTest2!.atendimentosPrestador![0].procedimentos,
            isInstanceOf<List<Procedimentos>>());
      });
      test("Should be return instance of AtendimentosPrestador Procedimentos",
          () {
        expect(modelTest2!.atendimentosPrestador![0].procedimentos![0],
            isInstanceOf<Procedimentos>());
      });
    },
  );

  group("Json test RetornoComparativoAtendimento", () {
    test("Should be return instance of RetornoComparativoAtendimento to json",
        () {
      expect(modelTest2!.toJson(), isInstanceOf<Map<dynamic, dynamic>>());
    });
    test("Should be return instance of RetornoComparativoAtendimento from json",
        () {
      final json = modelTest2!.toJson();
      expect(RetornoComparativoAtendimento.fromJson(json),
          isInstanceOf<RetornoComparativoAtendimento>());
    });

    test("Should be return instance of AtendimentosEspecialidade to json", () {
      expect(modelTest2!.atendimentosEspecialidade![0].toJson(),
          isInstanceOf<Map<dynamic, dynamic>>());
    });
    test("Should be return instance of AtendimentosEspecialidade from json",
        () {
      final json = modelTest2!.atendimentosEspecialidade![0].toJson();
      expect(AtendimentosEspecialidade.fromJson(json),
          isInstanceOf<AtendimentosEspecialidade>());
    });

    test("Should be return instance of AtendimentosPrestador to json", () {
      expect(modelTest2!.atendimentosPrestador![0].toJson(),
          isInstanceOf<Map<dynamic, dynamic>>());
    });
    test("Should be return instance of AtendimentosPrestador from json", () {
      final json = modelTest2!.atendimentosPrestador![0].toJson();
      expect(AtendimentosPrestador.fromJson(json),
          isInstanceOf<AtendimentosPrestador>());
    });
  });

  group(
    "Other tests json type",
    () {
      test("Should be return type json", () {
        expect(json!["ano"], isInstanceOf<int>());
        expect(json!["meses"], isInstanceOf<List>());
        expect(json!["meses"][0], isInstanceOf<Map<dynamic, dynamic>>());
        expect(json!["meses"][0]["mes"], isInstanceOf<int>());

        expect(json!["meses"][0]["producoes"], isInstanceOf<List>());
        expect(json!["meses"][0]["producoes"][0]["descricao"],
            isInstanceOf<String?>());
        expect(json!["meses"][0]["producoes"][0]["dataProducao"],
            isInstanceOf<String?>());
        expect(json!["meses"][0]["producoes"][0]["tipoProducao"],
            isInstanceOf<String?>());
        expect(json!["meses"][0]["producoes"][0]["dataProducaoFIm"],
            isInstanceOf<String?>());
        expect(json!["meses"][0]["producoes"][0]["dataAtendimento"],
            isInstanceOf<String?>());
        expect(json!["meses"][0]["producoes"][0]["valor"],
            isInstanceOf<double?>());
      });

      test("Should be return type json2", () {
        expect(json2!["dataInicio"], isInstanceOf<String>());
        expect(json2!["dataFim"], isInstanceOf<String>());
        expect(json2!["atendimentoPrestador"], isInstanceOf<List?>());

        if (json2!["atendimentoPrestador"] != null) {
          //AtendimentosPrestador
          expect(
              json2?["atendimentoPrestador"][0]["mes"], isInstanceOf<int?>());
          expect(
              json2?["atendimentoPrestador"][0]["ano"], isInstanceOf<int?>());
          expect(
              json2!["atendimentoPrestador"][0]["media"], isInstanceOf<int?>());
          expect(
              json2!["atendimentoPrestador"][0]["total"], isInstanceOf<int?>());
          expect(json2!["atendimentoPrestador"][0]["procedimentos"],
              isInstanceOf<List>());
          if (json2!["atendimentoPrestador"][0]["procedimentos"] != null) {
            //Procedimentos
            expect(
                json2!["atendimentoPrestador"][0]["procedimentos"][0]
                    ["quantidade"],
                isInstanceOf<int?>());
            expect(
                json2!["atendimentoPrestador"][0]["procedimentos"][0]["tipo"],
                isInstanceOf<Tipo?>());
            expect(
                json2!["atendimentoPrestador"][0]["procedimentos"][0]["tipo"]
                    ["descricao"],
                isInstanceOf<String?>());
          }
        }
        //AtendimentoEspecialidade
        expect(json2!["atendimentosEspecialidade"], isInstanceOf<List>());
        expect(
            json2!["atendimentosEspecialidade"][0]["mes"], isInstanceOf<int>());
        expect(json2!["atendimentosEspecialidade"][0]["media"],
            isInstanceOf<double>());

        expect(json2!["atendimentosEspecialidade"][0]["total"],
            isInstanceOf<double>());

        expect(
            json2!["atendimentosEspecialidade"][0]["ano"], isInstanceOf<int>());
        expect(json2!["atendimentosEspecialidade"][0]["prestador"],
            isInstanceOf<Map>());
        expect(json2!["atendimentosEspecialidade"][0]["prestador"]["codigo"],
            isInstanceOf<dynamic>());
        expect(
            json2!["atendimentosEspecialidade"][0]["prestador"]["codigoUnimed"],
            isInstanceOf<dynamic>());
        expect(json2!["atendimentosEspecialidade"][0]["prestador"]["nome"],
            isInstanceOf<dynamic>());
        expect(
            json2!["atendimentosEspecialidade"][0]["prestador"]["nomeFantasia"],
            isInstanceOf<dynamic>());
        expect(
            json2!["atendimentosEspecialidade"][0]["prestador"]
                ["suspensaoCovid"],
            isInstanceOf<dynamic>());
        expect(json2!["atendimentosEspecialidade"][0]["prestador"]["enderecos"],
            isInstanceOf<List>());
        expect(
            json2!["atendimentosEspecialidade"][0]["prestador"]
                ["especialidades"],
            isInstanceOf<List>());
      });
    },
  );
}
