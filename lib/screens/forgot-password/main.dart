import 'package:cooperado_minha_unimed/bloc/forgot-password/forgot_password_cubit.dart';
import 'package:cooperado_minha_unimed/colors.dart';
import 'package:cooperado_minha_unimed/screens/forgot-password/redefine_password.dart';
import 'package:cooperado_minha_unimed/screens/forgot-password/reset_password_message.dart';
import 'package:cooperado_minha_unimed/shared/screen-transitions/fade.transition.dart';
import 'package:cooperado_minha_unimed/shared/utils/validators.dart';
import 'package:cooperado_minha_unimed/shared/widgets/alert.dart';
import 'package:cooperado_minha_unimed/shared/widgets/elevated_button_custom.dart';
import 'package:cooperado_minha_unimed/shared/widgets/item_form.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:header_login/header_login.dart';
import 'package:mask_text_input_formatter/mask_text_input_formatter.dart';

const String clientId = String.fromEnvironment('CLIENT_ID');

class ForgotPasswordScreen extends StatefulWidget {
  final FirebaseAnalytics analytics;
  final FirebaseAnalyticsObserver observer;

  const ForgotPasswordScreen({
    super.key,
    required this.analytics,
    required this.observer,
  });

  @override
  ForgotPasswordScreenState createState() => ForgotPasswordScreenState();
}

class ForgotPasswordScreenState extends State<ForgotPasswordScreen> {
  final dateFormatter = MaskTextInputFormatter(
      mask: '##/##/####', filter: {"#": RegExp(r'[0-9]')});

  TextEditingController crmController = TextEditingController();

  bool? acceptTerm = false;
  final _formKey = GlobalKey<FormState>();

  final FocusNode _cpfFocus = FocusNode();

  @override
  void initState() {
    super.initState();
    widget.analytics.logScreenView(
      screenName: 'Redefinir senha (CRM)',
      screenClass: 'ForgotPasswordScreen',
    );
  }

  @override
  void dispose() {
    _cpfFocus.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: CooperadoColors.grayLight,
      extendBodyBehindAppBar: true,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0.0,
      ),
      body: SafeArea(
        top: false,
        child: Column(
          children: [
            HeaderLogin(
              clientId: clientId,
              innerCircleColor: CooperadoColors.tealGreenDark,
              outerCircleColor: CooperadoColors.tealGreenDark,
            ),
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(20.0).copyWith(top: 0),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: <Widget>[_form()],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _form() {
    return Form(
      key: _formKey,
      child: ListView(
        padding: EdgeInsets.zero,
        physics: const PageScrollPhysics(),
        shrinkWrap: true,
        children: [
          ItemForm(
            title: "CRM",
            validator: (value) => TextFieldValidators.crm(value!),
            controller: crmController,
            type: TextInputType.number,
            focusNode: _cpfFocus,
            next: true,
          ),
          _buttonConfirm(),
          Padding(
            padding: const EdgeInsets.only(top: 10, bottom: 10),
            child: TextButton(
              onPressed: () {
                Navigator.push(
                  context,
                  FadeRoute(
                    page: RedefinePasswordScreen(
                      analytics: widget.analytics,
                      observer: widget.observer,
                    ),
                  ),
                );
              },
              child: const Text(
                "Já tenho um código de redefinição",
                style: TextStyle(
                  decoration: TextDecoration.underline,
                  color: CooperadoColors.tealGreen,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buttonConfirm() {
    return BlocConsumer<ForgotPasswordCubit, ForgotPasswordState>(
      listener: (context, state) {
        if (state is ErrorForgotPasswordState) {
          showDialog(
            context: context,
            builder: (context) => CooperadoAlertDialog(
              textWidget: Text(
                state.message,
                textAlign: TextAlign.center,
              ),
              onPressed: () {
                Navigator.of(context).pop();
              },
            ),
          );
        } else if (state is SucessForgotPassworState) {
          Navigator.push(
            context,
            FadeRoute(
              page: ResetPasswordMessageScreen(
                crm: crmController.text,
                message: state.message,
                analytics: widget.analytics,
                observer: widget.observer,
              ),
            ),
          );
        }
      },
      builder: (context, state) {
        if (state is LoadingForgotPasswordState) {
          return const SpinKitThreeBounce(
            color: CooperadoColors.tealGreen,
            size: 20,
          );
        }
        return ElevatedButtonCustom(
          title: 'Redefinir senha',
          onPressed: () => _submit(),
        );
      },
    );
  }

  void _submit() {
    FocusScope.of(context).unfocus();

    if (_formKey.currentState != null) {
      if (_formKey.currentState!.validate()) {
        context.read<ForgotPasswordCubit>().recoverPassword(
              crm: crmController.text,
            );
      }
    }
  }
}
