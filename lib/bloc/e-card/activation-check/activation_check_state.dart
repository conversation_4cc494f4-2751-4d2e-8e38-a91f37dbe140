import 'package:cooperado_minha_unimed/models/e-card/ecard.model.dart';
import 'package:equatable/equatable.dart';

abstract class EcardActivationCheckState extends Equatable {
  const EcardActivationCheckState();

  @override
  List<Object> get props => [];
}

class InitialEcardActivationCheckState extends EcardActivationCheckState {}

class LoadingEcardActivationCheckState extends EcardActivationCheckState {
  @override
  List<Object> get props => [];
}

class ErrorEcardActivationCheckState extends EcardActivationCheckState {
  final String message;

  @override
  List<Object> get props => [message];

  const ErrorEcardActivationCheckState({required this.message});
}

class ErrorEcardPendingCheckState extends EcardActivationCheckState {
  final String message;

  @override
  List<Object> get props => [message];

  const ErrorEcardPendingCheckState({required this.message});
}

class LoadedEcardActivationCheckState extends EcardActivationCheckState {
  final ECardModel eCardModel;

  @override
  List<Object> get props => [eCardModel];

  const LoadedEcardActivationCheckState({required this.eCardModel});
}
