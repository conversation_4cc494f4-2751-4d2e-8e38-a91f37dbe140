import 'package:flutter/material.dart';

class SilverAppBarReplace extends StatefulWidget {
  final Widget child;
  const SilverAppBarReplace({
    super.key,
    required this.child,
  });

  @override
  SilverAppBarReplaceState createState() => SilverAppBarReplaceState();
}

class SilverAppBarReplaceState extends State<SilverAppBarReplace>
    with TickerProviderStateMixin {
  ScrollPosition? _position;
  bool? _visible;
  late Animation<double> animationPanel;
  late AnimationController animationControllerPanel;

  @override
  void initState() {
    animationControllerPanel = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    animationPanel =
        CurvedAnimation(parent: animationControllerPanel, curve: Curves.easeIn);
    super.initState();
  }

  @override
  void dispose() {
    _removeListener();
    animationControllerPanel.dispose();
    super.dispose();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    _removeListener();
    _addListener();
  }

  void _addListener() {
    _position = Scrollable.of(context).position;
    _position?.addListener(_positionListener);
    _positionListener();
  }

  void _removeListener() {
    _position?.removeListener(_positionListener);
  }

  void _positionListener() {
    final FlexibleSpaceBarSettings? settings = context
        .dependOnInheritedWidgetOfExactType(aspect: FlexibleSpaceBarSettings);
    // context.inheritFromWidgetOfExactType(FlexibleSpaceBarSettings);
    bool visible =
        settings == null || settings.currentExtent <= settings.minExtent;
    if (_visible != visible) {
      setState(() {
        _visible = visible;
        _visible!
            ? animationControllerPanel.forward()
            : animationControllerPanel.reverse();
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return FadeTransition(
      opacity: animationPanel,
      child: widget.child,
    );
  }
}
