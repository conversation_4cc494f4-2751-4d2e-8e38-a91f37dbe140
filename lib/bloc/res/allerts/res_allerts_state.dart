part of 'res_allerts_cubit.dart';

abstract class ResBrazilAllertsState extends Equatable {
  const ResBrazilAllertsState();

  @override
  List<Object> get props => [];
}

class InitialResAllertsState extends ResBrazilAllertsState {}

class NoDataResAllertsState extends ResBrazilAllertsState {}

class LoadingResAllertsState extends ResBrazilAllertsState {
  @override
  List<Object> get props => [];
}

class ErrorResAllertsState extends ResBrazilAllertsState {
  final String message;

  @override
  List<Object> get props => [message];

  const ErrorResAllertsState({required this.message});
}

class LoadedResAllertsState extends ResBrazilAllertsState {
  final List<ResAllertModel> listAllerts;

  @override
  List<Object> get props => [listAllerts];

  const LoadedResAllertsState({required this.listAllerts});
}
