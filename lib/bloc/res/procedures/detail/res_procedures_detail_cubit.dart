import 'package:cooperado_minha_unimed/models/res/res_procedure_detail_model.dart';
import 'package:cooperado_minha_unimed/shared/api/graphql-res.api.dart';
import 'package:cooperado_minha_unimed/shared/locator.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

part 'res_procedures_detail_state.dart';

class ResProcedureDetailCubit extends Cubit<ResProcedureDetailState> {
  ResProcedureDetailCubit() : super(InitialResProcedureDetailState());

  List<ResProcedureDetailModel>? _resProcedureDetailModel;
  List<ResProcedureDetailModel>? get resProcedureDetailModel =>
      _resProcedureDetailModel;

  void getProcedureDetail(
      {required String crm,
      required String card,
      required String code,
      int index = 0}) async {
    try {
      emit(LoadingResProcedureDetailState());

      _resProcedureDetailModel = await Locator.instance!<ResGraphQlApi>()
          .resProcedureDetail(crm: crm, card: card, code: code);

      emit(LoadedResProcedureDetailState(
          resProcedureDetailModel: _resProcedureDetailModel!, index: index));
    } catch (e) {
      emit(ErrorResProcedureDetailState(message: e.toString()));
    }
  }
}
