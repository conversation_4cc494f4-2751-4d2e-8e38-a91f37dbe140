import 'package:cooperado_minha_unimed/models/res/res_attendance_model.dart';
import 'package:equatable/equatable.dart';

abstract class ResBrasilAtendimentoResultState extends Equatable {
  const ResBrasilAtendimentoResultState();

  @override
  List<Object> get props => [];
}

class InitialResBrasilAtendimentoResultState
    extends ResBrasilAtendimentoResultState {}

class LoadingResBrasilAtendimentoResultState
    extends ResBrasilAtendimentoResultState {
  @override
  List<Object> get props => [];
}

class NoDataResBrasilAtendimentoResultState
    extends ResBrasilAtendimentoResultState {
  final String message;

  @override
  List<Object> get props => [message];

  const NoDataResBrasilAtendimentoResultState({required this.message});
}

class ErrorResBrasilAtendimentoResultState
    extends ResBrasilAtendimentoResultState {
  final String message;

  @override
  List<Object> get props => [message];

  const ErrorResBrasilAtendimentoResultState({required this.message});
}

class LoadedResBrasilAtendimentoResultState
    extends ResBrasilAtendimentoResultState {
  final List<ResAttendanceModel> listAtendimentoResult;

  @override
  List<Object> get props => [listAtendimentoResult];

  const LoadedResBrasilAtendimentoResultState(
      {required this.listAtendimentoResult});
}
