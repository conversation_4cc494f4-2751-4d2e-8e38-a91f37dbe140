import 'package:bloc/bloc.dart';
import 'package:cooperado_minha_unimed/models/service_historic.model.dart';
import 'package:cooperado_minha_unimed/shared/api/indicadores.api.dart';
import 'package:cooperado_minha_unimed/shared/exceptions.dart';
import 'package:cooperado_minha_unimed/shared/locator.dart';
import 'package:equatable/equatable.dart';

part 'attendance_comparative_state.dart';

class AttendanceComparativeCubit extends Cubit<AttendanceComparativeState> {
  AttendanceComparativeCubit() : super(InitialAttendanceComparative());

  ResponseServiceHistoric? _serviceHistoric;

  getAttendanceComparative(DateTime lastProduction) async {
    try {
      emit(LoadingAttendanceComparativeState());
      _serviceHistoric = await Locator.instance!<IndicadoresApi>()
          .getServicesHistoric(lastProduction);
      emit(LoadedAttendanceComparativeState(_serviceHistoric));
    } on IndicadoresException catch (e) {
      emit(ErrorAttendanceComparativeState(e.message));
    } catch (e) {
      emit(ErrorAttendanceComparativeState('$e'));
    }
  }

  repaintChart() {
    emit(LoadedAttendanceComparativeState(_serviceHistoric));
  }

  reloadChart() {
    emit(ReLoadingAttendanceComparativeState());
  }
}
