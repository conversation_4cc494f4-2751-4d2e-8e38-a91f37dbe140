import 'package:cooperado_minha_unimed/models/version_remote.model.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  VersionRemoteModel? modelTest;
  Map? json;
  setUpAll(
    () {
      modelTest = VersionRemoteModel(
        forceUpdate: true,
        buildNumber: 1222,
        versionNumber: "2",
      );
      json = {
        "force_update": false,
        "build_number": 310023,
        "version_number": "3.1.0"
      };
    },
  );

  group(
    "isInstanceOf VersionRemoteModel model tests",
    () {
      test("Should be return instance of VersionRemoteModel", () {
        expect(modelTest, isInstanceOf<VersionRemoteModel>());
      });

      test("Should be return instance of String", () {
        expect(modelTest!.versionNumber, isInstanceOf<String>());
      });
      test("Should be return instance of Int", () {
        expect(modelTest!.buildNumber, isInstanceOf<int>());
      });
      test("Should be return instance of Bool", () {
        expect(modelTest!.forceUpdate, isInstanceOf<bool>());
      });
    },
  );

  group("Json test", () {
    test("Should be return instance of VersionRemoteModel to json", () {
      expect(modelTest!.toJson(), isInstanceOf<Map<dynamic, dynamic>>());
    });
    test("Should be return instance of VersionRemoteModel from json", () {
      expect(VersionRemoteModel.fromJson(json!),
          isInstanceOf<VersionRemoteModel>());
    });
  });

  group(
    "isInstanceOf VersionRemoteModel json to model type test",
    () {
      test("Should be return instance of bool", () {
        expect(json!["force_update"], isInstanceOf<bool>());
      });

      test("Should be return instance of int", () {
        expect(json!["build_number"], isInstanceOf<int>());
      });
      test("Should be return instance of String", () {
        expect(json!["version_number"], isInstanceOf<String>());
      });
      test("Can´t return if is null", () {
        expect(json!["force_update"] == null, false);
        expect(json!["build_number"] == null, false);
        expect(json!["version_number"] == null, false);
      });
    },
  );

  group("Json test errors", () {
    Map<dynamic, dynamic> jsonError = {
      "force_update": "true",
      "build_number": 310023,
      "version_number": "3.2.0"
    };

    test(
        "Should be return an error 'type 'String' is not a subtype of type 'bool?' in type cast",
        () {
      try {
        VersionRemoteModel.fromJson(jsonError);
      } catch (e) {
        expect(e.toString(),
            "type 'String' is not a subtype of type 'bool?' in type cast");
      }
    });
    test("Should be return a version number 320000", () {
      expect(
          int.parse(
                  jsonError["version_number"].toString().replaceAll(".", "")) *
              1000,
          320000);
    });
    test("Should be return a build number 310023", () {
      expect(jsonError["build_number"], 310023);
    });
    test("Should be return if needs to update", () {
      expect(
          jsonError["build_number"] >=
              int.parse(jsonError["version_number"]
                      .toString()
                      .replaceAll(".", "")) *
                  1000,
          false);
      jsonError["force_update"] = true;
      expect(jsonError["force_update"], true);
    });
  });
}
