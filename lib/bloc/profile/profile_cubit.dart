import 'package:cooperado_minha_unimed/models/user.model.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

part 'profile_state.dart';

class ProfileCubit extends Cubit<ProfileState> {
  ProfileCubit() : super(InitialProfileState());

  User? _user;
  User get user => _user ?? User.empty;

  void setUser(User user) async {
    try {
      emit(LoadingProfileState());
      _user = user;
      emit(LoadedProfileState(user));
    } catch (e) {
      emit(ErrorProfileState(e.toString()));
    }
  }
}
