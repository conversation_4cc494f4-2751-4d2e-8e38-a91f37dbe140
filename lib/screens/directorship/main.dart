import 'dart:math';

import 'package:auto_size_text/auto_size_text.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:cooperado_minha_unimed/bloc/auth/auth_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/directors/directors_cubit.dart';
import 'package:cooperado_minha_unimed/colors.dart';
import 'package:cooperado_minha_unimed/models/diretoria.model.dart';
import 'package:cooperado_minha_unimed/screens/fiscal-council/image_member.dart';
import 'package:cooperado_minha_unimed/shared/constants.dart';
import 'package:cooperado_minha_unimed/shared/screen-transitions/fade.transition.dart';
import 'package:cooperado_minha_unimed/shared/widgets/error-banner/error_banner.component.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';

class DiretoriaScreen extends StatefulWidget {
  final FirebaseAnalytics analytics;
  final FirebaseAnalyticsObserver observer;

  const DiretoriaScreen(
      {super.key, required this.analytics, required this.observer});

  @override
  DiretoriaScreenState createState() => DiretoriaScreenState();
}

class DiretoriaScreenState extends State<DiretoriaScreen>
    with SingleTickerProviderStateMixin {
  double _animatedHeight = 0.0;
  bool _expandedMode = false;
  late AnimationController _arrowAnimationController;
  late Animation _arrowAnimation;
  ScrollController scrollController = ScrollController();
  String description = "";
  bool _isExpanded = false;

  @override
  void initState() {
    context.read<DirectorsCubit>().getDirectors();
    _arrowAnimationController = AnimationController(
        vsync: this, duration: const Duration(milliseconds: 300));
    _arrowAnimation =
        Tween(begin: 0.0, end: pi).animate(_arrowAnimationController);
    description = context.read<AuthCubit>().getMessageConfig(diretoria);

    super.initState();

    widget.analytics.logScreenView(
      screenName: 'Diretoria',
      screenClass: 'DiretoriaScreen',
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        centerTitle: true,
        title: const Text("Diretoria"),
        backgroundColor: CooperadoColors.tealGreenDark,
      ),
      body: RefreshIndicator(
        onRefresh: () async {
          context.read<DirectorsCubit>().getDirectors();
        },
        color: CooperadoColors.tealGreen,
        child: SafeArea(
          child: _body(),
        ),
      ),
    );
  }

  Widget _body() {
    return BlocBuilder<DirectorsCubit, DirectorsState>(
      builder: (context, state) {
        if (state is LoadingDirectorsState) {
          return const SpinKitCircle(color: CooperadoColors.tealGreen);
        } else if (state is ErrorDirectorsState) {
          return Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Center(
                child: ErrorBanner(
                  message: state.message,
                ),
              ),
            ],
          );
        } else if (state is LoadedDirectorsState) {
          return ListView(
            controller: scrollController,
            children: [
              Padding(
                padding: const EdgeInsets.all(10.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: <Widget>[
                    Container(
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(15.0),
                        color: _isExpanded
                            ? CooperadoColors.aliceBlue
                            : CooperadoColors.tealGreen,
                      ),
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(15.0),
                        child: Theme(
                          data: ThemeWidgets.expansionTile(
                            context,
                            colorArrow: _isExpanded
                                ? CooperadoColors.tealGreen
                                : Colors.white,
                          ),
                          child: ExpansionTile(
                            backgroundColor: _isExpanded
                                ? CooperadoColors.aliceBlue
                                : CooperadoColors.tealGreen,
                            onExpansionChanged: (value) {
                              setState(() {
                                _isExpanded = value;
                              });
                            },
                            tilePadding:
                                const EdgeInsets.symmetric(horizontal: 15),
                            childrenPadding: const EdgeInsets.symmetric(
                              horizontal: 15,
                              vertical: 10,
                            ),
                            title: Text(
                              "A Diretoria",
                              style: TextStyle(
                                  color: _isExpanded
                                      ? CooperadoColors.blackText
                                      : Colors.white),
                            ),
                            children: <Widget>[
                              Text(
                                description,
                                textAlign: TextAlign.start,
                                style: const TextStyle(
                                    color: CooperadoColors.blackText),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                    Card(
                      child: Padding(
                        padding: const EdgeInsets.all(8.0),
                        child: SingleChildScrollView(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: <Widget>[
                              const Text(
                                "Diretores Unimed",
                                style: TextStyle(
                                    fontWeight: FontWeight.bold, fontSize: 16),
                              ),
                              const SizedBox(height: 25),
                              _gridDiretores(state.diretoriaNoPresident!,
                                  state.president!),
                              _textExpand(state.retornoDiretoria!)
                            ],
                          ),
                        ),
                      ),
                    )
                  ],
                ),
              ),
            ],
          );
        } else {
          return Container();
        }
      },
    );
  }

  Widget _gridDiretores(List<Diretores> diretores, Diretores president) {
    return Column(
      children: [
        Column(
          children: [
            _thumbImage(president),
            Column(
              children: [
                AutoSizeText(
                  president.nome!,
                  textAlign: TextAlign.center,
                  style: const TextStyle(color: CooperadoColors.blackText),
                  minFontSize: 8,
                  maxFontSize: 14,
                ),
                AutoSizeText(
                  president.cargo!,
                  textAlign: TextAlign.center,
                  minFontSize: 6,
                  maxFontSize: 12,
                  maxLines: 3,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            )
          ],
        ),
        const SizedBox(height: 15),
        GridView.count(
          crossAxisCount: 2,
          controller: ScrollController(keepScrollOffset: false),
          shrinkWrap: true,
          scrollDirection: Axis.vertical,
          children: diretores.map((director) {
            return Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: <Widget>[
                Flexible(flex: 10, child: _thumbImage(director)),
                Flexible(
                  flex: 3,
                  child: AutoSizeText(
                    director.nome!,
                    textAlign: TextAlign.center,
                    style: const TextStyle(color: CooperadoColors.blackText),
                    minFontSize: 8,
                    maxFontSize: 14,
                  ),
                ),
                Flexible(
                  flex: 3,
                  child: AutoSizeText(
                    director.cargo!,
                    textAlign: TextAlign.center,
                    style: const TextStyle(color: CooperadoColors.grayDark),
                    minFontSize: 6,
                    maxFontSize: 12,
                    maxLines: 3,
                    overflow: TextOverflow.ellipsis,
                  ),
                )
              ],
            );
          }).toList(),
        ),
      ],
    );
  }

  Widget _thumbImage(Diretores director) {
    return InkWell(
      child: CachedNetworkImage(
        height: 100,
        imageUrl: director.thumb!,
        placeholder: (context, url) => const SizedBox(
            height: 100, width: 100, child: CircularProgressIndicator()),
        errorWidget: (context, url, error) =>
            const Icon(Icons.account_circle, color: CooperadoColors.grayLight),
      ),
      onTap: () {
        Navigator.push(
          context,
          FadeRoute(
            page: ImageMember(
              imageUrl: director.imagem ?? "",
              title: director.nome ?? "",
              subtitle: director.cargo ?? "",
            ),
          ),
        );
      },
    );
  }

  Widget _textExpand(RetornoDiretoria retornoDiretoria) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: <Widget>[
        AnimatedContainer(
          duration: const Duration(milliseconds: 500),
          curve: Curves.easeOutCirc,
          height: _animatedHeight,
          width: 100.0,
          child: _listText(retornoDiretoria),
        ),
        GestureDetector(
          onTap: () async {
            _arrowAnimationController.isCompleted
                ? _arrowAnimationController.reverse()
                : _arrowAnimationController.forward();
            _animatedHeight != 400
                ? _animatedHeight = 400
                : _animatedHeight = 0;

            _expandedMode = !_expandedMode;
            setState(() {});
            if (_expandedMode) {
              await Future.delayed(const Duration(milliseconds: 250));

              scrollController.animateTo(
                  scrollController.position.maxScrollExtent,
                  duration: const Duration(milliseconds: 500),
                  curve: Curves.ease);
            }
          },
          child: AnimatedBuilder(
            animation: _arrowAnimationController,
            builder: (context, child) => Transform.rotate(
              angle: _arrowAnimation.value,
              child: const Icon(
                Icons.expand_more,
                size: 30,
                color: Colors.black,
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _listText(RetornoDiretoria retornoDiretoria) {
    return ListView.builder(
      physics: const NeverScrollableScrollPhysics(),
      shrinkWrap: true,
      itemCount: retornoDiretoria.conselhos!.length,
      itemBuilder: (context, index) {
        final conselhos = retornoDiretoria.conselhos!.elementAt(index);
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: <Widget>[
            const SizedBox(height: 10),
            SizedBox(
              width: 200,
              child: Text(
                conselhos.nome!,
                style: const TextStyle(fontWeight: FontWeight.normal),
              ),
            ),
            const SizedBox(height: 10),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children:
                  conselhos.membros!.map((value) => Text(value.nome!)).toList(),
            ),
            const SizedBox(height: 10),
          ],
        );
      },
    );
  }
}
