import 'package:cooperado_minha_unimed/bloc/sensitive-data/sensitive_data_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/sensitive-data/sensitive_data_state.dart';
import 'package:cooperado_minha_unimed/colors.dart';
import 'package:cooperado_minha_unimed/screens/indicadores/daily-attendance/daily.dart';
import 'package:cooperado_minha_unimed/shared/vo/indicadores/daily_attendance.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class DetailsScreen extends StatefulWidget {
  final List<ResponseDailyAttendance> list;
  final int services;
  const DetailsScreen({super.key, required this.list, required this.services});
  @override
  DetailsScreenState createState() => DetailsScreenState();
}

class DetailsScreenState extends State<DetailsScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text("Detalhes"),
        backgroundColor: CooperadoColors.tealGreenDark,
        actions: [
          Padding(
            padding: const EdgeInsets.only(right: 13.0),
            child: IconButton(
            icon: BlocBuilder<SensitiveDataCubit, SensitiveDataState>(
              builder: (context, state) {
                return Icon(
                  state.isSensitiveDataVisible ? Icons.visibility : Icons.visibility_off,
                );
              },
            ),
            onPressed: () {
              context.read<SensitiveDataCubit>().toggleSensitiveDataVisibility();
            },
                    ),
          ),
        ],
      ),
      body: SafeArea(
        child: Container(
          color: Colors.white,
          child: Column(
            children: [
              const SizedBox(height: 20),
              Hero(
                tag: 'daily-attendance',
                child: Daily(services: widget.services, animate: false),
              ),
              Expanded(
                flex: 1,
                child: _details(),
              )
            ],
          ),
        ),
      ),
    );
  }

  Widget _details() {
  return BlocBuilder<SensitiveDataCubit, SensitiveDataState>(
    builder: (context, state) {
      final isSensitiveData = !state.isSensitiveDataVisible;

      return ListView.builder(
        padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 15),
        primary: true,
        shrinkWrap: true,
        physics: const ClampingScrollPhysics(parent: AlwaysScrollableScrollPhysics()),
        itemCount: widget.list.length,
        itemBuilder: (context, index) {
          return Padding(
            padding: const EdgeInsets.symmetric(vertical: 5),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: <Widget>[
                Expanded(
                  flex: 1,
                  child: Row(
                    children: <Widget>[
                      const Icon(Icons.calendar_today, color: CooperadoColors.tealGreen),
                      const SizedBox(width: 2),
                      Text(
                        isSensitiveData
                            ? '**'
                            : widget.list[index].dataAutorizacao!.substring(0, 5),
                        style: const TextStyle(
                            color: CooperadoColors.tealGreen,
                            fontWeight: FontWeight.bold),
                      ),
                    ],
                  ),
                ),
                Expanded(
                  flex: 2,
                  child: Container(
                    color: CooperadoColors.tealGreen,
                    height: 25,
                    alignment: Alignment.center,
                    child: Text(
                      isSensitiveData
                          ? '****'
                          : countServices(index).toString(),
                      style: const TextStyle(
                          color: Colors.white, fontWeight: FontWeight.bold),
                    ),
                  ),
                ),
              ],
            ),
          );
        },
      );
    },
  );
}

  int countServices(int index) {
    int quantity = 0;

    for (Procedimentos element in widget.list[index].procedimentos!) {
      quantity += element.quantidade!;
    }

    return quantity;
  }
}
