import 'package:cooperado_minha_unimed/colors.dart';
import 'package:cooperado_minha_unimed/shared/utils/graph_utils.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:flutter/material.dart';

import '../../indicadores/attendace-comparative/line_chart_compartative.dart';

class LineGraphHistoryAttendency extends StatefulWidget {
  const LineGraphHistoryAttendency(
      {super.key,
      required this.dataMediaEspecialidade,
      required this.dataPrestador});

  final List<Valores> dataMediaEspecialidade;
  final List<Valores> dataPrestador;

  @override
  State<LineGraphHistoryAttendency> createState() =>
      _LineGraphHistoryAttendencyState();
}

class _LineGraphHistoryAttendencyState
    extends State<LineGraphHistoryAttendency> {
  List<Color> gradientColorsME = [
    CooperadoColors.tealGreen,
    CooperadoColors.tealGreen,
  ];
  List<Color> gradientColorsProvider = [
    CooperadoColors.green,
    CooperadoColors.green,
  ];

  List<double> valuesYmediaEspecialidade = [];
  List<double> valuesYprestador = [];
  double maxLeftValue = 0;
  @override
  void initState() {
    valuesYmediaEspecialidade.clear();
    valuesYprestador.clear();

    for (Valores element in widget.dataMediaEspecialidade) {
      valuesYmediaEspecialidade.add(double.parse(element.valor));
    }

    for (Valores element in widget.dataPrestador) {
      valuesYprestador.add(double.parse(element.valor));
    }
    maxLeftValue = findTotalValue();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: <Widget>[
        AspectRatio(
          aspectRatio: 1.70,
          child: Padding(
            padding: const EdgeInsets.only(
              right: 18,
              left: 12,
              top: 24,
              bottom: 12,
            ),
            child: LineChart(
              mainData(),
            ),
          ),
        ),
      ],
    );
  }

  Widget bottomTitleWidgets(double value, TitleMeta meta) {
    List<String> dates = [];
    for (int i = 0; i < widget.dataMediaEspecialidade.length; i++) {
      dates.add(widget.dataMediaEspecialidade[i].mes);
    }

    return SideTitleWidget(
      axisSide: meta.axisSide,
      child: Text(dates[value.toInt()], style: const TextStyle(fontSize: 13)),
    );
  }

  double findTotalValue() {
    var valuesME = GraphUtils.findMax(valuesYmediaEspecialidade);
    var valuesPre = GraphUtils.findMax(valuesYprestador);
    double total = GraphUtils.findMax([valuesME, valuesPre]);
    return total;
  }

  Widget leftTitleWidgets(double value, TitleMeta meta) {
    String text = '';
    int intValue = value.toInt();

    for (int i = 0; i <= 5; i++) {
      double threshold = maxLeftValue / 4 * i;

      if (intValue == GraphUtils.convertToInterger(threshold)) {
        text = threshold.toStringAsFixed(0);
        break;
      }
    }

    return Padding(
      padding: const EdgeInsets.only(right: 5),
      child: Text(
        text,
        textAlign: TextAlign.right,
        style: const TextStyle(fontSize: 11),
      ),
    );
  }

  LineChartData mainData() {
    return LineChartData(
      gridData: FlGridData(
        show: true,
        drawHorizontalLine: true,
        drawVerticalLine: false,
        horizontalInterval:
            findTotalValue() / widget.dataMediaEspecialidade.length,
        verticalInterval: 1,
        getDrawingHorizontalLine: (value) {
          return const FlLine(
            color: Colors.grey,
            strokeWidth: 0.8,
          );
        },
      ),
      titlesData: FlTitlesData(
        show: true,
        rightTitles: const AxisTitles(
          sideTitles: SideTitles(showTitles: false),
        ),
        topTitles: const AxisTitles(
          sideTitles: SideTitles(showTitles: false),
        ),
        bottomTitles: AxisTitles(
          sideTitles: SideTitles(
            showTitles: true,
            reservedSize: 30,
            interval: 1,
            getTitlesWidget: bottomTitleWidgets,
          ),
        ),
        leftTitles: AxisTitles(
          sideTitles: SideTitles(
            showTitles: true,
            interval: 1,
            getTitlesWidget: leftTitleWidgets,
            reservedSize: 60,
          ),
        ),
      ),
      borderData: FlBorderData(
        show: true,
        border: const Border(bottom: BorderSide(), top: BorderSide()),
      ),
      minX: 0,
      maxX: widget.dataMediaEspecialidade.length.toDouble() - 1,
      minY: 0,
      maxY: findTotalValue(),
      // maxY: 5,
      lineTouchData: LineTouchData(
        getTouchLineEnd: (data, index) => double.infinity,
        getTouchedSpotIndicator:
            (LineChartBarData barData, List<int> spotIndexes) {
          return spotIndexes.map((spotIndex) {
            return TouchedSpotIndicatorData(
              const FlLine(color: Colors.grey, strokeWidth: 3),
              FlDotData(
                getDotPainter: (spot, percent, barData, index) =>
                    FlDotCirclePainter(
                  radius: 8,
                  color: barData.color!,
                ),
              ),
            );
          }).toList();
        },
        touchTooltipData: LineTouchTooltipData(
          maxContentWidth: 100,
          tooltipBgColor: Colors.grey[300]!,
          getTooltipItems: (touchedSpots) {
            return touchedSpots.map((LineBarSpot touchedSpot) {
              final textStyle = TextStyle(
                color: touchedSpot.bar.color,
                fontWeight: FontWeight.bold,
                fontSize: 14,
              );
              return LineTooltipItem(
                touchedSpot.bar.color == CooperadoColors.tealGreen
                    ? 'Média Especialidade\n R\$ ${GraphUtils.priceToCurrency(touchedSpot.y)}'
                    : 'Cooperado\n R\$ ${GraphUtils.priceToCurrency(touchedSpot.y)}',
                textStyle,
              );
            }).toList();
          },
        ),
      ),
      lineBarsData: [
        LineChartBarData(
          spots: List.generate(
              valuesYmediaEspecialidade.length,
              (index) =>
                  FlSpot(index.toDouble(), valuesYmediaEspecialidade[index])),
          isCurved: false,
          color: CooperadoColors.tealGreen,
          barWidth: 3,
          isStrokeCapRound: true,
          dotData: const FlDotData(
            show: true,
          ),
          belowBarData: BarAreaData(
            show: true,
            gradient: LinearGradient(
              colors: gradientColorsME
                  .map((color) => color.withOpacity(0.1))
                  .toList(),
            ),
          ),
        ),
        LineChartBarData(
          spots: List.generate(valuesYprestador.length,
              (index) => FlSpot(index.toDouble(), valuesYprestador[index])),
          isCurved: false,
          color: CooperadoColors.green,
          barWidth: 3,
          isStrokeCapRound: true,
          dotData: const FlDotData(
            show: true,
          ),
          belowBarData: BarAreaData(
            show: true,
            gradient: LinearGradient(
              colors: gradientColorsProvider
                  .map((color) => color.withOpacity(0.1))
                  .toList(),
            ),
          ),
        ),
      ],
    );
  }
}
