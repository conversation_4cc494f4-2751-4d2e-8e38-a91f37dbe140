class RetornoHistoricoProdMed {
  int? ano;
  List<Meses>? meses;

  RetornoHistoricoProdMed({this.ano, this.meses});

  RetornoHistoricoProdMed.fromJson(Map<String, dynamic> json) {
    ano = json['ano'];
    if (json['meses'] != null) {
      meses = [];
      json['meses'].forEach((v) {
        meses!.add(Meses.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['ano'] = ano;
    if (meses != null) {
      data['meses'] = meses!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class Meses {
  int? mes;
  List<Producoes>? producoes;
  double? total;

  Meses({this.mes, this.producoes, this.total});

  Meses.fromJson(Map<String, dynamic> json) {
    mes = json['mes'];
    if (json['producoes'] != null) {
      producoes = [];
      json['producoes'].forEach((v) {
        producoes!.add(Producoes.fromJson(v));
      });
    }
    total = json['total'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['mes'] = mes;
    if (producoes != null) {
      data['producoes'] = producoes!.map((v) => v.toJson()).toList();
    }
    data['total'] = total;
    return data;
  }
}

class Producoes {
  String? descricao;
  String? tipoProducao;
  String? dataProducao;
  String? dataProducaoFim;
  String? dataAtendimento;
  double? valor;

  Producoes(
      {this.descricao,
      this.tipoProducao,
      this.dataProducao,
      this.dataProducaoFim,
      this.dataAtendimento,
      this.valor});

  Producoes.fromJson(Map<String, dynamic> json) {
    descricao = json['descricao'];
    tipoProducao = json['tipoProducao'];
    dataProducao = json['dataProducao'];
    dataProducaoFim = json['dataProducaoFim'];
    dataAtendimento = json['dataAtendimento'];
    valor = json['valor'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['descricao'] = descricao;
    data['tipoProducao'] = tipoProducao;
    data['dataProducao'] = dataProducao;
    data['dataProducaoFim'] = dataProducaoFim;
    data['dataAtendimento'] = dataAtendimento;
    data['valor'] = valor;
    return data;
  }
}

class RetornoComparativoAtendimento {
  String? dataInicio;
  String? dataFim;
  List<AtendimentosPrestador>? atendimentosPrestador;
  List<AtendimentosEspecialidade>? atendimentosEspecialidade;

  RetornoComparativoAtendimento(
      {this.dataInicio,
      this.dataFim,
      this.atendimentosPrestador,
      this.atendimentosEspecialidade});

  RetornoComparativoAtendimento.fromJson(Map<String, dynamic> json) {
    dataInicio = json['dataInicio'];
    dataFim = json['dataFim'];
    if (json['atendimentosPrestador'] != null) {
      atendimentosPrestador = [];
      json['atendimentosPrestador'].forEach((v) {
        atendimentosPrestador!.add(AtendimentosPrestador.fromJson(v));
      });
    }
    if (json['atendimentosEspecialidade'] != null) {
      atendimentosEspecialidade = [];
      json['atendimentosEspecialidade'].forEach((v) {
        atendimentosEspecialidade!.add(AtendimentosEspecialidade.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['dataInicio'] = dataInicio;
    data['dataFim'] = dataFim;
    if (atendimentosPrestador != null) {
      data['atendimentosPrestador'] =
          atendimentosPrestador!.map((v) => v.toJson()).toList();
    }
    if (atendimentosEspecialidade != null) {
      data['atendimentosEspecialidade'] =
          atendimentosEspecialidade!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class AtendimentosPrestador {
  int? mes;
  int? ano;
  List<Procedimentos>? procedimentos;
  int? media;
  int? total;

  AtendimentosPrestador(
      {this.mes, this.ano, this.procedimentos, this.media, this.total});

  AtendimentosPrestador.fromJson(Map<String, dynamic> json) {
    mes = json['mes'];
    ano = json['ano'];
    if (json['procedimentos'] != null) {
      procedimentos = [];
      json['procedimentos'].forEach((v) {
        procedimentos!.add(Procedimentos.fromJson(v));
      });
    }
    media = json['media'];
    total = json['total'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['mes'] = mes;
    data['ano'] = ano;
    if (procedimentos != null) {
      data['procedimentos'] = procedimentos!.map((v) => v.toJson()).toList();
    }
    data['media'] = media;
    data['total'] = total;
    return data;
  }
}

class AtendimentosEspecialidade {
  int? mes;
  int? ano;
  List<Procedimentos>? procedimentos;
  int? media;
  int? total;

  AtendimentosEspecialidade(
      {this.mes, this.ano, this.procedimentos, this.media, this.total});

  AtendimentosEspecialidade.fromJson(Map<String, dynamic> json) {
    mes = json['mes'];
    ano = json['ano'];
    if (json['procedimentos'] != null) {
      procedimentos = [];
      json['procedimentos'].forEach((v) {
        procedimentos!.add(Procedimentos.fromJson(v));
      });
    }
    media = json['media'];
    total = json['total'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['mes'] = mes;
    data['ano'] = ano;
    if (procedimentos != null) {
      data['procedimentos'] = procedimentos!.map((v) => v.toJson()).toList();
    }
    data['media'] = media;
    data['total'] = total;
    return data;
  }
}

class Procedimentos {
  int? quantidade;
  Tipo? tipo;

  Procedimentos({this.quantidade, this.tipo});

  Procedimentos.fromJson(Map<String, dynamic> json) {
    quantidade = json['quantidade'];
    tipo = json['tipo'] != null ? Tipo.fromJson(json['tipo']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['quantidade'] = quantidade;
    if (tipo != null) {
      data['tipo'] = tipo!.toJson();
    }
    return data;
  }
}

class Tipo {
  String? descricao;

  Tipo({this.descricao});

  Tipo.fromJson(Map<String, dynamic> json) {
    descricao = json['descricao'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['descricao'] = descricao;
    return data;
  }
}
