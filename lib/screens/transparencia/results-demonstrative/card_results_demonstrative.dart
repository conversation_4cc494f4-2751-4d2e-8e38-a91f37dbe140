import 'package:cooperado_minha_unimed/bloc/transparency/demonstatives_results/demonstatives_results_cubit.dart';
import 'package:cooperado_minha_unimed/colors.dart';
import 'package:cooperado_minha_unimed/screens/transparencia/results-demonstrative/detals_results_demonstrative.dart';
import 'package:cooperado_minha_unimed/shared/screen-transitions/fade.transition.dart';
import 'package:cooperado_minha_unimed/shared/vo/transparencia/demostrativo_resultado.dart';
import 'package:cooperado_minha_unimed/shared/widgets/card_refresh.dart';
import 'package:cooperado_minha_unimed/shared/widgets/error-banner/error_banner.component.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:intl/intl.dart';

class CardDemonstrativoResultados extends StatefulWidget {
  final bool isVisible;

  const CardDemonstrativoResultados({super.key, this.isVisible = true});

  @override
  CardDemonstrativoResultadosState createState() =>
      CardDemonstrativoResultadosState();
}

class CardDemonstrativoResultadosState
    extends State<CardDemonstrativoResultados> {
  double total = 0.0;
  DateTime? selectedDateTime;

  @override
  void initState() {
    if (widget.isVisible) _getDemonstrativesResults();
    super.initState();
  }

  void _getDemonstrativesResults() {
    context
        .read<DemonstrativesResultsCubit>()
        .getDemonstrativesResults(pagina: 1);
  }

  @override
  Widget build(BuildContext context) {
    return Visibility(
      visible: widget.isVisible,
      child: CardRefresh(
        title: const Text('Resultados',
            style: TextStyle(
              color: CooperadoColors.blackText,
              fontSize: 16,
              fontWeight: FontWeight.bold,
            )),
        refresh: _iconRefresh(),
        child:
            BlocBuilder<DemonstrativesResultsCubit, DemonstrativesResultsState>(
          builder: (context, state) {
            if (state is LoadedDemonstrativesResultsState) {
              return GestureDetector(
                onTap: () => Navigator.push(
                  context,
                  FadeRoute(
                    page: ResultsDemonstrativeDetail(
                        demonstativo: state.demonstrativoResultados),
                  ),
                ),
                child: _demonstrativos(state.demonstrativoResultados),
              );
            } else if (state is LoadingDemonstrativesResultsState) {
              return const SpinKitCircle(color: CooperadoColors.tealGreen);
            } else if (state is ErrorDemonstrativesResultsState) {
              return ErrorBanner(message: state.message);
            } else {
              return Container();
            }
          },
        ),
      ),
    );
  }

  Widget _iconRefresh() {
    return BlocBuilder<DemonstrativesResultsCubit, DemonstrativesResultsState>(
        builder: (context, state) {
      if (state is ErrorDemonstrativesResultsState) {
        return InkWell(
            child: const Icon(Icons.refresh),
            onTap: () => _getDemonstrativesResults());
      } else {
        return Container();
      }
    });
  }

  Widget _demonstrativos(DemonstrativoResultadosVO demonstrativoResultadosVO) {
    return Column(
      children: <Widget>[
        Row(
          children: <Widget>[
            Expanded(
                flex: 1,
                child: _resultado(demonstrativoResultadosVO.noticias![0])),
            Expanded(
              flex: 1,
              child: Container(
                decoration: const BoxDecoration(
                    border: Border(
                  left: BorderSide(
                      width: 1.0, color: CooperadoColors.greenLight3),
                )),
                child: Padding(
                  padding: const EdgeInsets.only(left: 5),
                  child: _resultado(demonstrativoResultadosVO.noticias![1]),
                ),
              ),
            ),
          ],
        ),
        Row(
          children: <Widget>[
            Expanded(
                flex: 1,
                child: _resultado(demonstrativoResultadosVO.noticias![2])),
            Expanded(
              flex: 1,
              child: Container(
                decoration: const BoxDecoration(
                    border: Border(
                  left: BorderSide(
                      width: 1.0, color: CooperadoColors.greenLight3),
                )),
                child: Padding(
                  padding: const EdgeInsets.only(left: 5),
                  child: _resultado(demonstrativoResultadosVO.noticias![3]),
                ),
              ),
            ),
          ],
        )
      ],
    );
  }

  Widget _resultado(noticia) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 5),
      child: Text(
        "${DateFormat('dd/MM/yyyy').format(noticia.data)}   ${noticia.assunto.toString().toUpperCase()}",
        maxLines: 1,
        overflow: TextOverflow.ellipsis,
        style: const TextStyle(fontSize: 12),
      ),
    );
  }
}
