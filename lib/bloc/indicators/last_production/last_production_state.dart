part of 'last_production_cubit.dart';

abstract class LastProductionState extends Equatable {
  const LastProductionState();

  @override
  List<Object?> get props => [];
}

class InitialLastProduction extends LastProductionState {}

class LoadingLastProductionState extends LastProductionState {
  @override
  List<Object> get props => [];
}

class ErrorLastProductionState extends LastProductionState {
  final String message;
  @override
  List<Object?> get props => [message];
  const ErrorLastProductionState(this.message);
}

class LoadedLastProductionState extends LastProductionState {
  final DateTime lastDate;
  @override
  List<Object> get props => [lastDate];

  const LoadedLastProductionState(this.lastDate);
}
