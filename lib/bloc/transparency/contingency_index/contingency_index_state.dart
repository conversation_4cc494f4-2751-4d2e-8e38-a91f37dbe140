part of 'contingency_index_cubit.dart';

abstract class ContingencyIndexState extends Equatable {
  const ContingencyIndexState();
}

class InitialContingencyIndexState extends ContingencyIndexState {
  @override
  List<Object> get props => [];
}

class Loading<PERSON>ontingencyIndexState extends ContingencyIndexState {
  @override
  List<Object> get props => [];
}

class ErrorContingencyIndexState extends ContingencyIndexState {
  final String? message;
  @override
  List<Object?> get props => [message];
  const ErrorContingencyIndexState(this.message);
}

class LoadedContingencyIndexState extends ContingencyIndexState {
  final VOIndicatorModel contingencyIndex;
  @override
  List<Object> get props => [contingencyIndex];

  const LoadedContingencyIndexState(this.contingencyIndex);
}
