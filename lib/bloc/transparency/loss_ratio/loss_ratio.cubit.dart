import 'package:cooperado_minha_unimed/shared/api/transparencia.api.dart';
import 'package:cooperado_minha_unimed/shared/locator.dart';
import 'package:cooperado_minha_unimed/shared/vo/transparencia/indices.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

part 'loss_ratio_state.dart';

class LossRatioCubit extends Cubit<LossRatioState> {
  LossRatioCubit() : super(InitialLossRatioState());

  getLossRatio() async {
    try {
      emit(LoadingLossRatioState());
      final VOIndicatorModel lossRatio = await Locator.instance!
              <TransparenciaApi>()
          .getIndices('SINISTRALIDADE');
      debugPrint("====== ${lossRatio.last}");
      emit(LoadedLossRatioState(lossRatio));
    } catch (e) {
      emit(ErrorLossRatioState(e.toString()));
    }
  }
}
