import 'package:cooperado_minha_unimed/colors.dart';
import 'package:cooperado_minha_unimed/models/service_historic.model.dart';
import 'package:cooperado_minha_unimed/screens/indicadores/graphics/linegraph_qtdhistory.dart';
import 'package:flutter/material.dart';

class LineChartComparative extends StatefulWidget {
  final ResponseServiceHistoric? data;
  final bool? animate;
  final int? dataToShow;
  const LineChartComparative({
    super.key,
    this.data,
    this.animate,
    this.dataToShow,
  });

  @override
  LineChartComparativeState createState() => LineChartComparativeState();
}

class LineChartComparativeState extends State<LineChartComparative> {
  List<Valores> dataPrestador = [];
  List<Valores> dataEspecialidade = [];
  String? valuePrestadorSelected;
  String? valueEspecialidadeSelected;
  Widget? lineChart;

  @override
  void initState() {
    _createChartValues();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        (valuePrestadorSelected != null && valueEspecialidadeSelected != null)
            ? Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Text(
                    'Cooperado: $valuePrestadorSelected',
                    style: const TextStyle(
                        color: CooperadoColors.green,
                        fontWeight: FontWeight.bold),
                  ),
                  Text(
                    'Média Especialidade: $valueEspecialidadeSelected',
                    style: const TextStyle(
                        color: CooperadoColors.tealGreen,
                        fontWeight: FontWeight.bold),
                  )
                ],
              )
            : Container(),
        SizedBox(
          height: 220,
          child: LineGraphQuantityHistory(
              dataMediaEspecialidade: dataEspecialidade,
              dataPrestador: dataPrestador), //lineChart ??= _lineChart(),
        ),
      ],
    );
  }

/* 
  _errorWidget() => ErrorBanner(
        message: 'Não há dados no momento.',
        backgroundColor: Colors.white,
      ); */

  /// Create series list with multiple series
  _createChartValues() {
    int index = 0;
    dataPrestador = [];

    // TODO pensar numa forma de usar o bloc pra gerenciar o estado de erro
    final providerHistory =
        widget.data!.filterProviderHistoryByType(widget.dataToShow);
    for (HistoricoServico d in providerHistory) {
      final label =
          '${mesesAbbreviation[int.parse(d.mesPgto!) - 1]}/${d.ano2Digitos}';
      dataPrestador.add(
          Valores(label, double.parse(d.total!).toStringAsFixed(2), index));
      debugPrint('Total: ${double.parse(d.total!)}');
      index++;
    }

    index = 0;
    dataEspecialidade = [];

    final specialtyHistory =
        widget.data!.filterSpecialtyHistoryByType(widget.dataToShow);

    for (HistoricoServico d in specialtyHistory) {
      final label =
          '${mesesAbbreviation[int.parse(d.mesPgto!) - 1]}/${d.ano2Digitos}';

      dataEspecialidade.add(
          Valores(label, double.parse(d.media!).toStringAsFixed(2), index));
      debugPrint('media: ${double.parse(d.media!)}');
      index++;
    }
  }
}

const mesesAbbreviation = [
  "JAN",
  "FEV",
  "MAR",
  "ABR",
  "MAI",
  "JUN",
  "JUL",
  "AGO",
  "SET",
  "OUT",
  "NOV",
  "DEZ"
];

class Valores {
  final String mes;
  final String valor;
  final int index;

  Valores(this.mes, this.valor, this.index);
}
