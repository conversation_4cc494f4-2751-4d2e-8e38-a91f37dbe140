import 'package:cooperado_minha_unimed/bloc/auth/auth_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/res-internal/solicitations/res_solicitation_cubit.dart';
import 'package:cooperado_minha_unimed/colors.dart';
import 'package:cooperado_minha_unimed/screens/res-internal/res_solicitation.dart';
import 'package:cooperado_minha_unimed/screens/res-internal/widgets/card_res.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class ResInternalScreen extends StatefulWidget {
  const ResInternalScreen({
    super.key,
  });
  @override
  ResInternalScreenState createState() => ResInternalScreenState();
}

class ResInternalScreenState extends State<ResInternalScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Histórico de Saúde'),
      ),
      backgroundColor: CooperadoColors.grayLight,
      body: Padding(
        padding: const EdgeInsets.all(10.0),
        child: GridView.count(
          crossAxisCount: 2,
          childAspectRatio: 1.4,
          children: [
            CardRes(
              icon: const Icon(
                Icons.science,
                size: 32,
                color: CooperadoColors.green,
              ),
              title: "Solicitações",
              onClick: () {
                context.read<ResSolicitationCubit>().listResSolicitations(
                    crm: context.read<AuthCubit>().credentials.crm);

                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const ResSolicitationScreen(),
                  ),
                );
              },
            ),
            // CardRes(
            //   icon: Icon(
            //     Icons.access_alarm,
            //     size: 32,
            //   ),
            //   title: 'Card 2',
            //   onClick: () {},
            // ),
          ],
        ),
      ),
    );
  }
}
