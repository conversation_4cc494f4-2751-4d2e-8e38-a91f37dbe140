// ignore_for_file: library_private_types_in_public_api

import 'package:cooperado_minha_unimed/colors.dart';
import 'package:cooperado_minha_unimed/shared/screen-transitions/fade.transition.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/material.dart';

import '../../shared/widgets/pdf_view/pdf_view_platform.dart';

const String urlConsentimentoCirurgico =
    'https://www.unimedfortaleza.com.br/portaluploads/uploads/2020/06/Termo-de-Consentimento-Cir%C3%BArgico.pdf';
const String urlConsentimentoAnestesico =
    'https://www.unimedfortaleza.com.br/portaluploads/uploads/2020/06/Termo-de-Consentimento-Anest%C3%A9sico.pdf';

class ConsentTermsScreen extends StatefulWidget {
  final FirebaseAnalytics analytics;
  final FirebaseAnalyticsObserver observer;

  const ConsentTermsScreen(
      {super.key, required this.analytics, required this.observer});

  @override
  ConsentTermsScreenState createState() => ConsentTermsScreenState();
}

class ConsentTermsScreenState extends State<ConsentTermsScreen> {
  @override
  void initState() {
    super.initState();
    widget.analytics.logScreenView(
      screenName: 'Termos de Consentimento',
      screenClass: 'ConsentTermsScreen',
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        centerTitle: true,
        title: const Text("Termos de consentimentos"),
      ),
      body: SafeArea(child: _body()),
    );
  }

  Widget _body() {
    return Padding(
        padding: const EdgeInsets.all(8.0),
        child: Card(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: <Widget>[
              const Padding(
                padding: EdgeInsets.all(8.0),
                child: Text(
                  "Termos de consentimentos",
                  style: TextStyle(
                      color: CooperadoColors.tealGreen,
                      fontSize: 16,
                      fontWeight: FontWeight.bold),
                ),
              ),
              ListTile(
                onTap: _pressedCirurgico,
                leading: Container(
                  padding: const EdgeInsets.all(8.0),
                  decoration: const BoxDecoration(
                    color: CooperadoColors.tealGreen,
                    borderRadius: BorderRadius.all(Radius.elliptical(50, 50)),
                  ),
                  child: const Icon(
                    Icons.assignment,
                    color: CooperadoColors.grayLight,
                  ),
                ),
                title: const Text('Termo de consentimento cirúrgico'),
              ),
              ListTile(
                onTap: _pressedAnestesico,
                leading: Container(
                  padding: const EdgeInsets.all(8.0),
                  decoration: const BoxDecoration(
                    color: CooperadoColors.tealGreen,
                    borderRadius: BorderRadius.all(Radius.elliptical(50, 50)),
                  ),
                  child: const Icon(
                    Icons.assignment,
                    color: CooperadoColors.grayLight,
                  ),
                ),
                title: const Text('Termo de consentimento anestésico'),
              ),
            ],
          ),
        ));
  }

  void _pressedCirurgico() async {
    Navigator.push(
        context,
        FadeRoute(
            page: const PDFViewPlatform(
          urlConsentimentoCirurgico,
          share: true,
          filename: 'consentimento_cirurgico.pdf',
          title: 'Termo de consentimento cirúrgico',
        )));
  }

  void _pressedAnestesico() async {
    Navigator.push(
        context,
        FadeRoute(
            page: const PDFViewPlatform(
          urlConsentimentoAnestesico,
          share: true,
          filename: 'consentimento_anestésico.pdf',
          title: 'Termo de consentimento anestésico',
        )));
  }
}
