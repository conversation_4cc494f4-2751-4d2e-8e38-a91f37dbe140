import 'package:cooperado_minha_unimed/bloc/indicators/last_production/last_production_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/servicos/ir-declaration/ir_declaration_cubit.dart';
import 'package:cooperado_minha_unimed/colors.dart';
import 'package:cooperado_minha_unimed/shared/screen-transitions/fade.transition.dart';
import 'package:cooperado_minha_unimed/shared/widgets/error-banner/error_banner.component.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';

import '../../../shared/widgets/pdf_view/pdf_view_platform.dart';

class CardIRDeclaration extends StatefulWidget {
  const CardIRDeclaration({super.key});

  @override
  CardIRDeclarationState createState() => CardIRDeclarationState();
}

class CardIRDeclarationState extends State<CardIRDeclaration> {
  late DateTime lastProduction;
  @override
  void initState() {
    lastProduction =
        context.read<LastProductionCubit>().lastDate ?? DateTime.now();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      child: Container(
        padding: const EdgeInsets.all(10),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: <Widget>[
            const Text(
              "Demonstrativo de Imposto de Renda",
              style: TextStyle(
                  color: CooperadoColors.blackText,
                  fontSize: 16,
                  fontWeight: FontWeight.bold),
            ),
            Text(
                'Imposto de Renda exercício ${lastProduction.year}\nano calendário ${(lastProduction.year) - 1}'),
            const SizedBox(height: 10),
            _errorWidget(),
            const SizedBox(height: 10),
            _buildButtonConsult()
          ],
        ),
      ),
    );
  }

  Widget _buildButtonConsult() {
    return BlocConsumer<IRDeclarationCubit, IRDeclarationState>(
      listener: (context, state) {
        if (state is DoneGetIRDeclarationState) {
          _consultar(state.link);
        }
      },
      builder: (context, state) {
        if (state is LoadingGetIRDeclarationState) {
          return Row(
            children: <Widget>[
              Expanded(
                  child: ElevatedButton(
                style: ElevatedButton.styleFrom(
                  backgroundColor: CooperadoColors.tealGreen,
                ),
                onPressed: () {},
                child: const SpinKitThreeBounce(
                  color: Colors.white,
                  size: 20,
                ),
              )),
            ],
          );
        } else {
          return Row(
            children: <Widget>[
              Expanded(
                  child: ElevatedButton(
                style: ElevatedButton.styleFrom(
                  backgroundColor: CooperadoColors.tealGreen,
                ),
                onPressed: () => context
                    .read<IRDeclarationCubit>()
                    .getListIRDeclarationEvent(),
                child: const Text("CONSULTAR"),
              )),
            ],
          );
        }
      },
    );
  }

  _consultar(url) {
    Navigator.push(
        context,
        FadeRoute(
            page: PDFViewPlatform(
          url,
          share: true,
          filename: 'relatorio.pdf',
          title: 'Imposto de renda',
        )));
    //  launch(url);
  }

  Widget _errorWidget() {
    return BlocBuilder<IRDeclarationCubit, IRDeclarationState>(
      builder: (context, state) {
        if (state is ErrorGetIRDeclarationState) {
          return Center(child: ErrorBanner(message: state.message));
        } else {
          return Container();
        }
      },
    );
  }
}
