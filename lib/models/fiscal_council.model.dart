class FiscalCouncil {
  String nome;
  String cargo;
  String crm;
  String? especialidade;
  String? imagem;
  String? thumb;

  FiscalCouncil({
    required this.nome,
    required this.cargo,
    required this.crm,
    this.especialidade,
    this.imagem,
    this.thumb,
  });

  FiscalCouncil.fromJson(Map<String, dynamic> json)
      : nome = json['nome'],
        cargo = json['cargo'],
        crm = json['crm'],
        especialidade = json['especialidade'],
        //Adicionado timestamp nas imagens para evitar cache
        imagem = json['imagem'] + "?t=${DateTime.now().millisecondsSinceEpoch}",
        thumb = json['thumb'] + "?t=${DateTime.now().millisecondsSinceEpoch}";

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['nome'] = nome;
    data['cargo'] = cargo;
    data['crm'] = crm;
    data['especialidade'] = especialidade;
    data['imagem'] = imagem;
    data['thumb'] = thumb;
    return data;
  }
}

class CouncilTopics {
  int? codigo;
  String? descricao;
  Servico? servico;

  CouncilTopics({this.codigo, this.descricao, this.servico});

  CouncilTopics.fromJson(Map<String, dynamic> json) {
    codigo = json['codigo'];
    descricao = json['descricao'];
    servico =
        json['servico'] != null ? Servico.fromJson(json['servico']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['codigo'] = codigo;
    data['descricao'] = descricao;
    if (servico != null) {
      data['servico'] = servico!.toJson();
    }
    return data;
  }
}

class Servico {
  int? codigo;
  String? descricao;

  Servico({this.codigo, this.descricao});

  Servico.fromJson(Map<String, dynamic> json) {
    codigo = json['codigo'];
    descricao = json['descricao'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['codigo'] = codigo;
    data['descricao'] = descricao;
    return data;
  }
}
