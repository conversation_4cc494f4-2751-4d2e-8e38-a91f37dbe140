import 'package:package_info/package_info.dart';

class VersionInfo {
  final String appName;
  final String packageName;
  final String version;
  final String buildNumber;

  VersionInfo({
    required this.appName,
    required this.packageName,
    required this.version,
    required this.buildNumber,
  });
}

class VersionService {
  VersionInfo? _info;
  VersionInfo? get info => _info;

  Future<VersionInfo> getInfo() async {
    try {
      final packageInfo = await PackageInfo.fromPlatform();

      _info ??= VersionInfo(
        appName: packageInfo.appName,
        packageName: packageInfo.packageName,
        version: packageInfo.version,
        buildNumber: packageInfo.buildNumber,
      );
    } catch (ex) {
      _info = null;

      return VersionInfo(
        appName: 'ND',
        packageName: 'ND',
        version: 'ND',
        buildNumber: 'ND',
      );
    }

    return _info!;
  }
}
