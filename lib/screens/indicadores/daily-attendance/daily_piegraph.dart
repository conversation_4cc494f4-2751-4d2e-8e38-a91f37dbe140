// ignore_for_file: public_member_api_docs, sort_constructors_first

import 'package:cooperado_minha_unimed/bloc/sensitive-data/sensitive_data_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/sensitive-data/sensitive_data_state.dart';
import 'package:cooperado_minha_unimed/screens/indicadores/daily-attendance/daily_graphic.dart';
import 'package:cooperado_minha_unimed/shared/widgets/graphics/indicator.dart';
import 'package:evaluation/colors.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class PieGraph extends StatelessWidget {
  const PieGraph({
    super.key,
    required this.seriesDaily,
  });
  final List<DailyAttendanceSerie> seriesDaily;
  // sunAmount

  List<PieChartSectionData> showingSections(bool isSensitiveData) {
    const radius = 60.0;
    const shadows = [Shadow(color: Colors.black, blurRadius: 2)];

    final data = [
      PieChartSectionData(
        color:  !isSensitiveData ? UnimedColors.grayDark : seriesDaily[0].barColor,
        value: seriesDaily[0].quantity ?? 0,
        title: '',
        radius: radius,
        titleStyle: const TextStyle(
          fontWeight: FontWeight.bold,
          color: Colors.white,
          shadows: shadows,
        ),
      ),
      PieChartSectionData(
        color: !isSensitiveData ? UnimedColors.grayDark : seriesDaily[1].barColor,
        value: seriesDaily[1].quantity ?? 0,
        title:  '',
        radius: radius,
        titleStyle: const TextStyle(
          fontWeight: FontWeight.bold,
          color: Colors.white,
          shadows: shadows,
        ),
      ),
      PieChartSectionData(
        color: !isSensitiveData ? UnimedColors.grayDark : seriesDaily[2].barColor,
        value: seriesDaily[2].quantity ?? 0,
        title: '',
        radius: radius,
        titleStyle: const TextStyle(
          fontWeight: FontWeight.bold,
          color: Colors.white,
          shadows: shadows,
        ),
      ),
    ];

    return data;
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<SensitiveDataCubit, SensitiveDataState>(
      builder: (context, state) {
         final isSensitiveData = state.isSensitiveDataVisible;

        return Padding(
          padding: const EdgeInsets.symmetric(horizontal: 8.0),
          child: Row(
            children: [
              Expanded(
                child: PieChart(
                  PieChartData(
                    borderData: FlBorderData(
                      show: false,
                    ),
                    sectionsSpace: 0,
                    startDegreeOffset: 270,
                    centerSpaceRadius: 0,
                    sections:  showingSections(isSensitiveData),
                  ),
                ),
              ),
               Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: <Widget>[
                Indicator(
                  color:  !isSensitiveData
                      ? UnimedColors.grayDark : seriesDaily[0].barColor,
                  text: !isSensitiveData
                      ? '${seriesDaily[0].title} (***)'
                      : '${seriesDaily[0].title} (${seriesDaily[0].quantity?.toInt()})',
                  isSquare: false,
                ),
                const SizedBox(
                  height: 6,
                ),
                Indicator(
                  color:  !isSensitiveData
                      ? UnimedColors.grayDark : seriesDaily[1].barColor,
                  text:! isSensitiveData
                      ? '${seriesDaily[1].title} (***)'
                      : '${seriesDaily[1].title} (${seriesDaily[1].quantity?.toInt()})',
                  isSquare: false,
                ),
                const SizedBox(
                  height: 6,
                ),
                Indicator(
                  color:  !isSensitiveData
                      ? UnimedColors.grayDark : seriesDaily[2].barColor,
                  text: !isSensitiveData
                      ? '${seriesDaily[2].title} (***)'
                      : '${seriesDaily[2].title} (${seriesDaily[2].quantity?.toInt()})',
                  isSquare: false,
                ),
                const SizedBox(
                  height: 6,
                ),
              ],
            ),
            ],
          ),
        );
      },
    );
  }
}
