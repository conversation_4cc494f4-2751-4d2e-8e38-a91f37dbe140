import 'package:bloc/bloc.dart';
import 'package:cooperado_minha_unimed/shared/api/indicadores.api.dart';
import 'package:cooperado_minha_unimed/shared/locator.dart';
import 'package:equatable/equatable.dart';

part 'last_production_state.dart';

class LastProductionCubit extends Cubit<LastProductionState> {
  LastProductionCubit() : super(InitialLastProduction());

  DateTime? _lastDate;
  DateTime? get lastDate => _lastDate;

  listLastProduction() async {
    try {
      emit(LoadingLastProductionState());
      final formattedString = (await Locator.instance!<IndicadoresApi>().getLastProduction())!.split('/');

      _lastDate = DateTime.parse('${formattedString[2]}-${formattedString[1]}-${formattedString[0]}');
      emit(LoadedLastProductionState(_lastDate!));
    } catch (e) {
      emit(ErrorLastProductionState(e.toString()));
    }
  }

  clearNewDate() async {
    _lastDate = null;
  }
}
