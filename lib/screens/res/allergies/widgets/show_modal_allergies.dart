import 'package:cooperado_minha_unimed/bloc/res/allergies/res_allergies_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/res/configs/res_configs_cubit.dart';
import 'package:cooperado_minha_unimed/colors.dart';
import 'package:cooperado_minha_unimed/shared/widgets/elevated_button_custom.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class AllergySelection extends StatefulWidget {
  const AllergySelection({super.key});

  @override
  AllergySelectionState createState() => AllergySelectionState();
}

class AllergySelectionState extends State<AllergySelection> {
  final TextEditingController _controller = TextEditingController();

  final List<String> _selectedAllergies = [];
  bool _selectAll = false;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: <Widget>[
        GestureDetector(
          onTap: _showAllergySelectionModal,
          child: AbsorbPointer(
            child: TextForm<PERSON>ield(
              controller: _controller,
              readOnly: true,
              canRequestFocus: false,
              decoration: InputDecoration(
                hintText: 'Selecione',
                fillColor: Colors.white,
                hintStyle: const TextStyle(
                  color: CooperadoColors.grayLight7,
                ),
                filled: true,
                border: const OutlineInputBorder(
                  borderSide: BorderSide(
                    color: CooperadoColors.grayLight3,
                  ),
                ),
                enabledBorder: const OutlineInputBorder(
                  borderSide: BorderSide(
                    color: CooperadoColors.grayLight3,
                  ),
                ),
                focusedBorder: const OutlineInputBorder(
                  borderSide: BorderSide(
                    color: CooperadoColors.grayLight3,
                  ),
                ),
                suffixIcon: IconButton(
                  icon: const Icon(
                    Icons.keyboard_arrow_down,
                    color: CooperadoColors.grayLight3,
                  ),
                  onPressed: _showAllergySelectionModal,
                ),
              ),
            ),
          ),
        ),
        const SizedBox(height: 8),
        Wrap(
          spacing: 8.0,
          runSpacing: 4.0,
          children: _selectedAllergies.isEmpty
              ? []
              : _selectedAllergies.map((allergy) {
                  return Chip(
                    label: Text(
                      _getSelectedAllergies(
                              alerfiesCode: allergy, context: context) ??
                          allergy,
                      style: const TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                        color: CooperadoColors.greenDark,
                      ),
                    ),
                    deleteIcon: const Icon(
                      Icons.close,
                      color: CooperadoColors.greenDark,
                    ),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(2),
                    ),
                    backgroundColor: CooperadoColors.green3,
                    onDeleted: () {
                      setState(() {
                        _selectedAllergies.remove(allergy);
                        if (_selectedAllergies.isEmpty) {
                          _selectAll = false;
                        }
                      });

                      context.read<ResAllergieCubit>().searchCategoryAllergies(
                            categories: _selectedAllergies,
                          );
                           setState(() {
        if (_selectedAllergies.length !=
            context.read<ResConfigCubit>().resConfigModel.allergies.categories!.length) {
          _selectAll = false;
        }
      });
                    },
                  );
                }).toList(),
        ),
      ],
    );
  }

  void _showAllergySelectionModal() {
    showModalBottomSheet<void>(
      context: context,
      backgroundColor: Colors.white,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(20),
        ),
      ),
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (BuildContext context, StateSetter setModalState) {
            return SafeArea(
              child: Theme(
                data: Theme.of(context).copyWith(
                  unselectedWidgetColor: CooperadoColors.grayLight3,
                ),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: <Widget>[
                    Center(
                      child: Container(
                        height: 4,
                        width: 100,
                        margin: const EdgeInsets.symmetric(
                          vertical: 16,
                        ),
                        decoration: BoxDecoration(
                          color: CooperadoColors.grayLight3,
                          borderRadius: BorderRadius.circular(2),
                        ),
                      ),
                    ),
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 16.0),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          const Text(
                            'Categorias de alergias',
                            style: TextStyle(
                              fontSize: 15,
                              fontWeight: FontWeight.bold,
                              color: CooperadoColors.blackText,
                            ),
                            textAlign: TextAlign.left,
                          ),
                          Checkbox(
                            materialTapTargetSize:
                                MaterialTapTargetSize.shrinkWrap,
                            value: _selectAll,
                            onChanged: (bool? value) {
                              setModalState(() {
                                _selectAll = value ?? false;
                                if (_selectAll) {
                                  _selectedAllergies.clear();
                                  _selectedAllergies.addAll(context
                                      .read<ResConfigCubit>()
                                      .resConfigModel
                                      .allergies
                                      .categories!
                                      .map((e) => e.code!));
                                } else {
                                  _selectedAllergies.clear();
                                }
                              });
                            },
                          )
                        ],
                      ),
                    ),
                    const SizedBox(height: 24),
                    Expanded(
                      child: Stack(
                        children: [
                          ListView(
                            padding: EdgeInsets.only(
                              bottom: MediaQuery.of(context).size.height * 0.1,
                            ),
                            children: context
                                .read<ResConfigCubit>()
                                .resConfigModel
                                .allergies
                                .categories!
                                .map((allergy) {
                              return Column(
                                children: [
                                  CheckboxListTile(
                                    title: Text(
                                      '${allergy.description!} (${allergy.code})',
                                      style: const TextStyle(
                                        fontSize: 14,
                                        fontWeight: FontWeight.w400,
                                      ),
                                    ),
                                    value: _selectedAllergies
                                        .contains(allergy.code),
                                    onChanged: (bool? value) {
                                      setModalState(() {
                                        if (value == true) {
                                          _selectedAllergies.add(allergy.code!);
                                        } else {
                                          _selectedAllergies
                                              .remove(allergy.code);
                                        }

                                        if (_selectedAllergies.length ==
                                            context
                                                .read<ResConfigCubit>()
                                                .resConfigModel
                                                .allergies
                                                .categories!
                                                .length) {
                                          _selectAll = true;
                                        } else {
                                          _selectAll = false;
                                        }
                                      });
                                    },
                                  ),
                                  const Divider(height: 0),
                                ],
                              );
                            }).toList(),
                          ),
                          Positioned(
                            bottom: 0,
                            left: 0,
                            right: 0,
                            child: Container(
                              color: Colors.white,
                              margin: EdgeInsets.zero,
                              padding: EdgeInsets.zero,
                              child: Padding(
                                padding: const EdgeInsets.only(
                                  left: 14,
                                  right: 14,
                                  bottom: 20,
                                ),
                                child: ElevatedButtonCustom(
                                  onPressed: () {
                                    Navigator.pop(context);

                                    context
                                        .read<ResAllergieCubit>()
                                        .searchCategoryAllergies(
                                          categories: _selectedAllergies,
                                        );
                                  },
                                  color: CooperadoColors.green2,
                                  title: 'Aplicar',
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            );
          },
        );
      },
    ).whenComplete(() {
      setState(() {
        if (_selectedAllergies.length !=
            context.read<ResConfigCubit>().resConfigModel.allergies.categories!.length) {
          _selectAll = false;
        }
      });
    });
  }

  String? _getSelectedAllergies(
      {required BuildContext context, required String alerfiesCode}) {
    final String allergies = context
        .read<ResConfigCubit>()
        .resConfigModel
        .allergies
        .categories!
        .firstWhere((element) => element.code == alerfiesCode)
        .description!;

    return allergies;
  }
}
