import 'package:cooperado_minha_unimed/colors.dart';
import 'package:cooperado_minha_unimed/shared/widgets/month_picker.dart';
import 'package:flutter/material.dart';

class ChooseDateWidget extends StatefulWidget {
  final DateTime? date;
  final Widget textBox;
  final Function(DateTime?)? onPressed;
  final bool alignCenter;
  final DateTime? firstDate;
  final DateTime? lastDate;

  const ChooseDateWidget(
      {super.key,
      required this.textBox,
      required this.onPressed,
      this.date,
      this.alignCenter = false,
      this.firstDate,
      this.lastDate});
  @override
  ChooseDateWidgetState createState() => ChooseDateWidgetState();
}

class ChooseDateWidgetState extends State<ChooseDateWidget> {
  @override
  Widget build(BuildContext context) {
    final DateTime now = DateTime.now();
    return Row(
      mainAxisAlignment:
          widget.alignCenter ? MainAxisAlignment.center : MainAxisAlignment.end,
      children: [
        InkWell(
          onTap: () => showMonthPicker(
            context: context,
            firstDate: widget.firstDate ?? DateTime(now.year, now.month - 12),
            initialDate: widget.date ?? now,
            lastDate: widget.lastDate ?? DateTime(now.year, now.month - 1),
          ),
          child: Container(
            alignment: Alignment.centerRight,
            padding: const EdgeInsets.only(bottom: 8.0, right: 8.0),
            child: Container(
              padding: const EdgeInsets.all(4.0),
              decoration: BoxDecoration(
                  border: Border.all(color: CooperadoColors.grayLight3),
                  borderRadius: BorderRadius.circular(5)),
              child: Row(
                children: <Widget>[
                  const Icon(
                    Icons.calendar_today,
                    color: CooperadoColors.grayDark,
                  ),
                  widget.textBox,
                  const Icon(Icons.keyboard_arrow_down_outlined,
                      color: CooperadoColors.tealGreen)
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }

  Future<DateTime?> showMonthPicker({
    required BuildContext context,
    required DateTime initialDate,
    DateTime? firstDate,
    DateTime? lastDate,
  }) async {
    return await showDialog<DateTime>(
      context: context,
      builder: (context) => MonthPickerCustom(
          initialDate: initialDate,
          firstDate: firstDate,
          lastDate: lastDate,
          onPressed: widget.onPressed,
      )
    );
  }
}
