import 'package:bloc/bloc.dart';
import 'package:cooperado_minha_unimed/bloc/e-card/finish/finish_state.dart';
import 'package:cooperado_minha_unimed/models/e-card/finish.model.dart';
import 'package:cooperado_minha_unimed/shared/api/graphqlecard.api.dart';
import 'package:cooperado_minha_unimed/shared/locator.dart';

class EcardFinishCubit extends Cubit<EcardFinishState> {
  EcardFinishCubit() : super(InitialEcardFinishState());

  late EcardFinishCardModel _ecardFinishCardModel;
  EcardFinishCardModel get ecardFinishCardModel => _ecardFinishCardModel;

  void finishEcard({
    required String codPrestador,
  }) async {
    try {
      emit(LoadingEcardFinishState());

      _ecardFinishCardModel =
          await Locator.instance!.get<GraphQlApiEcard>().ecardFinish(
                codPrestador: codPrestador,
              );

      emit(LoadedEcardFinishState(ecardFinishCardModel: _ecardFinishCardModel));
    } catch (e) {
      emit(ErrorEcardFinishState(message: e.toString()));
    }
  }
}
