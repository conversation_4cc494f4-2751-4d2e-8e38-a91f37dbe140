import 'package:bloc/bloc.dart';
import 'package:cooperado_minha_unimed/models/office_notices.model.dart';
import 'package:cooperado_minha_unimed/shared/api/office_notices.api.dart';
import 'package:cooperado_minha_unimed/shared/locator.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';

part 'office_notices_state.dart';

class OfficeNoticesCubit extends Cubit<OfficeNoticesState> {
  OfficeNoticesCubit() : super(OfficeNoticesInitial());

  getAllOfficeNotices(
      {required String codPrestador,
      required bool showHideNotifications,
      required int page,
      required int pageSize}) async {
    try {
      emit(LoadingOfficeNoticesState());
      final officeNotices =
          await Locator.instance!<OfficeNoticesApi>().getOfficeNotices(
        codPrestador: codPrestador,
        showHideNotifications: showHideNotifications,
        page: page,
        pageSize: pageSize,
      );

      if (officeNotices.isEmpty) {
        emit(EmptyOfficeNoticesState());
      } else {
        emit(LoadedListOfficeNoticesState(officeNotices: officeNotices));
      }
    } catch (ex) {
      debugPrint(ex.toString());
      emit(ErrorOfficeNoticesState('$ex'));
    }
  }

  readNotification(
      {required String codPrestador, required int codNotificacao}) async {
    try {
      emit(LoadingOfficeNoticesState());

      await Locator.instance!.get<OfficeNoticesApi>().readNotification(
            crm: codPrestador,
            codNotificacao: codNotificacao,
          );

      emit(DoneReadNotificationState());
    } catch (ex) {
      debugPrint(ex.toString());
      emit(ErrorOfficeNoticesState('$ex'));
    }
  }

  hideNotification(
      {required String codPrestador, required int codNotificacao}) async {
    try {
      emit(LoadingOfficeNoticesState());

      await Locator.instance!.get<OfficeNoticesApi>().hideNotification(
            crm: codPrestador,
            codNotificacao: codNotificacao,
          );

      emit(DoneHideNotificationState());
    } catch (ex) {
      debugPrint(ex.toString());
      emit(ErrorOfficeNoticesState('$ex'));
    }
  }
}
