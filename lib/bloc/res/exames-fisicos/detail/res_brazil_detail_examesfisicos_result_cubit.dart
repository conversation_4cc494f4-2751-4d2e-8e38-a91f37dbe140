import 'package:cooperado_minha_unimed/models/res/res_brasil_detail_exam_phisical_model.dart';
import 'package:equatable/equatable.dart';

abstract class ResBrazilDetailExamesFisicosResultState extends Equatable {
  const ResBrazilDetailExamesFisicosResultState();

  @override
  List<Object> get props => [];
}

class InitialResBrazilDetailExamesFisicosResultState
    extends ResBrazilDetailExamesFisicosResultState {}

class LoadingResBrazilDetailExamesFisicosResultState
    extends ResBrazilDetailExamesFisicosResultState {
  @override
  List<Object> get props => [];
}

class NoDataResBrazilDetailExamesFisicosResultState
    extends ResBrazilDetailExamesFisicosResultState {
  @override
  List<Object> get props => [];
}

class ErrorResBrazilDetailExamesFisicosResultState
    extends ResBrazilDetailExamesFisicosResultState {
  final String message;

  @override
  List<Object> get props => [message];

  const ErrorResBrazilDetailExamesFisicosResultState({required this.message});
}

class LoadedResBrazilDetailExamesFisicosResultState
    extends ResBrazilDetailExamesFisicosResultState {
  final List<ResDetailExameFisicoModel> detailExamesFisicos;

  @override
  List<Object> get props => [detailExamesFisicos];

  const LoadedResBrazilDetailExamesFisicosResultState({
    required this.detailExamesFisicos,
  });
}
