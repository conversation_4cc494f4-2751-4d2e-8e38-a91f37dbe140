part of 'my_invoices_cubit.dart';

abstract class MyInvoicesState extends Equatable {
  const MyInvoicesState();

  @override
  List<Object?> get props => [];
}

class MyInvoicesInitial extends MyInvoicesState {}

class LoadingMyInvoicesState extends MyInvoicesState {
  @override
  List<Object> get props => [];
}

class LoadedMyInvoicesState extends MyInvoicesState {
  final List<InvoiceModel> list;
  @override
  List<Object?> get props => [list];
  const LoadedMyInvoicesState({required this.list});
}

class NoDataMyInvoicesState extends MyInvoicesState {
  @override
  List<Object?> get props => [];
  const NoDataMyInvoicesState();
}

class ErrorMyInvoicesState extends MyInvoicesState {
  final String message;
  @override
  List<Object> get props => [message];

  const ErrorMyInvoicesState(this.message);
}
