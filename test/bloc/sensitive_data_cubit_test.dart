import 'package:cooperado_minha_unimed/bloc/sensitive-data/sensitive_data_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/sensitive-data/sensitive_data_state.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('SensitiveDataCubit', () {
    late SensitiveDataCubit sensitiveDataCubit;

    setUp(() {
      sensitiveDataCubit = SensitiveDataCubit();
    });

    tearDown(() {
      sensitiveDataCubit.close();
    });

    test('initial state is SensitiveDataState with isSensitiveDataVisible as true', () {
      expect(sensitiveDataCubit.state, const SensitiveDataState(isSensitiveDataVisible: true));
    });

    test('toggleSensitiveDataVisibility changes isSensitiveDataVisible from false to true', () {
      sensitiveDataCubit.toggleSensitiveDataVisibility();
      expect(sensitiveDataCubit.state.isSensitiveDataVisible, false);
    });

    test('toggleSensitiveDataVisibility changes isSensitiveDataVisible from true to false', () {
      sensitiveDataCubit.emit(const SensitiveDataState(isSensitiveDataVisible: true));
      sensitiveDataCubit.toggleSensitiveDataVisibility();
      expect(sensitiveDataCubit.state.isSensitiveDataVisible, false);
    });
  });
}