import 'package:cooperado_minha_unimed/models/res/res_allergie_model.dart';
import 'package:cooperado_minha_unimed/shared/api/graphql-res.api.dart';
import 'package:cooperado_minha_unimed/shared/locator.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

part 'res_allergies_state.dart';

class ResAllergieCubit extends Cubit<ResAllergiesState> {
  ResAllergieCubit() : super(InitialResAllergiesState());

  List<ResAllergieModel> _listAllergies = List.empty(growable: true);
  List<ResAllergieModel> get listAllergies => _listAllergies;

  void listResAllergies({required String crm, required String card}) async {
    try {
      emit(LoadingResAllergiesState());

      _listAllergies = await Locator.instance!<ResGraphQlApi>()
          .resListAllergies(crm: crm, card: card);

      if (_listAllergies.isEmpty) {
        emit(const NoDataResAllergiesState());
        return;
      } else {
        emit(LoadedResAllergiesState(listResAllergies: _listAllergies));
      }
    } catch (e) {
      emit(ErrorResAllergiesState(message: e.toString()));
    }
  }

  void searchCategoryAllergies({required List<String> categories}) async {
    try {
      emit(LoadingResAllergiesSearchState());

      if (categories.isEmpty) {
        emit(const NoDataResAllergiesState());
        return;
      }

      if (categories.last == 'Todos') {
        emit(LoadedResAllergiesState(listResAllergies: _listAllergies));
        return;
      }

      final List<ResAllergieModel> filtredList =
          _listAllergies.where((element) {
        return categories.any((category) => element.categoria
            .toString()
            .toLowerCase()
            .contains(category.toLowerCase()));
      }).toList();

      emit(LoadedResAllergiesState(listResAllergies: filtredList));
    } catch (e) {
      emit(ErrorResAllergiesSearchState(message: e.toString()));
    }
  }
}
