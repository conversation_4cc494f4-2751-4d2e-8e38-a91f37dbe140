import 'package:cooperado_minha_unimed/bloc/sensitive-data/sensitive_data_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/sensitive-data/sensitive_data_state.dart';
import 'package:cooperado_minha_unimed/bloc/transparency/assistance_costs/assistance_costs_cubit.dart';
import 'package:cooperado_minha_unimed/colors.dart';
import 'package:cooperado_minha_unimed/screens/transparencia/widgets/donut_chart.dart';
import 'package:cooperado_minha_unimed/shared/vo/transparencia/custos_assistenciais.vo.dart';
import 'package:cooperado_minha_unimed/shared/widgets/card_refresh.dart';
import 'package:cooperado_minha_unimed/shared/widgets/error-banner/error_banner.component.dart';
import 'package:cooperado_minha_unimed/shared/widgets/graphics/choose_date.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:intl/intl.dart';

class CardCustosAssistenciais extends StatefulWidget {
  final bool isVisible;

  const CardCustosAssistenciais({super.key, this.isVisible = true});

  @override
  CardCustosAssistenciaisState createState() => CardCustosAssistenciaisState();
}

class CardCustosAssistenciaisState extends State<CardCustosAssistenciais> {
  DateTime? _selectedDateTime;

  @override
  void initState() {
    if (widget.isVisible) {
      _selectedDateTime = context.read<AssistanceCostsCubit>().lastDate;
      if (_selectedDateTime != null) {
        context
            .read<AssistanceCostsCubit>()
            .getAssistanceCosts(dateReport: _selectedDateTime);
      } else {
        context.read<AssistanceCostsCubit>().getAssistanceCosts();
      }
    }

    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Visibility(
      visible: widget.isVisible,
      child: CardRefresh(
        title: const Text("Custos Assistenciais",
            style: TextStyle(
              color: CooperadoColors.blackText,
              fontSize: 16,
              fontWeight: FontWeight.bold,
            )),
        refresh: _iconRefresh(),
        child: Column(
          children: [
            _selectDateWidget(),
            BlocBuilder<AssistanceCostsCubit, AssistanceCostsState>(
              builder: (context, state) {
                if (state is LoadedAssistanceCostsState) {
                  return Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: <Widget>[
                        SizedBox(
                          height: 200,
                          child: DonutChart(
                            animate: true,
                            custos: state.custosAssistenciais.custos,
                          ),
                        ),
                        const SizedBox(height: 15),
                        BlocBuilder<SensitiveDataCubit, SensitiveDataState>(
                          builder: (context, stateSensitive) {
                            return Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: _chartSubtitles(
                                  state.custosAssistenciais,
                                  !stateSensitive.isSensitiveDataVisible),
                            );
                          },
                        ),
                        Text(state.custosAssistenciais.observacao ?? ''),
                      ]);
                } else if (state is LoadingAssistanceCostsState) {
                  return const SpinKitCircle(color: CooperadoColors.tealGreen);
                } else if (state is ErrorAssistanceCostsState) {
                  return ErrorBanner(message: state.message);
                } else {
                  return Container();
                }
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _selectDateWidget() {
    return ChooseDateWidget(
      date: _selectedDateTime,
      textBox: Text(DateFormat("MM/yyyy").format(_selectedDateTime!)),
      onPressed: (value) {
        setState(() => _selectedDateTime = value);
        context
            .read<AssistanceCostsCubit>()
            .getAssistanceCosts(dateReport: value);
      },
    );
  }

  Widget _iconRefresh() {
    return BlocBuilder<AssistanceCostsCubit, AssistanceCostsState>(
        builder: (context, state) {
      if (state is ErrorAssistanceCostsState) {
        return InkWell(
            child: const Icon(Icons.refresh),
            onTap: () =>
                context.read<AssistanceCostsCubit>().getAssistanceCosts());
      } else {
        return Container();
      }
    });
  }

  List<Widget> _chartSubtitles(CustosAssistenciaisVO custosAssistenciaisVO,
      bool isSensitiveDataVisible) {
    List<Custo> custos = custosAssistenciaisVO.custos!;
    List<Widget> subtitles = [];

    for (int i = 0; i < custos.length; i++) {
      double porcentagem = custos[i].valor! / custosAssistenciaisVO.total * 100;
      String displayPorcentagem = isSensitiveDataVisible
          ? '*' * porcentagem.toStringAsFixed(2).length
          : porcentagem.toStringAsFixed(2);

      subtitles.add(Row(
        children: <Widget>[
          Container(
            decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(3.0),
                color: CooperadoColors.chartColors[i]),
            width: 12,
            height: 12,
          ),
          Padding(
              padding: const EdgeInsets.only(left: 5, right: 5),
              child: Text(custos[i].descricao!)),
          Text('($displayPorcentagem %)')
        ],
      ));
    }
    return subtitles;
  }
}
