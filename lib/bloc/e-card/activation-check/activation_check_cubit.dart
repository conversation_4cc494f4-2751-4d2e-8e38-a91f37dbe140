import 'package:cooperado_minha_unimed/bloc/e-card/activation-check/activation_check_state.dart';
import 'package:cooperado_minha_unimed/shared/api/graphqlecard.api.dart';
import 'package:cooperado_minha_unimed/shared/locator.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class EcardActivationCheckCubit extends Cubit<EcardActivationCheckState> {
  EcardActivationCheckCubit() : super(InitialEcardActivationCheckState());

  void ecardActivationCheck({required String codPrestador}) async {
    try {
      emit(LoadingEcardActivationCheckState());

      final result = await Locator.instance!
          .get<GraphQlApiEcard>()
          .ecardActivationCheck(codPrestador: codPrestador);

      emit(LoadedEcardActivationCheckState(eCardModel: result));
    } catch (e) {
      emit(ErrorEcardActivationCheckState(message: e.toString()));
    }
  }

  void ecardPendingActivationCheck({required String codPrestador}) async {
    try {
      emit(LoadingEcardActivationCheckState());

      final result = await Locator.instance!
          .get<GraphQlApiEcard>()
          .ecardPendingActivationCheck(codPrestador: codPrestador);

      emit(LoadedEcardActivationCheckState(eCardModel: result));
    } catch (e) {
      emit(ErrorEcardPendingCheckState(message: e.toString()));
    }
  }
}
