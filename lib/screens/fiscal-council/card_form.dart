import 'package:cooperado_minha_unimed/bloc/fiscal-council/form-council/form_council_cubit.dart';
import 'package:cooperado_minha_unimed/colors.dart';
import 'package:cooperado_minha_unimed/models/fiscal_council.model.dart';
import 'package:cooperado_minha_unimed/screens/fiscal-council/form_council.dart';
import 'package:cooperado_minha_unimed/shared/screen-transitions/fade.transition.dart';
import 'package:cooperado_minha_unimed/shared/widgets/alert.dart';
import 'package:cooperado_minha_unimed/shared/widgets/error-banner/error_banner.component.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:unimed_select/unimed-select.dart';

class CardForm extends StatefulWidget {
  const CardForm({super.key});

  @override
  CardFormState createState() => CardFormState();
}

class CardFormState extends State<CardForm> {
  CouncilTopics? councilTopics;
  TextEditingController controller = TextEditingController();
  bool enable = true;

  @override
  void initState() {
    context.read<FormCouncilCubit>().getTopicEvent();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return enable
        ? Card(
            child: Padding(
              padding: const EdgeInsets.all(8.0),
              child: Center(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: <Widget>[
                    const Padding(
                      padding: EdgeInsets.only(bottom: 16.0),
                      child: Text(
                        "Fale com o Conselho fiscal",
                        style: TextStyle(
                            color: CooperadoColors.tealGreen, fontSize: 16),
                      ),
                    ),
                    _dropDown()
                  ],
                ),
              ),
            ),
          )
        : Container();
  }

  Widget _dropDown() {
    return BlocConsumer<FormCouncilCubit, FormCouncilState>(
      listener: (context, state) {
        if (state is DoneGetTopic) {
          if (state.list!.isNotEmpty) {
            setState(() {
              councilTopics = state.list!.first;
              controller.text = state.list!.first.descricao!;
            });
          } else {
            setState(() {
              enable = false;
            });
          }
        }
      },
      builder: (context, state) {
        if (state is LoadingGetTopic) {
          return const SpinKitCircle(
            color: CooperadoColors.tealGreen,
          );
        } else if (state is DoneGetTopic) {
          return Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: <Widget>[
              UnimedSelect<CouncilTopics?>(
                title: 'Assunto',
                controller: controller,
                items: state.list!
                    .map(
                      (entry) => UnimedSelectItemModel(
                        value: entry,
                        label: entry.descricao!,
                      ),
                    )
                    .toList(),
                onSelect: (item) {
                  setState(() {
                    setState(() {
                      councilTopics = item;
                    });
                  });
                },
              ),
              ElevatedButton(
                style: ElevatedButton.styleFrom(
                  backgroundColor: CooperadoColors.tealGreen,
                ),
                onPressed: () {
                  if (councilTopics != null) {
                    Navigator.push(
                        context,
                        FadeRoute(
                            page: FormCouncilWidget(
                          councilTopics: councilTopics,
                          list: state.list,
                        )));
                  } else {
                    Alert.open(context,
                        text: 'Escolha um assunto para continuar',
                        title: 'Aviso:');
                  }
                },
                child: const Text("ESCREVER"),
              )
            ],
          );
        } else if (state is ErrorGetTopic) {
          return ErrorBanner(message: state.message);
        } else {
          return Container();
        }
      },
    );
  }
}
