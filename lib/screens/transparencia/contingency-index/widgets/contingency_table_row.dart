import 'package:cooperado_minha_unimed/bloc/sensitive-data/sensitive_data_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/sensitive-data/sensitive_data_state.dart';
import 'package:cooperado_minha_unimed/colors.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';

import '../../../../shared/utils/string_utils.dart';

class ContingencyTableRow extends StatelessWidget {
  final double? monthValue;
  final double? totalValue;
  final int? refYear;
  final int? refMonth;
  final Color color;
  const ContingencyTableRow(
      {super.key,
      this.monthValue,
      this.totalValue,
      this.refYear,
      this.refMonth,
      this.color = CooperadoColors.tealGreen});

  @override
  Widget build(BuildContext context) {
    return Row(
      children: <Widget>[
        Expanded(
          flex: 4,
          child: Container(
            padding: const EdgeInsets.symmetric(vertical: 10.0),
            margin: const EdgeInsets.only(bottom: 8.0),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: <Widget>[
                const Padding(
                  padding: EdgeInsets.only(right: 8.0, left: 8.0),
                  child: Icon(
                    Icons.event,
                    color: CooperadoColors.grayDark,
                    size: 20,
                  ),
                ),
                Expanded(
                  child: Text(
                    _referenceLabel(),
                    textAlign: TextAlign.center,
                    style: const TextStyle(color: CooperadoColors.grayDark),
                  ),
                ),
              ],
            ),
          ),
        ),
        Expanded(
          flex: 4,
          child: _rowCell(monthValue, color),
        ),
      ],
    );
  }

Widget _rowCell(double? value, Color color) {
  return BlocBuilder<SensitiveDataCubit, SensitiveDataState>(
    builder: (context, sensitiveState) {
      bool isSensitiveDataVisible = sensitiveState.isSensitiveDataVisible;
      String displayValue = isSensitiveDataVisible
          ? StringUtils.ptBrValue(value)
          : '*****';

      return Container(
        padding: const EdgeInsets.symmetric(vertical: 10.0),
        margin: const EdgeInsets.only(bottom: 8.0, left: 5.0, right: 5.0),
        decoration: BoxDecoration(
          color: color,
          borderRadius: const BorderRadius.only(
            bottomLeft: Radius.circular(6.0),
            topLeft: Radius.circular(6.0),
            topRight: Radius.circular(6.0),
          ),
        ),
        child: Text(
          displayValue,
          textAlign: TextAlign.center,
          style: const TextStyle(
            color: CooperadoColors.grayDark,
          ),
        ),
      );
    },
  );
}

  String _referenceLabel() {
    final DateTime date =
        DateTime.parse('$refYear-${refMonth.toString().padLeft(2, '0')}-01');
    final f = DateFormat('MMM/yyyy', "pt_BR");
    return f.format(date);
  }
}
