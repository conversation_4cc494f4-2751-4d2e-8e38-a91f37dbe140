import 'package:cooperado_minha_unimed/bloc/splash-screen/splash_screen_cubit.dart';
import 'package:cooperado_minha_unimed/colors.dart';
import 'package:cooperado_minha_unimed/screens/login.dart';
import 'package:cooperado_minha_unimed/screens/main/slide.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:splash_unimed/splash_unimed.dart';

const String firstTime = "firstTime";
const String clientId = String.fromEnvironment('CLIENT_ID');
const String clienteIdFortaleza = 'UNIMED_FORTALEZA';

class SplashScreen extends StatefulWidget {
  final FirebaseAnalytics analytics;
  final FirebaseAnalyticsObserver observer;

  const SplashScreen({
    super.key,
    required this.analytics,
    required this.observer,
  });

  @override
  SplashState createState() => SplashState();
}

class SplashState extends State<SplashScreen> {
  Widget? screen;

  @override
  void initState() {
    super.initState();
    context.read<SplashScreenCubit>().checkFirstTime();
    widget.analytics.logScreenView(
      screenName: 'Splash',
      screenClass: 'SplashScreen',
    );
  }

  Future<bool> isFirstTime() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    final isFirstTime = prefs.getBool(firstTime);
    if (isFirstTime != null && !isFirstTime) {
      prefs.setBool(firstTime, false);
      return false;
    } else {
      prefs.setBool(firstTime, false);
      return true;
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<SplashScreenCubit, SplashScreenState>(builder: (context, state) {
      if (state is LoadedSplashScreenState) {
        return AnnotatedRegion<SystemUiOverlayStyle>(
          value: SystemUiOverlayStyle.light,
          child: SplashPage(
            backgroundColor: cooperadoTealGreen[700],
            next: (context) => state.isFirstTime
                ? SlideScreen(
                    analytics: widget.analytics,
                    observer: widget.observer,
                  )
                : LoginScreen(
                    analytics: widget.analytics,
                    observer: widget.observer,
                  ),
            skipAnimation: clientId != clienteIdFortaleza,

            ///TODO - Quando criar as animacoes das outras unimeds, deve ser removido esse parametro skipAnimation
          ),
        );
      } else {
        return Container(
          color: Colors.white,
        );
      }
    });
  }
}
