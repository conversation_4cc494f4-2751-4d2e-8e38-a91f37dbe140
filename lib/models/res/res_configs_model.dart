class ResConfigModel {
  late Allergies allergies;
  late List<ResIndicatorModel> indicators = [];

  ResConfigModel({required this.allergies, required this.indicators});

  ResConfigModel.fromJson(Map<String, dynamic> json) {
    allergies = Allergies.fromJson(json['allergies']);
     if (json['indicators'] != null) {
      indicators = <ResIndicatorModel>[];
      json['indicators'].forEach((v) {
        indicators.add(ResIndicatorModel.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['allergies'] = allergies.toJson();
    data['indicators'] = indicators.map((v) => v.toJson()).toList();
    return data;
  }
}

class Allergies {
  List<Categories>? categories;

  Allergies({this.categories});

  Allergies.fromJson(Map<String, dynamic> json) {
    if (json['categories'] != null) {
      categories = <Categories>[];
      json['categories'].forEach((v) {
        categories!.add(Categories.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    if (categories != null) {
      data['categories'] = categories!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class Categories {
  String? code;
  String? description;

  Categories({this.code, this.description});

  Categories.fromJson(Map<String, dynamic> json) {
    code = json['code'];
    description = json['description'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['code'] = code;
    data['description'] = description;
    return data;
  }
}


class ResIndicatorModel {
  final int id;
   final String description;
  int? selected; 

  ResIndicatorModel({
    required this.id,
    required this.description,
    this.selected, 
  });

  factory ResIndicatorModel.fromJson(Map<String, dynamic> json) {
    return ResIndicatorModel(
      id: json['id'],
      description: json['description'],
      selected: json['selected'], 
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'description': description,
      'selected': selected, 
    };
  }
}