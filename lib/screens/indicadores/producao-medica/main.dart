import 'package:cooperado_minha_unimed/bloc/indicators/medical_production/medical_production_cubit.dart';
import 'package:cooperado_minha_unimed/screens/indicadores/producao-medica/card_producao_medica.dart';
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class ProducaoMedicaScreen extends StatefulWidget {
  final DateTime lastDate;
  const ProducaoMedicaScreen({super.key, required this.lastDate});
  @override
  ProducaoMedicaScreenState createState() => ProducaoMedicaScreenState();
}

class ProducaoMedicaScreenState extends State<ProducaoMedicaScreen> {
  @override
  void initState() {
    _init();
    super.initState();
  }

  void _init() {
    SchedulerBinding.instance.addPostFrameCallback((_) {
      context.read<MedicalProductionCubit>().selectDate(widget.lastDate);
    });
  }

  @override
  Widget build(BuildContext context) {
    return _body();
  }

  Widget _body() {
    return BlocConsumer<MedicalProductionCubit, MedicalProductionState>(
      listener: (context, state) {
        if (state is DoneSelectDate) {
          context
              .read<MedicalProductionCubit>()
              .getMedicalProduction(state.date, null);
        }
      },
      builder: (context, state) {
        return CardProducaoMedica(refresh: () => _init());
      },
    );
  }
}
