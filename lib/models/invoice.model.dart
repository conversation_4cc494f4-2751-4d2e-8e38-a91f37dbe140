import 'package:cooperado_minha_unimed/shared/constants.dart';
import 'package:cooperado_minha_unimed/shared/utils/convert.dart';
import 'package:cooperado_minha_unimed/shared/utils/string_utils.dart';
import 'package:intl/intl.dart';

class InvoiceModel {
  late String dataVencimento;
  late double valor;
  late double? valorAtualizado;
  late int situacao;
  double? valorDesconto;
  int? numeroFatura;

  String get dataVencimentoFormatted =>
      DateFormat("dd/MM/yyyy").format(DateTime.parse(dataVencimento));
  int get anoVencimento => DateTime.parse(dataVencimento).year;
  int get mesVencimento => DateTime.parse(dataVencimento).month;
  String get valorFormatted => StringUtils.formatMoney(valor);
  String get valorAtualizadoFormatted =>
      StringUtils.formatMoney(valorAtualizado!);
  String get dataReferencia => "$mesVencimento-$anoVencimento";

  String get situacaoFormatted {
    switch (situacao) {
      case FaturaListaSituacao.chequeSemFundos:
        return 'CHEQUE SEM FUNDOS';
      case FaturaListaSituacao.emAberto:
        return 'EM ABERTO';
      case FaturaListaSituacao.pago:
        return 'PAGO';
      case FaturaListaSituacao.cancelado:
        return 'CANCELADO';
      default:
        return 'SEM STATUS $situacao';
    }
  }

  InvoiceModel({
    required this.dataVencimento,
    required this.valor,
    required this.valorAtualizado,
    required this.situacao,
    this.numeroFatura,
    this.valorDesconto,
  });

  InvoiceModel.fromJson(Map<String, dynamic> json) {
    dataVencimento = json['dataVencimento'];
    valor = json['valor'];
    valorAtualizado = Convert.dynamicToDouble(json['valorAtualizado']);
    situacao = json['situacao'];
    numeroFatura = json['numeroFatura'];
    valorDesconto = Convert.dynamicToDouble(json['valorDesconto']);
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['dataVencimento'] = dataVencimento;
    data['valor'] = double.parse(valor.toString());
    data['valorAtualizado'] = valorAtualizado != null
        ? double.parse(valorAtualizado.toString())
        : null;
    data['situacao'] = situacao;
    data['numeroFatura'] = numeroFatura;
    data['valorDesconto'] = valorDesconto;

    return data;
  }
}
