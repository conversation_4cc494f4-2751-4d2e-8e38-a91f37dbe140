class HonorarySolicitation {
  int? numeroSolicitacao;
  String? prestadorPF;
  String? nomePrestadorGuiaRef;
  Participacao? participacao;
  String? dataSolicitacao;
  String? observacaoPrestador;
  String? situacao;
  String? dataAuditoria;
  String? observacaoAuditor;
  String? nomeBeneficiario;
  String? intercambio;
  String? carteira;
  int? numeroNota;
  int? guiaReferencia;
  List<ServicosSolicitados>? servicosSolicitados;
  String? hospital;
  bool? showGlosaButton;

  HonorarySolicitation(
      {this.numeroSolicitacao,
      this.prestadorPF,
      this.nomePrestadorGuiaRef,
      this.participacao,
      this.dataSolicitacao,
      this.observacaoPrestador,
      this.situacao,
      this.dataAuditoria,
      this.nomeBeneficiario,
      this.carteira,
      this.intercambio,
      this.observacaoAuditor,
      this.numeroNota,
      this.guiaReferencia,
      this.servicosSolicitados,
      this.hospital,
      this.showGlosaButton});

  bool get isGlosa => showGlosaButton == true;

  String? get intercambioLabel {
    if (intercambio == 'S') {
      return 'Sim';
    } else if (intercambio == 'N') {
      return 'Não';
    }

    return intercambio;
  }

  HonorarySolicitation.fromJson(Map<String, dynamic> json) {
    numeroSolicitacao = json['numeroSolicitacao'];
    prestadorPF = json['prestadorPF'];
    nomePrestadorGuiaRef = json['nomePrestadorGuiaRef'];
    participacao = json['participacao'] != null
        ? Participacao.fromJson(json['participacao'])
        : null;
    dataSolicitacao = json['dataSolicitacao'];
    observacaoPrestador = json['observacaoPrestador'];
    situacao = json['situacao'];
    dataAuditoria = json['dataAuditoria'];
    observacaoAuditor = json['observacaoAuditor'];
    numeroNota = json['numeroNota'];
    guiaReferencia = json['guiaReferencia'];
    nomeBeneficiario = json['nomeBeneficiario'];
    carteira = json['carteira'];
    intercambio = json['intercambio'];
    if (json['servicosSolicitados'] != null) {
      servicosSolicitados = [];
      json['servicosSolicitados'].forEach((v) {
        servicosSolicitados!.add(ServicosSolicitados.fromJson(v));
      });
    }
    hospital = json['hospital'];
    showGlosaButton = json['showGlosaButton'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['numeroSolicitacao'] = numeroSolicitacao;
    data['prestadorPF'] = prestadorPF;
    data['nomePrestadorGuiaRef'] = nomePrestadorGuiaRef;
    if (participacao != null) {
      data['participacao'] = participacao!.toJson();
    }
    data['dataSolicitacao'] = dataSolicitacao;
    data['observacaoPrestador'] = observacaoPrestador;
    data['situacao'] = situacao;
    data['dataAuditoria'] = dataAuditoria;
    data['observacaoAuditor'] = observacaoAuditor;
    data['numeroNota'] = numeroNota;
    data['guiaReferencia'] = guiaReferencia;
    data['nomeBeneficiario'] = nomeBeneficiario;
    data['carteira'] = carteira;
    data['intercambio'] = intercambio;
    if (servicosSolicitados != null) {
      data['servicosSolicitados'] =
          servicosSolicitados!.map((v) => v.toJson()).toList();
    }
    data['hospital'] = hospital;
    data['showGlosaButton'] = showGlosaButton;
    return data;
  }
}

class Participacao {
  int? codigo;
  String? descricao;

  Participacao({this.codigo, this.descricao});

  Participacao.fromJson(Map<String, dynamic> json) {
    codigo = json['codigo'];
    descricao = json['descricao'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['codigo'] = codigo;
    data['descricao'] = descricao;
    return data;
  }
}

class ServicosSolicitados {
  int? codigo;
  String? servico;
  int? quantSolicitada;
  int? quantAuditada;

  ServicosSolicitados(
      {this.codigo, this.servico, this.quantSolicitada, this.quantAuditada});

  ServicosSolicitados.fromJson(Map<String, dynamic> json) {
    codigo = json['codigo'];
    servico = json['servico'];
    quantSolicitada = json['quantSolicitada'];
    quantAuditada = json['quantAuditada'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['codigo'] = codigo;
    data['servico'] = servico;
    data['quantSolicitada'] = quantSolicitada;
    data['quantAuditada'] = quantAuditada;
    return data;
  }
}
