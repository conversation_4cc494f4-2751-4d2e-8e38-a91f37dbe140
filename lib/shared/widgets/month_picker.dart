import 'package:cooperado_minha_unimed/colors.dart';
import 'package:flutter/material.dart';
import 'package:intl/date_symbol_data_local.dart';
import 'package:intl/intl.dart';

class MonthPickerCustom extends StatefulWidget {
  final DateTime? initialDate, firstDate, lastDate;
  final Color? selectedBackgroundColor;
  final Color? selectedTextColor;
  final Color? headerTextColor;
  final Function(DateTime?)? onPressed;

  const MonthPickerCustom(
      {super.key,
      this.selectedBackgroundColor,
      this.selectedTextColor,
      this.headerTextColor,
      required this.initialDate,
      this.firstDate,
      this.lastDate,
      this.onPressed});

  @override
  MonthPickerCustomState createState() => MonthPickerCustomState();
}

class MonthPickerCustomState extends State<MonthPickerCustom> {
  PageController? pageController;
  DateTime? selectedDate;
  late int displayedPage;
  bool isYearSelection = false;

  DateTime? _firstDate, _lastDate;

  @override
  void initState() {
    super.initState();
    initializeDateFormatting();
    selectedDate =
        DateTime(widget.initialDate!.year, widget.initialDate!.month);
    if (widget.firstDate != null) {
      _firstDate = DateTime(widget.firstDate!.year, widget.firstDate!.month);
    }
    if (widget.lastDate != null) {
      _lastDate = DateTime(widget.lastDate!.year, widget.lastDate!.month);
    }
    displayedPage = selectedDate!.year;
    pageController = PageController(initialPage: displayedPage);
  }

 @override
Widget build(BuildContext context) {
  var theme = Theme.of(context);
  var header = buildHeader(theme, "pt_BR");
  var pager = buildPager(theme, 'pt_BR');
  var content = Material(
    color: theme.dialogBackgroundColor,
    child: Column(
      crossAxisAlignment: CrossAxisAlignment.end,
      children: [
        pager,
        buildButtonBar(context),
      ],
    ),
  );
  return Dialog(
    child: Column(
      mainAxisSize: MainAxisSize.min,
      children: <Widget>[
        Builder(
          builder: (context) {
            if (MediaQuery.of(context).orientation == Orientation.portrait) {
              return IntrinsicWidth(
                child: Column(
                  children: [header, content],
                ),
              );
            }
            return IntrinsicHeight(
              child: Row(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [header, content],
              ),
            );
          },
        ),
      ],
    ),
  );
}

  Widget buildButtonBar(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        TextButton(
          onPressed: () => Navigator.pop(context, null),
          child: const Text(
            "CANCELAR",
            style: TextStyle(
                fontWeight: FontWeight.bold, color: CooperadoColors.tealGreen),
          ),
        ),
        TextButton(
          onPressed: () {
            widget.onPressed!(selectedDate);
            Navigator.pop(context, selectedDate);
          },
          child: const Text(
            "OK",
            style: TextStyle(
                fontWeight: FontWeight.bold, color: CooperadoColors.tealGreen),
          ),
        )
      ],
    );
  }

  Widget buildHeader(ThemeData theme, String locale) {
    return Material(
        color: CooperadoColors.tealGreen,
        child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: <Widget>[
                  Text(
                    DateFormat.yMMM(locale).format(selectedDate!).toUpperCase(),
                    style: theme.primaryTextTheme.titleLarge,
                  ),
                  Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: <Widget>[
                        if (!isYearSelection)
                          GestureDetector(
                            onTap: () {
                              setState(() {
                                isYearSelection = true;
                              });
                            },
                            child: Text(
                              DateFormat.y(locale)
                                  .format(DateTime(displayedPage)),
                              style: theme.primaryTextTheme.titleLarge,
                            ),
                          ),
                        if (isYearSelection)
                          Row(
                              mainAxisSize: MainAxisSize.min,
                              mainAxisAlignment: MainAxisAlignment.start,
                              children: <Widget>[
                                Text(
                                  DateFormat.y(locale)
                                      .format(DateTime(displayedPage)),
                                  style: theme.primaryTextTheme.titleLarge,
                                ),
                                Text(
                                  '-',
                                  style: theme.primaryTextTheme.titleLarge,
                                ),
                                Text(
                                  DateFormat.y(locale)
                                      .format(DateTime(displayedPage + 11)),
                                  style: theme.primaryTextTheme.titleLarge,
                                )
                              ]),
                        Row(children: <Widget>[
                          IconButton(
                            icon: Icon(
                              Icons.keyboard_arrow_up,
                              color: theme.primaryIconTheme.color,
                            ),
                            onPressed: () => pageController!.jumpToPage(
                              isYearSelection
                                  ? displayedPage - 12
                                  : displayedPage - 1,
                            ),
                          ),
                          IconButton(
                            icon: Icon(
                              Icons.keyboard_arrow_down,
                              color: theme.primaryIconTheme.color,
                            ),
                            onPressed: () => pageController!.jumpToPage(
                              isYearSelection
                                  ? displayedPage + 12
                                  : displayedPage + 1,
                            ),
                          )
                        ])
                      ])
                ])));
  }

  Widget buildPager(ThemeData theme, String locale) {
    return SizedBox(
        height: 220.0,
        width: 300.0,
        child: Theme(
            data: theme.copyWith(
              buttonTheme: const ButtonThemeData(
                padding: EdgeInsets.all(2.0),
                shape: CircleBorder(),
                minWidth: 4.0,
              ),
            ),
            child: PageView.builder(
                controller: pageController,
                scrollDirection: Axis.vertical,
                onPageChanged: (index) {
                  setState(() {
                    displayedPage = index;
                  });
                },
                itemBuilder: (context, page) {
                  return GridView.count(
                    padding: const EdgeInsets.all(8.0),
                    physics: const NeverScrollableScrollPhysics(),
                    crossAxisCount: 4,
                    children: isYearSelection
                        ? List<int>.generate(12, (i) => page + i)
                            .map(
                              (year) => Padding(
                                padding: const EdgeInsets.all(4.0),
                                child: _getYearButton(year, theme, locale),
                              ),
                            )
                            .toList()
                        : List<int>.generate(12, (i) => i + 1)
                            .map((month) => DateTime(page, month))
                            .map(
                              (date) => Padding(
                                padding: const EdgeInsets.all(4.0),
                                child: _getMonthButton(date, theme, locale),
                              ),
                            )
                            .toList(),
                  );
                })));
  }

  Widget _getMonthButton(
      final DateTime date, final ThemeData theme, final String locale) {
    VoidCallback? callback;
    if (_firstDate == null && _lastDate == null) {
      callback =
          () => setState(() => selectedDate = DateTime(date.year, date.month,));
    } else if (_firstDate != null &&
        _lastDate != null &&
        _firstDate!.compareTo(date) <= 0 &&
        _lastDate!.compareTo(date) >= 0) {
      callback =
          () => setState(() => selectedDate = DateTime(date.year, date.month));
    } else if (_firstDate != null &&
        _lastDate == null &&
        _firstDate!.compareTo(date) <= 0) {
      callback =
          () => setState(() => selectedDate = DateTime(date.year, date.month));
    } else if (_firstDate == null &&
        _lastDate != null &&
        _lastDate!.compareTo(date) >= 0) {
      callback =
          () => setState(() => selectedDate = DateTime(date.year, date.month));
    } else {
      callback = null;
    }
    return TextButton(
      style: TextButton.styleFrom(
          backgroundColor: date.month == selectedDate!.month &&
                  date.year == selectedDate!.year
              ? CooperadoColors.greenDark
              : null,
          textStyle: TextStyle(
              color: date.month == selectedDate!.month &&
                      date.year == selectedDate!.year
                  ? CooperadoColors.aliceBlueLight
                  : date.month == DateTime.now().month &&
                          date.year == DateTime.now().year
                      ? CooperadoColors.greenDark
                      : null)),
      onPressed: callback,
      child: Text(
        DateFormat.MMM(locale).format(date).toUpperCase(),
        style: TextStyle(
          color: date.month == selectedDate!.month &&
                  date.year == selectedDate!.year
              ? Colors.white
              : null,
        ),
      ),
    );
  }

  Widget _getYearButton(int year, ThemeData theme, String locale) {
    return TextButton(
      style: TextButton.styleFrom(
          backgroundColor:
              year == selectedDate!.year ? CooperadoColors.greenDark : null,
          textStyle: TextStyle(
            color: year == selectedDate!.year
                ? theme.textTheme.labelLarge!.color
                : year == DateTime.now().year
                    ? CooperadoColors.greenDark
                    : null,
          )),
      onPressed: () {
        pageController!.jumpToPage(year);
        setState(() {
          isYearSelection = false;
        });
      },
      child: Text(
        DateFormat.y(locale).format(DateTime(year)).toUpperCase(),
      ),
    );
  }
}
