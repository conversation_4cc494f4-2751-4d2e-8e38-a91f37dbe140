import 'package:cooperado_minha_unimed/bloc/sensitive-data/sensitive_data_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/sensitive-data/sensitive_data_state.dart';
import 'package:cooperado_minha_unimed/bloc/transparency/beneficiaries_quantitative/beneficiaries_quantitative_cubit.dart';
import 'package:cooperado_minha_unimed/colors.dart';
import 'package:cooperado_minha_unimed/screens/transparencia/beneficiary/beneficiary_details.dart';
import 'package:cooperado_minha_unimed/shared/screen-transitions/fade.transition.dart';
import 'package:cooperado_minha_unimed/shared/utils/string_utils.dart';
import 'package:cooperado_minha_unimed/shared/widgets/card_refresh.dart';
import 'package:cooperado_minha_unimed/shared/widgets/error-banner/error_banner.component.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';

class CardQuantitativoBeneficiarios extends StatefulWidget {
  final bool isVisible;

  const CardQuantitativoBeneficiarios({super.key, this.isVisible = true});

  @override
  CardQuantitativoBeneficiariosState createState() =>
      CardQuantitativoBeneficiariosState();
}

class CardQuantitativoBeneficiariosState
    extends State<CardQuantitativoBeneficiarios> {
  double total = 0.0;
  DateTime? selectedDateTime;

  @override
  void initState() {
    if (widget.isVisible) _getBeneficiariesQuantitative();
    super.initState();
  }

  void _getBeneficiariesQuantitative() {
    context
        .read<BeneficiariesQuantitativeCubit>()
        .getBeneficiariesQuantitative();
  }

  @override
  Widget build(BuildContext context) {
    return Visibility(
      visible: widget.isVisible,
      child: CardRefresh(
        title: const Text('Beneficiários Ativos',
            style: TextStyle(
              color: CooperadoColors.blackText,
              fontSize: 16,
              fontWeight: FontWeight.bold,
            )),
        refresh: _iconRefresh(),
        child: Column(
          children: [
          BlocBuilder<BeneficiariesQuantitativeCubit, BeneficiariesQuantitativeState>(
  builder: (context, state) {
    if (state is LoadedBeneficiariesQuantitativeState) {
      return BlocBuilder<SensitiveDataCubit, SensitiveDataState>(
        builder: (context, sensitiveState) {
          bool isSensitiveDataVisible = !sensitiveState.isSensitiveDataVisible;
          String displayValue = isSensitiveDataVisible
              ? '*****'
              : StringUtils.formatThousands(state.quantitativoBeneficiarios.quantidadeBeneficiarioAtivos);

          return InkWell(
            onTap: () => Navigator.push(
              context,
              FadeRoute(
                page: BeneficiaryDetail(
                  quantitativoBeneficiariosVO: state.quantitativoBeneficiarios,
                ),
              ),
            ),
            child: Padding(
              padding: const EdgeInsets.symmetric(vertical: 10),
              child: Row(
                children: <Widget>[
                  const Expanded(
                    flex: 2,
                    child: Text(
                      'Beneficiários Ativos',
                      textAlign: TextAlign.center,
                      style: TextStyle(fontSize: 12),
                    ),
                  ),
                  Expanded(
                    flex: 3,
                    child: Container(
                      decoration: const BoxDecoration(
                        border: Border(
                          left: BorderSide(
                            width: 1.0,
                            color: CooperadoColors.greenLight3,
                          ),
                        ),
                      ),
                      child: Text(
                        displayValue,
                        textAlign: TextAlign.center,
                        style: const TextStyle(
                          fontSize: 38,
                          color: CooperadoColors.indicatorsText,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          );
        },
      );
    } else if (state is LoadingBeneficiariesQuantitativeState) {
      return const SpinKitCircle(color: CooperadoColors.tealGreen);
    } else if (state is ErrorBeneficiariesQuantitativeState) {
      return ErrorBanner(message: state.message);
    } else {
      return Container();
    }
  },
)
          ],
        ),
      ),
    );
  }

  Widget _iconRefresh() {
    return BlocBuilder<BeneficiariesQuantitativeCubit,
        BeneficiariesQuantitativeState>(builder: (context, state) {
      if (state is ErrorBeneficiariesQuantitativeState) {
        return InkWell(
            child: const Icon(Icons.refresh),
            onTap: () => _getBeneficiariesQuantitative());
      } else {
        return Container();
      }
    });
  }
}
