part of 'res_allergies_cubit.dart';

abstract class ResAllergiesState extends Equatable {
  const ResAllergiesState();

  @override
  List<Object> get props => [];
}

class InitialResAllergiesState extends ResAllergiesState {}

class LoadingResAllergiesState extends ResAllergiesState {
  @override
  List<Object> get props => [];
}

class ErrorResAllergiesState extends ResAllergiesState {
  final String message;

  @override
  List<Object> get props => [message];

  const ErrorResAllergiesState({required this.message});
}

class NoDataResAllergiesState extends ResAllergiesState {
  const NoDataResAllergiesState();
}

class LoadedResAllergiesState extends ResAllergiesState {
  final List<ResAllergieModel> listResAllergies;

  @override
  List<Object> get props => [listResAllergies];

  const LoadedResAllergiesState({required this.listResAllergies});
}

class LoadingResAllergiesSearchState extends ResAllergiesState {
  @override
  List<Object> get props => [];
}

class ErrorResAllergiesSearchState extends ResAllergiesState {
  final String message;

  @override
  List<Object> get props => [message];

  const ErrorResAllergiesSearchState({required this.message});
}
