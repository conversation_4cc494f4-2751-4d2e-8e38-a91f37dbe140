import 'package:cooperado_minha_unimed/bloc/res/indicators/res_indicator_data_state.dart';
import 'package:cooperado_minha_unimed/models/res-internal/res_indicator_data.model.dart';
import 'package:cooperado_minha_unimed/shared/api/graphql-res.api.dart';
import 'package:cooperado_minha_unimed/shared/locator.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class ResIndsicatorsDataCubit extends Cubit<ResIndicatorsDataState> {
  ResIndsicatorsDataCubit() : super(InitialResIndicatorsDataState());

  List<IndicatorDataModel> _listIndicatorsData = [];
  List<IndicatorDataModel> get listIndicatorsData => _listIndicatorsData;

  final List<String> _selectedIndicators = [];
  List<String> get selectedIndicators => _selectedIndicators;

  void loadResBrazilBeneficiaryIndicatorsData({
    required String cpf,
    required List<String> indicatorsId,
    required DateTimeRange dateRange,
  }) async {
    try {
      emit(LoadingResIndicatorsDataState());
      _listIndicatorsData = await Locator.instance!<ResGraphQlApi>()
          .resBrazilBeneficiaryIndicatorsData(
              cpf: cpf,
              indicatorsId: indicatorsId,
              calendarType: 'month',
              dateRange: dateRange);

      bool allIndicatorsEmpty = _listIndicatorsData
          .every((indicator) => indicator.indicatorsData.isEmpty);

      if (allIndicatorsEmpty) {
        emit(NoDataResIndicatorDataState());
        return;
      }

      if (_listIndicatorsData.isEmpty) {
        emit(NoDataResIndicatorDataState());
      } else if (_listIndicatorsData.isNotEmpty &&
          _listIndicatorsData.length == 1 &&
          _listIndicatorsData[0].indicatorsData.isEmpty) {
        emit(NoDataResIndicatorDataState());
      } else {
        emit(LoadedResIndicatorDataState(
            resIndicatorsData: _listIndicatorsData));
      }
    } catch (e) {
      emit(ErrorResIndicatorDataState(message: e.toString()));
    }
  }

  void addSelectedIndicator(String indicatorId) {
    _selectedIndicators.add(indicatorId);
  }

  void removeSelectedIndicator(String indicatorId) {
    _selectedIndicators.remove(indicatorId);
  }

  void clearSelectedIndicators() {
    _selectedIndicators.clear();
  }

  void addAllSelectedIndicators(List<String> indicators) {
    _selectedIndicators.clear();
    _selectedIndicators.addAll(indicators);
  }

  bool containsSelectedIndicator(String indicatorId) {
    return _selectedIndicators.contains(indicatorId);
  }

/* 
  void listResBrazilBeneficiaryIndicatorsData({required String crm,
      required String card,}) async {
    try {
      emit(LoadingResAllIndicatorsState());
 _listIndicators = await Locator.instance!<GraphQlApi>()
          .resBrazilBeneficiaryIndicatorsData(cpf: '60866109323', indicatorIds: '16', calendarType: 'month', startDate: '2021-12-12', endDate: '2024-12-12'
               );

  /*     Future.delayed( const Duration(seconds: 3),(){
       emit(LoadedResIndicatorState(listResIndicators: _listIndicators));
      }); */

      
    } catch (e) {
      emit(ErrorResIndicatorState(message: e.toString()));
    }
  } */

/*   void getBeneficiaryIndicatorList(){
    try {
      emit(LoadingResAllIndicatorsState());

     var resul = Locator.instance!.get<GraphQlApi>().resBrazilBeneficiaryIndicatorList(cpf: '60866109323');
    } catch (e) {
      print(e);
    }
  } */
}
