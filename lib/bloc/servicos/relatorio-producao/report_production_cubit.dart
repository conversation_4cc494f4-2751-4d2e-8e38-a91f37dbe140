import 'package:bloc/bloc.dart';
import 'package:cooperado_minha_unimed/shared/api/services.api.dart';
import 'package:cooperado_minha_unimed/shared/locator.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';

part 'report_production_state.dart';

class ReportProductionCubit extends Cubit<ReportProductionState> {
  ReportProductionCubit() : super(ReportProductionInitial());

  DateTime? _selectedDateTime;
  setSelectedDateTime(selectedDateTime) {
    _selectedDateTime = selectedDateTime;
  }

  DateTime get lastDate =>
      _selectedDateTime ??
      DateTime(
        DateTime.now().year,
        DateTime.now().month - 1,
        DateTime.now().day,
      );

  getBase64ReportProductionEvent(lastProduction) async {
    emit(LoadingGetReportProductionState());
    try {
      final servicesApi = Locator.instance!<ServicesApi>();
      final String base64Report = await servicesApi.getBase64ReportProduction(
          lastProduction: lastProduction);

      emit(DoneGetReportProductionState(base64Report));
    } catch (ex) {
      debugPrint(ex.toString());
      emit(ErrorGetReportProductionState('$ex'));
    }
  }

  resetState() => emit(ReportProductionInitial());
}
