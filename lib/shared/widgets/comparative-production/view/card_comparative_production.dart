import 'package:cooperado_minha_unimed/bloc/sensitive-data/sensitive_data_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/sensitive-data/sensitive_data_state.dart';
import 'package:cooperado_minha_unimed/colors.dart';
import 'package:cooperado_minha_unimed/shared/widgets/card_refresh.dart';
import 'package:cooperado_minha_unimed/shared/widgets/comparative-production/bloc/comparative_production_cubit.dart';
import 'package:cooperado_minha_unimed/shared/widgets/comparative-production/view/widget/production_graphics.dart';
import 'package:cooperado_minha_unimed/shared/widgets/graphics/choose_date.dart';
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';

class CardComparativoProducao extends StatefulWidget {
  final bool isVisible;
  final DateTime lastDate;

  const CardComparativoProducao({
    super.key,
    required this.lastDate,
    this.isVisible = true,
  });
  @override
  CardComparativoProducaoState createState() => CardComparativoProducaoState();
}

class CardComparativoProducaoState extends State<CardComparativoProducao> {
  @override
  void initState() {
    super.initState();
    if (widget.isVisible == true) {
      SchedulerBinding.instance.addPostFrameCallback((_) {
        _getIndicators();
      });
    }
  }

  void _getIndicators() {
    context.read<ComparativeProductionCubit>().selectDate(
        context.read<ComparativeProductionCubit>().selectedDate ??
            widget.lastDate);
  }

  @override
  Widget build(BuildContext context) {
    return Visibility(
      visible: widget.isVisible,
      child: CardRefresh(
        title: const Text("Comparativo de Produção",
            style: TextStyle(
              color: CooperadoColors.blackText,
              fontSize: 16,
              fontWeight: FontWeight.bold,
            )),
        refresh: _iconRefresh(),
        child: BlocConsumer<ComparativeProductionCubit,
            ComparativeProductionState>(
          listener: (context, state) {
            if (state is SelectedDate) {
              context
                  .read<ComparativeProductionCubit>()
                  .getIndicators(state.dateTime!);
            }
          },
          builder: (context, state) {
            if (state is LoadedComparativeProductionState) {
              return Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: <Widget>[
                  ChooseDateWidget(
                    date:
                        context.read<ComparativeProductionCubit>().selectedDate,
                    textBox: Text(context
                        .read<ComparativeProductionCubit>()
                        .formattedDate
                        .toString()),
                    onPressed: _onPressed,
                  ),
                  _success(state.comparativeProductionModel)
                ],
              );
            } else if (state is LoadingComparativeProductionState ||
                state is SelectDateLoading) {
              return const SpinKitCircle(
                color: CooperadoColors.tealGreen,
              );
            } else if (state is ErrorComparativeProductionState) {
              return Column(
                children: <Widget>[
                  ChooseDateWidget(
                    date:
                        context.read<ComparativeProductionCubit>().selectedDate,
                    textBox: Text(
                      context
                          .read<ComparativeProductionCubit>()
                          .formattedDate
                          .toString(),
                    ),
                    onPressed: _onPressed,
                  ),
                  Text(state.message ?? ''),
                  // ErrorBanner(message: state.message)
                ],
              );
            } else {
              return Container();
            }
          },
        ),
      ),
    );
  }

  Widget _iconRefresh() {
    return BlocBuilder<ComparativeProductionCubit, ComparativeProductionState>(
      builder: (context, state) {
        if (state is ErrorComparativeProductionState) {
          return InkWell(
              child: const Icon(Icons.refresh), onTap: () => _getIndicators());
        } else {
          return Container();
        }
      },
    );
  }

  Widget _success(comparativoProducaoVO) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: <Widget>[
          Expanded(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: <Widget>[
                if (comparativoProducaoVO.valorEspecialidade > 0)
                  Row(
                    children: <Widget>[
                      const Icon(
                        Icons.people_alt,
                        color: CooperadoColors.limaColorDark,
                        size: 30,
                      ),
                      const SizedBox(width: 10),
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: <Widget>[
                          const Text("Média da \nEspecialidade"),
                          BlocBuilder<SensitiveDataCubit, SensitiveDataState>(
                            builder: (context, state) {
                              return Text(
                                  state.isSensitiveDataVisible
                                      ? comparativoProducaoVO
                                          .valorEspecialidadeFormatted
                                      : "********",
                                  style: const TextStyle(
                                      color: CooperadoColors.limaColorDark));
                            },
                          )
                        ],
                      ),
                    ],
                  ),
                const SizedBox(height: 8),
                Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: <Widget>[
                    const Icon(
                      Icons.person,
                      color: CooperadoColors.orange,
                      size: 30,
                    ),
                    const SizedBox(width: 10),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: <Widget>[
                        const Text("Produção"),
                        BlocBuilder<SensitiveDataCubit, SensitiveDataState>(
                          builder: (context, state) {
                            return Text(
                             state.isSensitiveDataVisible ?  comparativoProducaoVO.valorPrestadorFormatted : "********",
                                style: const TextStyle(
                                    color: CooperadoColors.orange));
                          },
                        )
                      ],
                    )
                  ],
                )
              ],
            ),
          ),
          const Expanded(
            child: SizedBox(
              height: 100,
              child: ProductionChart(),
            ),
          ),
        ],
      ),
    );
  }

  void _onPressed(selectedDate) {
    if (mounted) {
      context.read<ComparativeProductionCubit>().selectDate(selectedDate);
    }
  }
}
