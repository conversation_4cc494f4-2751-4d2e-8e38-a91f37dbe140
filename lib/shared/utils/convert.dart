abstract class Convert {
  static double? dynamicToDouble(dynamic value) {
    double? retorno = 0.0;

    try {
      if (value.runtimeType == String) {
        retorno = double.parse(value);
      } else if (value.runtimeType == int) {
        retorno = double.parse('$value');
      } else if (value.runtimeType == double) {
        retorno = value;
      }
    } catch (ex) {
      retorno = 0.0;
    }

    return retorno;
  }
}
