# App Cooperado Unimed

- iOS - Quando tiver dando erro nos plugins do firebase
  - $ flutter clean
  - $ cd ios && rm -rf Podfile.lock && pod deintegrate && pod install
  
- Quando algum model com modelo JsonSerializable for criado ou ajustado rodar o seguinte comando:
- $ flutter pub run build_runner build --delete-conflicting-outputs

- Regerar icones do app
  - $ flutter pub run flutter_launcher_icons

bundle id iOS Enterprise    : br.com.unimedfortaleza.cooperado.minhaunimed
bundle id iOS               : br.com.unimedfortaleza.minhaunimed
bundle id Android           : br.com.unimedfortaleza.minhaunimed

- Debug
  - Forçar Token de produção:
    - flutter run -t lib/main-prod.dart  --dart-define="token_forced=\<token\>"
  - Obs: Esse "force" só está sendo permitido no ambiente de produção e irá forçar o login com o token passado pelo commando acima independentemente do preenchimento dos campos (usuário e senha) na tela de login.

## Lançamento de versão

- Android

> flutter pub run cooperado_minha_unimed:firebase_upload_android
> flutter pub run cooperado_minha_unimed:sobral_firebase_upload_android
> flutter pub run cooperado_minha_unimed:cariri_firebase_upload_android

- ios

> flutter build ios --release
> flutter pub run cooperado_minha_unimed:firebase_upload_ios
> flutter pub run cooperado_minha_unimed:sobral_firebase_upload_ios
> flutter pub run cooperado_minha_unimed:sobral_firebase_upload_ios

Executar

$ flutter clean && flutter pub get

Antes de cada deploy de cada unimed

- Erro de Asset validation failed / Framework contains bitcode distribute iOS

1. executar comando: xcrun bitcode_strip -r <pathDoFramework> -o <pathDoFramework>
  Exemplo:
        xcrun bitcode_strip -r /Volumes/ssdExterno/Development/FlutterProjects/      cliente-minha-unimed/ios/Pods/TwilioVideo/TwilioVideo.xcframework/ios-arm64_armv7/TwilioVideo.framework/TwilioVideo -o /Volumes/ssdExterno/Development/FlutterProjects/cliente-minha-unimed/ios/Pods/TwilioVideo/TwilioVideo.xcframework/ios-arm64_armv7/TwilioVideo.framework/TwilioVideo

2. Clicar no botão de Distribute novamente
