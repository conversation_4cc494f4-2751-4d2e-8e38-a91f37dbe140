// ignore_for_file: use_build_context_synchronously

import 'package:cooperado_minha_unimed/bloc/auth/auth_cubit.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:url_launcher/url_launcher.dart';

const url = 'https://corretora.unimedfortaleza.com.br/programa-multimedico#rd-text-joq3m2m4';

class FinancialServiceScreen extends StatefulWidget {
  const FinancialServiceScreen({super.key});

  @override
  State<FinancialServiceScreen> createState() => _FinancialServiceScreenState();
}

class _FinancialServiceScreenState extends State<FinancialServiceScreen> {
  @override
  void initState() {
    super.initState();
    _launchURL();
  }
Future<void> _launchURL() async {
    final url = BlocProvider.of<AuthCubit>(context).modelGeneralConfigModel.links?.financialServiceLink;
    if (url != null && await canLaunchUrl(Uri.parse(url))) {
      await launchUrl(Uri.parse(url), mode: LaunchMode.externalApplication);
      Navigator.popUntil(context, (route) => route.isFirst); // Volta para a tela inicial
    } else {
      throw 'Unable to open url: $url';
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Antecipa'),
      ),
      body: const Center(
        child: CircularProgressIndicator(),
      ),
    );
  }
  
}