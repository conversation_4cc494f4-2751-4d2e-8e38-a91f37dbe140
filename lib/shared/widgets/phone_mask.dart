import 'package:flutter/material.dart';
import 'package:mask_text_input_formatter/mask_text_input_formatter.dart';

class PhoneMaskFormatter extends MaskTextInputFormatter {
  static String phoneMask = '(##) #### - ####';
  static String celMask = '(##) # #### - ####';

  PhoneMaskFormatter({super.initialText})
      : super(mask: phoneMask, filter: {"#": RegExp(r'[0-9]')});

  @override
  TextEditingValue formatEditUpdate(
      TextEditingValue oldValue, TextEditingValue newValue) {
    if (newValue.text.replaceAll(RegExp(r'[^\w\s]+'), '').length > 2) {
      if (newValue.text.replaceAll(RegExp(r'[^\w\s]+'), '').substring(2, 3) ==
          '9') {
        if (getMask() != celMask) {
          updateMask(mask: celMask);
        }
      } else {
        updateMask(mask: phoneMask);
      }
    }

    return super.formatEditUpdate(oldValue, newValue);
  }
}
