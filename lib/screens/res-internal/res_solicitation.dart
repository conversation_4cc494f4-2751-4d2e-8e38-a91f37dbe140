import 'package:auto_size_text/auto_size_text.dart';
import 'package:cooperado_minha_unimed/bloc/res-internal/image_result/res_image_exam_result_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/res-internal/lab_exams/res_lab_exam_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/res-internal/solicitations/res_solicitation_cubit.dart';
import 'package:cooperado_minha_unimed/colors.dart';
import 'package:cooperado_minha_unimed/models/res-internal/solicitation.model.dart';
import 'package:cooperado_minha_unimed/screens/res-internal/exam.dart';
import 'package:cooperado_minha_unimed/shared/widgets/error-banner/refresh_error_banner.component.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';

class ResSolicitationScreen extends StatefulWidget {
  const ResSolicitationScreen({super.key});

  @override
  State<ResSolicitationScreen> createState() => _ResSolicitationScreenState();
}

class _ResSolicitationScreenState extends State<ResSolicitationScreen> {
  TextEditingController controllerSearch = TextEditingController();

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: CooperadoColors.grayLight3,
      appBar: AppBar(
        centerTitle: true,
        title: const Text("Solicitações"),
        backgroundColor: CooperadoColors.tealGreenDark,
      ),
      body: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16),
        child: BlocConsumer<ResSolicitationCubit, ResSolicitationState>(
          listener: (context, state) {},
          builder: (context, state) {
            if (state is LoadingResSolicitationState) {
              return const Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  SpinKitCircle(color: CooperadoColors.tealGreen),
                  SizedBox(height: 10),
                  AutoSizeText(
                    'Buscando solicitações...',
                    style: TextStyle(
                      color: CooperadoColors.tealGreen,
                      fontSize: 12,
                    ),
                  ),
                ],
              );
            } else if (state is ErrorResSolicitationState) {
              return Center(
                child: RefreshErrorBanner(
                  message: state.message,
                  onRefresh: () {
                    context.read<ResSolicitationCubit>().listSolicitations;
                  },
                ),
              );
            } else if (state is LoadedResSolicitationState) {
              return Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Padding(
                    padding: const EdgeInsets.all(8.0),
                    child: Container(
                      decoration: const BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.all(
                          Radius.circular(24),
                        ),
                      ),
                      child: _serchSolicitation(),
                    ),
                  ),
                  Expanded(
                    child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const SizedBox(height: 20),
                          AutoSizeText(
                            '${state.listSolicitations.length} ${state.listSolicitations.length > 1 ? 'solicitações encontradas' : 'solicitação encontrada'}',
                            style: const TextStyle(
                              color: CooperadoColors.tealGreen,
                              fontSize: 12,
                            ),
                          ),
                          const SizedBox(height: 5),
                          Expanded(
                            child: ListView.builder(
                              physics: const ClampingScrollPhysics(
                                  parent: AlwaysScrollableScrollPhysics()),
                              itemCount: state.listSolicitations.length,
                              itemBuilder: (context, index) {
                                final solicitation =
                                    state.listSolicitations[index];
                                return _buildSolicitationItem(
                                    solicitationModel: solicitation);
                              },
                            ),
                          ),
                        ]),
                  ),
                ],
              );
            }
            return const Center();
          },
        ),
      ),
    );
  }

  Widget _serchSolicitation() {
    return TextFormField(
      enableInteractiveSelection: false,
      autofocus: false,
      controller: controllerSearch,
      textInputAction: TextInputAction.search,
      style: const TextStyle(color: Colors.green),
      onChanged: (query) {
        debugPrint('query: $query');
        context.read<ResSolicitationCubit>().searchListSolicitations(
              searchKey: query,
            );
      },
      maxLines: 1,
      decoration: InputDecoration(
        hintText: 'Buscar solicitação',
        hintMaxLines: null,
        suffixIcon: IconButton(
          onPressed: () {
            controllerSearch.clear();
            context.read<ResSolicitationCubit>().searchListSolicitations(
                  searchKey: '',
                );
          },
          icon: const Icon(
            Icons.clear,
            size: 16,
          ),
        ),
        prefixIcon: const Icon(
          Icons.search,
          size: 24,
          color: CooperadoColors.green,
        ),
        contentPadding: const EdgeInsets.only(
          top: 12,
          left: 10,
          right: 10,
          bottom: 6,
        ),
        border: InputBorder.none,
      ),
    );
  }

  Widget _buildSolicitationItem(
      {required ResSolicitationModel solicitationModel}) {
    return Hero(
      tag: solicitationModel.numInvoice,
      child: InkWell(
        onTap: () {
          context
              .read<ResLabExamCubit>()
              .listResLabExams(card: solicitationModel.card);

          context
              .read<ResImageExamResultCubit>()
              .listResImageExamResults(card: solicitationModel.card);

          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => TabBarExam(
                card: solicitationModel.card,
              ),
            ),
          );
        },
        child: Card(
          margin: const EdgeInsets.symmetric(vertical: 5),
          child: Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    _buildItemText(
                      title: 'Nome do beneficiário',
                      value: solicitationModel.nameBeneficiary,
                      setBackgroundColor: true,
                    ),
                    // _buildItemText(
                    //   title: 'Tipo de solicitação',
                    //   value: solicitationModel.typeSolicitation,
                    //   setBackgroundColor: false,
                    //   isPrice: true,
                    // ),
                    // _buildItemText(
                    //   title: 'Fornecedor',
                    //   value: solicitationModel.nameProvider,
                    //   setBackgroundColor: false,
                    // ),
                    _buildItemText(
                      title: 'Data de atendimento',
                      value: solicitationModel.dateAttendanceFormatted,
                      setBackgroundColor: true,
                    ),
                    const SizedBox(height: 20),
                  ],
                ),
              ),
              const Padding(
                padding: EdgeInsets.all(4.0),
                child: Icon(
                  Icons.arrow_forward_ios,
                  color: CooperadoColors.tealGreen,
                  size: 20,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildItemText({
    required String title,
    required String? value,
    required bool setBackgroundColor,
  }) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          AutoSizeText(
            '$title: ',
            style: const TextStyle(
              color: CooperadoColors.blackText,
              fontSize: 12,
            ),
          ),
          AutoSizeText(
            value ?? 'Não informado',
            style: const TextStyle(
              color: CooperadoColors.blackText,
              fontWeight: FontWeight.bold,
            ),
            textAlign: TextAlign.end,
          ),
        ],
      ),
    );
  }
}
