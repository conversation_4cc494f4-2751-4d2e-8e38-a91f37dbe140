import 'package:bloc/bloc.dart';
import 'package:cooperado_minha_unimed/colors.dart';
import 'package:cooperado_minha_unimed/screens/indicadores/comparativo-custo/grafico_custo.dart';
import 'package:cooperado_minha_unimed/shared/api/indicadores.api.dart';
import 'package:cooperado_minha_unimed/shared/locator.dart';
import 'package:cooperado_minha_unimed/shared/vo/indicadores/comparativo_custo.dart';
import 'package:equatable/equatable.dart';
import 'package:intl/intl.dart';

part 'cost_comparative_state.dart';

class CostComparativeCubit extends Cubit<CostComparativeState> {
  CostComparativeCubit() : super(InitialCostComparativeState());

  List<CustoSerie> costSerieList = [];
  List<CustoSerie> get costList => costSerieList;

  DateTime? _selectedDate;
  DateTime? get selectedDate => _selectedDate;
  String? _formattedDate;
  String? get formattedDate => _formattedDate;

  getCostComparative(DateTime gte, DateTime lte) async {
    try {
      emit(LoadingCostComparativeState());
      _clearCosts();
      final comparative = await Locator.instance!<IndicadoresApi>()
          .getComparativoCusto(dataInicio: gte, dataFim: lte);
      _mount(comparative);
      emit(LoadedCostComparativeState(comparative));
    } catch (e) {
      emit(ErrorCostComparativeState(e.toString()));
    }
  }

  void _clearCosts() {
    costSerieList.clear();
  }

  selectDate(DateTime? date) {
    emit(LoadingCostComparativeSelectDate());
    _selectedDate = date;
    _formattedDate = DateFormat('MM/yyyy').format(_selectedDate!);

    emit(CostComparativeSelectDate(_selectedDate));
  }

  void _mount(RetornoComparativoCustoVO comparativoProducaoVO) {
    costSerieList.add(CustoSerie(
        title: "M.E",
        valor: comparativoProducaoVO.getValorEspecialdade(),
        barColor: CooperadoColors.tealGreen));
    costSerieList.add(CustoSerie(
        title: "P",
        valor: comparativoProducaoVO.getValorPrestador(),
        barColor: CooperadoColors.green));
    // _costList.replaceRange(0, 1, [
    //   CustoSerie(
    //       title: "M.E",
    //       valor: comparativoProducaoVO.getValorEspecialdade(),
    //       barColor: charts.ColorUtil.fromDartColor(CooperadoColors.purple))
    // ]);
    // _costList.replaceRange(0, 1, [
    // CustoSerie(
    //     title: "P",
    //     valor: 22000,
    //     barColor: charts.ColorUtil.fromDartColor(CooperadoColors.purple))
    // ]);
  }
}
