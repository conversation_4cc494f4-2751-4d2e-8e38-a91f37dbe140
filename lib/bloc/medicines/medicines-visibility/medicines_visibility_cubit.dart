import 'package:bloc/bloc.dart';
import 'package:cooperado_minha_unimed/models/user.model.dart';
import 'package:cooperado_minha_unimed/shared/api/services.api.dart';
import 'package:cooperado_minha_unimed/shared/locator.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';

part 'medicines_visibility_state.dart';

class MedicinesVisibilityCubit extends Cubit<MedicinesVisibilityState> {
  MedicinesVisibilityCubit() : super(MedicinesVisibilityInitial());

  bool? exibeCard = false;
  String? crmId;

  getListMedicinesVisibility({required List<Especialidades> specialities}) async {
    try {
      final servicesApi = Locator.instance!<ServicesApi>();
      final visible = await servicesApi.getListMedicineVisible(specialities: specialities);

      if (visible) {
        emit(const VisibilityListMedicinesState());
      } else {
        emit(const NoVisibilityListMedicinesState());
      }
    } catch (ex) {
      debugPrint(ex.toString());
      emit(ErrorMedicinesVisibilityState('$ex'));
    }
  }
}
