part of 'beneficiary_cubit.dart';

abstract class BeneficiaryState extends Equatable {
  const BeneficiaryState();

  @override
  List<Object?> get props => [];
}

class BeneficiaryInitial extends BeneficiaryState {}

class LoadingBeneficiaryState extends BeneficiaryState {
  @override
  List<Object> get props => [];
}

class LoadedBeneficiaryState extends BeneficiaryState {
  final List<BeneficiaryCardModel> cards;
  final BeneficiaryCardModel selectCard;
  @override
  List<Object?> get props => [
        cards,
      ];
  const LoadedBeneficiaryState({required this.cards, required this.selectCard});
}

class SelectedBeneficiaryState extends BeneficiaryState {
  final BeneficiaryCardModel card;
  @override
  List<Object?> get props => [
        card,
      ];
  const SelectedBeneficiaryState({required this.card});
}

class NoDataBeneficiaryState extends BeneficiaryState {
  @override
  List<Object?> get props => [];
  const NoDataBeneficiaryState();
}

class ErrorBeneficiaryState extends BeneficiaryState {
  final String message;
  @override
  List<Object> get props => [message];

  const ErrorBeneficiaryState(this.message);
}
