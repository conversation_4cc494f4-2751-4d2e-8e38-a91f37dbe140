// o retorno do comparativo de custo retorna um json mais completo, mas só utilizamos
// esses valores para montagem de graficos

import 'package:cooperado_minha_unimed/shared/utils/string_utils.dart';

class RetornoComparativoCustoVO {
  List<AtendimentosPrestador>? atendimentosPrestador;
  List<AtendimentosEspecialidade>? atendimentosEspecialidade;

  RetornoComparativoCustoVO(
      {this.atendimentosPrestador, this.atendimentosEspecialidade});

  //regra de negocio ionic: var speciality = data.atendimentosEspecialidade[0].media;
  // var provider = data.atendimentosPrestador[0]?data.atendimentosPrestador[0].total:0;

  double? getValorPrestador() =>
      atendimentosPrestador == null || atendimentosPrestador!.isEmpty
          ? 0.0
          : atendimentosPrestador!.first.total;
  double? getValorEspecialdade() =>
      atendimentosEspecialidade == null || atendimentosEspecialidade!.isEmpty
          ? 0.0
          : atendimentosEspecialidade!.first.media;

  String getValorPrestadorFormat() =>
      atendimentosPrestador == null || atendimentosPrestador!.isEmpty
          ? "R\$ 0,00"
          : atendimentosPrestador!.first.totalFormatted;
  String getValorEspecialidadeFormat() =>
      atendimentosEspecialidade == null || atendimentosEspecialidade!.isEmpty
          ? "R\$ 0,00"
          : atendimentosEspecialidade!.first.mediaFormatted;

  RetornoComparativoCustoVO.fromJson(Map<String, dynamic> json) {
    if (json['atendimentosPrestador'] != null) {
      atendimentosPrestador = [];
      json['atendimentosPrestador'].forEach((v) {
        atendimentosPrestador!.add(AtendimentosPrestador.fromJson(v));
      });
    }
    if (json['atendimentosEspecialidade'] != null) {
      atendimentosEspecialidade = [];
      json['atendimentosEspecialidade'].forEach((v) {
        atendimentosEspecialidade!.add(AtendimentosEspecialidade.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    if (atendimentosPrestador != null) {
      data['atendimentosPrestador'] =
          atendimentosPrestador!.map((v) => v.toJson()).toList();
    }
    if (atendimentosEspecialidade != null) {
      data['atendimentosEspecialidade'] =
          atendimentosEspecialidade!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class AtendimentosPrestador {
  double? media;
  double? total;

  AtendimentosPrestador({this.media, this.total});

  String get totalFormatted => StringUtils.formatMoney(total!);

  AtendimentosPrestador.fromJson(Map<String, dynamic> json) {
    media =
        json['media'] != null ? double.parse(json['media'].toString()) : null;
    total =
        json['total'] != null ? double.parse(json['total'].toString()) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['media'] = media;
    data['total'] = total;
    return data;
  }
}

class AtendimentosEspecialidade {
  double? media;
  double? total;

  AtendimentosEspecialidade({this.media, this.total});

  String get mediaFormatted => StringUtils.formatMoney(media!);

  AtendimentosEspecialidade.fromJson(Map<String, dynamic> json) {
    media =
        json['media'] != null ? double.parse(json['media'].toString()) : null;
    total =
        json['total'] != null ? double.parse(json['total'].toString()) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['media'] = media;
    data['total'] = total;
    return data;
  }
}
