import 'package:flutter_test/flutter_test.dart';
import 'package:cooperado_minha_unimed/models/user.model.dart';

void main() {
  group('UserCredentials Serialization', () {
    test('Deve desserializar corretamente a partir de um JSON', () {
      // JSON de exemplo
      final json = {
        'crm': '123456',
        'password': 'senha123',
      };

      // Desserialização
      final userCredentials = UserCredentials.fromJson(json);

      // Verificações
      expect(userCredentials.crm, '123456');
      expect(userCredentials.password, 'senha123');
    });

    test('Deve serializar corretamente para um JSON', () {
      // Objeto UserCredentials de exemplo
      final userCredentials = UserCredentials(
        crm: '123456',
        password: 'senha123',
      );

      // Serialização
      final json = userCredentials.toJson();

      // Verificações
      expect(json['crm'], '123456');
      expect(json['password'], 'senha123');
    });

    test('Deve lidar com valores nulos no campo password', () {
      // JSON com password nulo
      final json = {
        'crm': '123456',
        'password': null,
      };

      // Desserialização
      final userCredentials = UserCredentials.fromJson(json);

      // Verificações
      expect(userCredentials.crm, '123456');
      expect(userCredentials.password, isNull);

      // Serialização
      final serializedJson = userCredentials.toJson();

      // Verificações
      expect(serializedJson['crm'], '123456');
      expect(serializedJson['password'], isNull);
    });

    test('Deve aceitar uma senha com o máximo de caracteres', () {
      // JSON com senha máxima
      final json = {
        'crm': '123456',
        'password': 'a' * 20, // Senha com 20 caracteres (máximo)
      };

      // Desserialização
      final userCredentials = UserCredentials.fromJson(json);

      // Verificações
      expect(userCredentials.password, 'a' * 20);
      expect(userCredentials.password!.length, lessThanOrEqualTo(20));
    });
  });
}
