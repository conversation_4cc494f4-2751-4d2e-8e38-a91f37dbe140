import 'package:auto_size_text/auto_size_text.dart';
import 'package:cooperado_minha_unimed/bloc/sensitive-data/sensitive_data_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/sensitive-data/sensitive_data_state.dart';
import 'package:cooperado_minha_unimed/bloc/servicos/extrato-quota-part/extract_quota_part_cubit.dart';
import 'package:cooperado_minha_unimed/colors.dart';
import 'package:cooperado_minha_unimed/shared/utils/string_utils.dart';
import 'package:cooperado_minha_unimed/shared/vo/servicos/extract_copart.vo.dart';
import 'package:cooperado_minha_unimed/shared/widgets/error-banner/error_banner.component.dart';
import 'package:cooperado_minha_unimed/shared/widgets/graphics/choose_date.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';

class DetailsScreen extends StatefulWidget {
  final ExtractCopartVO extractQuotaPart;

  const DetailsScreen({super.key, required this.extractQuotaPart});

  @override
  DetailsScreenState createState() => DetailsScreenState();
}

class DetailsScreenState extends State<DetailsScreen> {
  bool showExtract = true;
  bool _isExtractSelected = true;

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        title: const Text("Quota Parte"),
        backgroundColor: CooperadoColors.tealGreenDark,
        actions: [
          Padding(
            padding: const EdgeInsets.only(right: 15.0),
            child: IconButton(
              icon: BlocBuilder<SensitiveDataCubit, SensitiveDataState>(
                builder: (context, state) {
                  return Icon(
                    state.isSensitiveDataVisible
                        ? Icons.visibility
                        : Icons.visibility_off,
                  );
                },
              ),
              onPressed: () {
                context
                    .read<SensitiveDataCubit>()
                    .toggleSensitiveDataVisibility();
              },
            ),
          ),
        ],
      ),
      body: SafeArea(
        child: BlocConsumer<ExtractQuotaPartCubit, ExtractQuotaPartState>(
          listener: (context, state) {
            if (state is DoneSelectDate) {
              context
                  .read<ExtractQuotaPartCubit>()
                  .getExtractQuotaPart(state.date);
            }
          },
          builder: (context, state) {
            return _body();
          },
        ),
      ),
    );
  }

  Widget _body() {
    return SingleChildScrollView(
      child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: <Widget>[
            const SizedBox(height: 20.0),
            Hero(
              tag: 'animation',
              flightShuttleBuilder:
                  (context, anim, direction, fromContext, toContext) {
                final Hero toHero = toContext.widget as Hero;
                if (direction == HeroFlightDirection.pop) {
                  return FadeTransition(
                    opacity: const AlwaysStoppedAnimation(2),
                    child: toHero.child,
                  );
                } else {
                  return toHero.child;
                }
              },
              child: BlocBuilder<SensitiveDataCubit, SensitiveDataState>(
                builder: (context, sensitiveState) {
                  bool isSensitiveDataVisible =
                      sensitiveState.isSensitiveDataVisible;
                  return Card(
                    elevation: 0,
                    child: Container(
                      padding: const EdgeInsets.only(
                          top: 8.0, left: 8.0, right: 8.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: <Widget>[
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceAround,
                            children: [
                              Column(
                                children: [
                                  _modelShowInfo(
                                    "Valor Integralizado",
                                    isSensitiveDataVisible
                                        ? widget.extractQuotaPart
                                            .getSaldoIntegralizadoFormat()
                                        : '*****',
                                  ),
                                  _modelShowInfo(
                                    "Valor Subscrição",
                                    isSensitiveDataVisible
                                        ? widget.extractQuotaPart
                                            .getTotalSubscricaoFormat()
                                        : '*****',
                                  ),
                                ],
                              ),
                              Container(
                                height: 120,
                                width: 2,
                                color: Colors.grey.withOpacity(0.2),
                              ),
                              Column(
                                children: [
                                  _modelShowInfo(
                                    "Valor a Integralizar",
                                    isSensitiveDataVisible
                                        ? widget.extractQuotaPart
                                            .getSaldoIntegralizarFormat()
                                        : '*****',
                                  ),
                                  _modelShowInfo(
                                    "Valor Devolução",
                                    isSensitiveDataVisible
                                        ? widget.extractQuotaPart
                                            .getTotalDevolucaoFormat()
                                        : '*****',
                                  ),
                                ],
                              ),
                            ],
                          ),
                          const SizedBox(height: 10),
                        ],
                      ),
                    ),
                  );
                },
              ),
            ),
            const SizedBox(height: 20),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16.0),
              child: Row(
                children: [
                  Expanded(
                    child: InkWell(
                      onTap: () {
                        setState(() {
                          _isExtractSelected = true;
                        });
                      },
                      child: Ink(
                        decoration: BoxDecoration(
                          color: _isExtractSelected
                              ? CooperadoColors.tealGreen
                              : Colors.white,
                          border: Border.all(color: CooperadoColors.tealGreen),
                          borderRadius:
                              const BorderRadius.all(Radius.circular(10)),
                        ),
                        child: Padding(
                          padding: const EdgeInsets.symmetric(vertical: 13),
                          child: Center(
                            child: Text(
                              'EXTRATO',
                              style: TextStyle(
                                color: _isExtractSelected
                                    ? Colors.white
                                    : CooperadoColors.tealGreen,
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(width: 10),
                  Expanded(
                    child: InkWell(
                      onTap: () {
                        setState(() {
                          _isExtractSelected = false;
                        });
                      },
                      child: Ink(
                        decoration: BoxDecoration(
                          color: !_isExtractSelected
                              ? CooperadoColors.tealGreen
                              : Colors.white,
                          border: Border.all(color: CooperadoColors.tealGreen),
                          borderRadius:
                              const BorderRadius.all(Radius.circular(10)),
                        ),
                        child: Padding(
                          padding: const EdgeInsets.symmetric(vertical: 13),
                          child: Center(
                            child: Text(
                              'APORTE',
                              style: TextStyle(
                                color: !_isExtractSelected
                                    ? Colors.white
                                    : CooperadoColors.tealGreen,
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 20),
            Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                const Text('Desde o mês',
                    style: TextStyle(color: CooperadoColors.grayDark)),
                ChooseDateWidget(
                  date: context.read<ExtractQuotaPartCubit>().selectedDate,
                  onPressed: _onChangeDate,
                  alignCenter: true,
                  textBox: Padding(
                    padding: const EdgeInsets.all(8.0),
                    child: Text(
                      context
                          .read<ExtractQuotaPartCubit>()
                          .formattedDate
                          .toString(),
                      style: const TextStyle(
                          fontSize: 16, color: CooperadoColors.grayDark),
                    ),
                  ),
                ),
              ],
            ),
            BlocBuilder<SensitiveDataCubit, SensitiveDataState>(
              builder: (context, sensitiveState) {
                bool isSensitiveDataVisible = !sensitiveState.isSensitiveDataVisible;
                return Container(
                  height: 400,
                  padding:
                      const EdgeInsets.symmetric(horizontal: 10, vertical: 10),
                  margin:
                      const EdgeInsets.symmetric(horizontal: 10, vertical: 10),
                  child: _isExtractSelected
                      ? BlocBuilder<ExtractQuotaPartCubit,
                          ExtractQuotaPartState>(
                          builder: (context, state) {
                            if (state is DoneGetExtractQuotaPartState) {
                              return _details(state.extractQuotaPart);
                            } else if (state
                                is LoadingGetExtractQuotaPartState) {
                              return const SpinKitCircle(
                                color: CooperadoColors.tealGreen,
                              );
                            } else if (state is ErrorGetExtractQuotaPartState) {
                              return ErrorBanner(message: state.message);
                            } else {
                              return Container();
                            }
                          },
                        )
                      : BlocBuilder<ExtractQuotaPartCubit,
                          ExtractQuotaPartState>(
                          builder: (context, state) {
                            if (state is DoneGetExtractQuotaPartState) {
                              return _aporte(
                                state.extractQuotaPart,isSensitiveDataVisible
                              );
                            } else if (state
                                is LoadingGetExtractQuotaPartState) {
                              return const SpinKitCircle(
                                color: CooperadoColors.tealGreen,
                              );
                            } else if (state is ErrorGetExtractQuotaPartState) {
                              return ErrorBanner(message: state.message);
                            } else {
                              return Container();
                            }
                          },
                        ),
                );
              },
            ),
          ]),
    );
  }

  Widget _modelShowInfo(String description, String value) {
    return Column(
      children: <Widget>[
        Padding(
          padding: const EdgeInsets.symmetric(vertical: 10),
          child: Text(description,
              style: const TextStyle(color: CooperadoColors.grayLight2)),
        ),
        Text(
          value,
          style: const TextStyle(
              color: CooperadoColors.limaColorDark, fontSize: 18),
        ),
        const SizedBox(
          height: 10,
        )
      ],
    );
  }

  void _onChangeDate(selectedDate) {
    context.read<ExtractQuotaPartCubit>().selectDate(selectedDate);
  }

  Widget _aporte(ExtractCopartVO extractData, bool isSensitiveData) {
    List<ExtratoAporte> extracts = extractData.extratoAporte!;

    return Column(
      children: [
        const Padding(
          padding: EdgeInsets.symmetric(vertical: 16.0),
          child: Row(
            children: <Widget>[
              Expanded(
                flex: 1,
                child: Text(
                  "Data",
                  textAlign: TextAlign.center,
                  style: TextStyle(color: CooperadoColors.tealGreen),
                ),
              ),
              Expanded(
                flex: 1,
                child: Text(
                  "Valor",
                  textAlign: TextAlign.center,
                  style: TextStyle(color: CooperadoColors.tealGreen),
                ),
              ),
              Expanded(
                flex: 1,
                child: Text(
                  "Status",
                  textAlign: TextAlign.center,
                  style: TextStyle(color: CooperadoColors.tealGreen),
                ),
              ),
            ],
          ),
        ),
        Expanded(
          child: ListView.separated(
              physics: const ClampingScrollPhysics(
                  parent: AlwaysScrollableScrollPhysics()),
              itemCount: extracts.length,
              separatorBuilder: (context, index) => const Divider(
                    height: 1,
                    color: CooperadoColors.grayDark,
                  ),
              itemBuilder: (context, index) {
                final e = extracts.elementAt(index);
                return _aporteTile(e, index % 2 == 0, isSensitiveData);
              }),
        )
      ],
    );
  }

  Widget _details(ExtractCopartVO extractData) {
    List<Extrato> extracts = extractData.extrato!;
    return Column(
      children: [
        const Divider(
          height: 1,
          color: CooperadoColors.grayDark,
        ),
        const Padding(
          padding: EdgeInsets.symmetric(vertical: 16.0),
          child: Row(
            children: <Widget>[
              Expanded(
                flex: 1,
                child: AutoSizeText(
                  "Data",
                  textAlign: TextAlign.center,
                  style:
                      TextStyle(color: CooperadoColors.blackText, fontSize: 12),
                  maxLines: 1,
                  minFontSize: 10,
                ),
              ),
              Expanded(
                flex: 1,
                child: AutoSizeText(
                  "Descrição",
                  textAlign: TextAlign.center,
                  style:
                      TextStyle(color: CooperadoColors.blackText, fontSize: 12),
                  maxLines: 1,
                  minFontSize: 10,
                ),
              ),
              Expanded(
                flex: 1,
                child: AutoSizeText(
                  "Subscrição",
                  textAlign: TextAlign.center,
                  style:
                      TextStyle(color: CooperadoColors.blackText, fontSize: 12),
                  maxLines: 1,
                  minFontSize: 10,
                ),
              ),
              Expanded(
                flex: 1,
                child: AutoSizeText(
                  "Integralização",
                  textAlign: TextAlign.center,
                  style: TextStyle(color: CooperadoColors.blackText),
                  maxLines: 1,
                  minFontSize: 10,
                ),
              ),
            ],
          ),
        ),
        const Divider(
          height: 1,
          color: CooperadoColors.grayDark,
        ),
        Expanded(
          child: BlocBuilder<SensitiveDataCubit, SensitiveDataState>(
            builder: (context, sensitiveState) {
              bool isSensitiveDataVisible =
                  sensitiveState.isSensitiveDataVisible;

              return ListView.builder(
                physics: const ClampingScrollPhysics(
                    parent: AlwaysScrollableScrollPhysics()),
                itemCount: extracts.length,
                itemBuilder: (context, index) {
                  final Extrato e = extracts.elementAt(index);
                  return _extractCard(e, false, isSensitiveDataVisible);
                },
              );
            },
          ),
        )
      ],
    );
  }

  Widget _extractCard(
      Extrato extract, bool isGrey, bool isSensitiveDataVisible) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 8),
      color: isGrey ? Colors.grey.withOpacity(.25) : Colors.transparent,
      child: Row(
        children: <Widget>[
          Expanded(
            flex: 1,
            child: Text(extract.data!,
                textAlign: TextAlign.center,
                style:
                    const TextStyle(fontSize: 10, fontWeight: FontWeight.bold)),
          ),
          Expanded(
            flex: 1,
            child: Text(
              isSensitiveDataVisible ? extract.descricaoVerba! : '*****',
              textAlign: TextAlign.center,
              style: const TextStyle(fontSize: 12),
            ),
          ),
          Expanded(
            flex: 1,
            child: Text(
              isSensitiveDataVisible
                  ? StringUtils.formatMoneyDynamic(extract.subscricao!)
                  : '*****',
              textAlign: TextAlign.center,
              style: TextStyle(
                  fontSize: 12,
                  fontWeight: extract.subscricao! > 0
                      ? FontWeight.bold
                      : FontWeight.normal),
            ),
          ),
          Expanded(
            flex: 1,
            child: Text(
              isSensitiveDataVisible
                  ? StringUtils.formatMoneyDynamic(extract.integralizacao!)
                  : '*****',
              textAlign: TextAlign.center,
              style: TextStyle(
                  fontSize: 12,
                  fontWeight: extract.integralizacao! > 0
                      ? FontWeight.bold
                      : FontWeight.normal),
            ),
          )
        ],
      ),
    );
  }

  Widget _aporteTile(ExtratoAporte extract, bool isGrey, bool isSensitive) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 8),
      color: isGrey ? Colors.grey.withOpacity(.25) : Colors.transparent,
      child: Row(
        children: <Widget>[
          Expanded(
            flex: 1,
            child: Text(
              extract.data != null ? extract.data! : "",
              textAlign: TextAlign.center,
            ),
          ),
          Expanded(
            flex: 1,
            child: Text(
              isSensitive
                  ? '*****'
                  : StringUtils.formatMoneyDynamic(
                      (extract.valorLancamento != null &&
                              extract.valorLancamento! > 0)
                          ? extract.valorLancamento!
                          : extract.valorBoleto!),
              textAlign: TextAlign.center,
            ),
          ),
          Expanded(
            flex: 1,
            child: Text(
               isSensitive
                  ? '*****'
                  :
             ( extract.statusBoleto != null ? extract.statusBoleto! : "-"),
              textAlign: TextAlign.center,
              style: TextStyle(
                  fontWeight: extract.statusBoleto != null
                      ? FontWeight.bold
                      : FontWeight.normal),
            ),
          )
        ],
      ),
    );
  }
}
