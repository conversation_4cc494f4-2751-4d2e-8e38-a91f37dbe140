import 'dart:convert';

import 'package:cooperado_minha_unimed/models/address.model.dart';
import 'package:cooperado_minha_unimed/models/user.model.dart';
import 'package:cooperado_minha_unimed/models/zipcode.model.dart';
import 'package:cooperado_minha_unimed/shared/api/auth.api.dart';
import 'package:cooperado_minha_unimed/shared/exceptions.dart';
import 'package:cooperado_minha_unimed/shared/flavor_config.dart';
import 'package:http_client/exceptions/exceptions.dart';
import 'package:http_client/http_client.dart';
import 'package:cooperado_minha_unimed/shared/locator.dart';
import 'package:cooperado_minha_unimed/shared/messages.exceptions.dart';
import 'package:cooperado_minha_unimed/shared/utils/logger_print.dart';
import 'package:cooperado_minha_unimed/shared/vo/profile/profile-payload.vo.dart';
import 'package:crypto/crypto.dart';

class ConfigProfileApi {
  final UnimedHttpClient httpClient;

  ConfigProfileApi(this.httpClient);
  final logger = UnimedLogger(className: 'ConfigProfileApi');

  Future<String> _getToken() async {
    final UserCredentials? credentials =
        await Locator.instance!.get<AuthApi>().getCredentials();
    if (credentials != null) {
      return await User.createToken(credentials: credentials);
    } else {
      return "";
    }
  }

  List<AddressModel> _parseAddresses(List<dynamic> list) {
    return list.map((item) => AddressModel.fromJson(item)).toList();
  }

  ZipCodeModel _parseZipCodeAddresses(Map<String, dynamic> json) {
    return ZipCodeModel.fromJson(json);
  }

  Future<List<AddressModel>> getProfileAddresses() async {
    try {
      final token = await _getToken();
      final url =
          '${FlavorConfig.instance!.values.portal.url}alteracao-dados-cadastrais/selecionar-enderecos?token=$token';

      final body = {};

      final response = await httpClient.post(Uri.parse(url),
          body: jsonEncode(body),
          headers: {"Content-Type": "application/json"});
      if (response.statusCode == 200) {
        final list = _parseAddresses(
            jsonDecode(response.body)['retorno']['model']['form']['enderecos']);
        logger.d('getProfileAddresses, success. list: ${list.length}');
        return list;
      } else {
        final message = jsonDecode(response.body)['mensagem'];
        logger.e(
            'getProfileAddresses statusCode : ${response.statusCode} body: ${response.body}');
        throw UnimedException('$message}');
      }
    } catch (ex) {
      logger.e('getProfileAddresses exception : $ex');
      throw UnimedException('$ex');
    }
  }

  Future<ZipCodeModel> searchAddresses(String zipcode) async {
    try {
      final tokenPerfilApps =
          await Locator.instance!.get<AuthApi>().tokenPerfilApps();
      final zipCodeFormated = zipcode.replaceAll(RegExp(r'[^\w\s]+'), '');
      final url =
          '${FlavorConfig.instance!.values.profilePermissions.url}app/cooperado/endereco/cep/$zipCodeFormated';

      final response = await httpClient.get(
        Uri.parse(url),
        headers: {
          "Content-Type": "application/json",
          "Authorization": "Bearer $tokenPerfilApps",
        },
      );
      if (response.statusCode == 200) {
        if (jsonDecode(utf8.decode(response.bodyBytes))['sucesso'] == false) {
          final message =
              jsonDecode(utf8.decode(response.bodyBytes))['mensagem'];
          logger.e(
              'searchAddresses statusCode : ${response.statusCode} body: ${response.body}');
          throw UnimedException('$message');
        } else {
          final result = jsonDecode(utf8.decode(response.bodyBytes));
          logger.d('searchAddresses, success. $result');
          return _parseZipCodeAddresses(result);
        }
      } else {
        logger.e(
            'searchAddresses statusCode : ${response.statusCode} body: ${response.body}');
        throw UnimedException(MessageException.general);
      }
    } on NotFoundException catch (ex) {
      logger.e('searchAddresses NotFoundException : $ex');
      throw NotFoundException();
    } on NoInternetException catch (ex) {
      logger.e('searchAddresses NoInternetException : $ex');
      throw NoInternetException();
    } on NotFoundDataException catch (ex) {
      logger.e('searchAddresses NotFoundDataException : $ex');
      throw NotFoundDataException();
    } on ServiceTimeoutException catch (ex) {
      logger.e('searchAddresses ServiceTimeoutException : $ex');
      throw ServiceTimeoutException();
    } catch (ex) {
      logger.e('searchAddresses exception : $ex');
      throw UnimedException('$ex');
    }
  }

  Future<void> changeEmail(String email, String password) async {
    try {
      final token = await _getToken();
      final url =
          '${FlavorConfig.instance!.values.portal.url}alteracao-dados-cadastrais/alteracao-email?token=$token';

      final body = {
        'emailNovo': email,
        'senha': md5.convert(utf8.encode(password)).toString()
      };

      final response = await httpClient.post(Uri.parse(url),
          body: jsonEncode(body),
          headers: {"Content-Type": "application/json"});

      if (response.statusCode == 200) {
        logger.d(
            'changeEmail, success. Changed to $email | body: ${response.body}');
      } else {
        final message = jsonDecode(response.body)['mensagem'];
        logger.e(
            'changeEmail statusCode : ${response.statusCode} ${response.body}');
        throw UnimedException('$message');
      }
    } catch (ex) {
      logger.e('changeEmail exception : $ex');
      throw UnimedException('$ex');
    }
  }

  Future<void> renewPassword(
      String password, String newPass, String confirmPass) async {
    try {
      final token = await _getToken();
      final tokenPerfilApps =
          await Locator.instance!.get<AuthApi>().tokenPerfilApps();
      final url =
          '${FlavorConfig.instance!.values.profilePermissions.url}app/cooperado/prestador/senha?tokenPortal=$token';

      final body = {
        'senha': password,
        'novaSenha': newPass,
        'confirmaSenha': confirmPass,
      };

      final response = await httpClient.put(
        Uri.parse(url),
        body: jsonEncode(body),
        headers: {
          "Content-Type": "application/json",
          "Authorization": "Bearer $tokenPerfilApps",
        },
      );

      if (response.statusCode == 200) {
        logger.d('renewPassword, success. body: ${response.body}');
      } else {
        final message = jsonDecode(response.body)['message'];
        logger.e(
            'renewPassword statusCode : ${response.statusCode} - ${response.body}');
        throw UnimedException('$message');
      }
    } on UnimedException catch (ex) {
      logger.e('renewPassword ${ex.runtimeType} : $ex');
      throw UnimedException(ex.message);
    } catch (ex) {
      logger.e('renewPassword exception : $ex');
      throw UnimedException('Não foi possível no momento.');
    }
  }

  Future<PayloadAddress> updateProfile(
      String crm, PayloadAddress payloadAddress) async {
    try {
      final tokenPerfilApps =
          await Locator.instance!.get<AuthApi>().tokenPerfilApps();
      final url =
          '${FlavorConfig.instance!.values.profilePermissions.url}app/cooperado/prestador/$crm/enderecos';
      final headers = {
        "Content-Type": "application/json",
        "Authorization": "Bearer $tokenPerfilApps",
      };
      final body = jsonEncode(payloadAddress.toJson());

      final response = await httpClient.put(
        Uri.parse(url),
        headers: headers,
        body: body,
      );
      if (response.statusCode == 200) {
        final result = jsonDecode(response.body);
        logger.d(
            'updateProfile success. | list address: ${PayloadAddress.fromJson(result).enderecos!.length}');
        return PayloadAddress.fromJson(result);
      } else {
        final message = jsonDecode(response.body)['message'] ??
            'Não foi possível no momento.';
        logger.e(
            'updateProfile statusCode : ${response.statusCode} - response: ${response.body}');
        throw UnimedException('$message');
      }
    } on UnimedException catch (ex) {
      logger.e('updateProfile ${ex.runtimeType} : $ex');
      throw UnimedException(ex.message);
    } catch (ex) {
      logger.e('updateProfile exception : $ex');
      throw UnimedException('Não foi possível no momento.');
    }
  }

  Future<PayloadAddress> getAllAddresses(String? crm) async {
    try {
      final tokenPerfilApps =
          await Locator.instance!.get<AuthApi>().tokenPerfilApps();
      final url =
          '${FlavorConfig.instance!.values.profilePermissions.url}app/cooperado/prestador/$crm/enderecos';

      final response = await httpClient.get(
        Uri.parse(url),
        headers: {
          "Content-Type": "application/json",
          "Authorization": "Bearer $tokenPerfilApps",
        },
      );
      if (response.statusCode == 200) {
        final PayloadAddress payloadAddress =
            PayloadAddress.fromJson(jsonDecode(response.body));
        logger.d(
            'getAllAddresses, success. | list: ${payloadAddress.enderecos!.length}');
        return payloadAddress;
      } else {
        // final _message = jsonDecode(response.body)['message'];
        logger.e(
            'getAllAddresses statusCode : ${response.statusCode} - ${response.body}');
        throw UnimedException('Não foi possível no momento.');
      }
    } on UnimedException catch (ex) {
      logger.e('getAllAddresses ${ex.runtimeType} : $ex');
      throw UnimedException(ex.message);
    } catch (ex) {
      logger.e('getAllAddresses exception : $ex');
      throw UnimedException('Não foi possível no momento.');
    }
  }

  // String _getBasicAuth(String? username, String? password) {
  //   return 'Basic ' + base64Encode(utf8.encode('$username:$password'));
  // }
}
