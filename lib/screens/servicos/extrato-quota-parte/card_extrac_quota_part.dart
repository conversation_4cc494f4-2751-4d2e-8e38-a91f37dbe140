import 'package:cooperado_minha_unimed/bloc/sensitive-data/sensitive_data_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/sensitive-data/sensitive_data_state.dart';
import 'package:cooperado_minha_unimed/bloc/servicos/extrato-quota-part/extract_quota_part_cubit.dart';
import 'package:cooperado_minha_unimed/colors.dart';
import 'package:cooperado_minha_unimed/screens/servicos/extrato-quota-parte/details_screen.dart';
import 'package:cooperado_minha_unimed/shared/vo/servicos/extract_copart.vo.dart';
import 'package:cooperado_minha_unimed/shared/widgets/error-banner/error_banner.component.dart';
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';

class CardExtractQuotaPart extends StatefulWidget {
  final DateTime? lastDate;
  const CardExtractQuotaPart({super.key, this.lastDate});

  @override
  CardExtractQuotaPartState createState() => CardExtractQuotaPartState();
}

class CardExtractQuotaPartState extends State<CardExtractQuotaPart> {
  @override
  void initState() {
    _init();

    super.initState();
  }

  _init() {
    SchedulerBinding.instance.addPostFrameCallback((_) {
      context.read<ExtractQuotaPartCubit>().selectDate(widget.lastDate);
      context
          .read<ExtractQuotaPartCubit>()
          .getExtractQuotaPart(widget.lastDate);
    });
  }

  @override
  Widget build(BuildContext context) {
  return BlocBuilder<ExtractQuotaPartCubit, ExtractQuotaPartState>(
    builder: (context, state) {
      if (state is DoneGetExtractQuotaPartState) {
        return BlocBuilder<SensitiveDataCubit, SensitiveDataState>(
          builder: (context, sensitiveState) {
            bool isSensitiveDataVisible = sensitiveState.isSensitiveDataVisible;
            return Hero(
              tag: 'animation',
              child: Card(
                child: Container(
                  padding: const EdgeInsets.only(top: 8.0, left: 8.0, right: 8.0),
                  child: SingleChildScrollView(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: <Widget>[
                        _headerCard(context),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceAround,
                          children: [
                            Column(
                              children: [
                                _modelShowInfo(
                                  "Valor Integralizado",
                                  isSensitiveDataVisible
                                      ? state.extractQuotaPart.getSaldoIntegralizadoFormat()
                                      : '*****',
                                ),
                                _modelShowInfo(
                                  "Valor Subscrição",
                                  isSensitiveDataVisible
                                      ? state.extractQuotaPart.getTotalSubscricaoFormat()
                                      : '*****',
                                ),
                              ],
                            ),
                            Container(
                              height: 120,
                              width: 2,
                              color: Colors.grey.withOpacity(0.2),
                            ),
                            Column(
                              children: [
                                _modelShowInfo(
                                  "Valor a Integralizar",
                                  isSensitiveDataVisible
                                      ? state.extractQuotaPart.getSaldoIntegralizarFormat()
                                      : '*****',
                                ),
                                _modelShowInfo(
                                  "Valor Devolução",
                                  isSensitiveDataVisible
                                      ? state.extractQuotaPart.getTotalDevolucaoFormat()
                                      : '*****',
                                ),
                              ],
                            ),
                          ],
                        ),
                        const SizedBox(height: 20),
                        _buttonDetails(state.extractQuotaPart),
                      ],
                    ),
                  ),
                ),
              ),
            );
          },
        );
      } else if (state is LoadingGetExtractQuotaPartState) {
        return Card(
          child: Container(
            padding: const EdgeInsets.all(8.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: <Widget>[
                _headerCard(context),
                const SpinKitCircle(
                  color: CooperadoColors.tealGreen,
                ),
              ],
            ),
          ),
        );
      } else if (state is ErrorGetExtractQuotaPartState) {
        return Card(
          child: Container(
            padding: const EdgeInsets.all(8.0),
            child: Column(
              children: <Widget>[
                _headerCard(context),
                ErrorBanner(message: state.message),
              ],
            ),
          ),
        );
      } else {
        return Container();
      }
    },
  );
}

  Widget _headerCard(context) {
    return const Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: <Widget>[
        Padding(
          padding: EdgeInsets.symmetric(vertical: 8.0),
          child: Text(
            "Extrato Quota Parte",
            style: TextStyle(
                color: CooperadoColors.blackText,
                fontSize: 16,
                fontWeight: FontWeight.bold),
          ),
        ),
        //SizedBox(height: 300, child: ProducaoChart())
      ],
    );
  }

  Widget _modelShowInfo(String description, String value) {
    return Column(
      children: <Widget>[
        Padding(
          padding: const EdgeInsets.symmetric(vertical: 10),
          child: Text(description),
        ),
        Text(
          value,
          style: const TextStyle(
              color: CooperadoColors.limaColorDark, fontSize: 20),
        ),
        const SizedBox(
          height: 10,
        )
      ],
    );
  }

  Widget _buttonDetails(ExtractCopartVO extractQuotaPart) {
    return Align(
      child: InkWell(
        onTap: () {
          Navigator.push(
              context,
              PageRouteBuilder(
                  transitionDuration: const Duration(seconds: 1),
                  pageBuilder: (_, __, ___) =>
                      DetailsScreen(extractQuotaPart: extractQuotaPart)));
        },
        child: Container(
          padding: const EdgeInsets.only(
              top: 12.0, bottom: 12.0, left: 40, right: 40),
          decoration: const BoxDecoration(
              color: CooperadoColors.tealGreen,
              borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(20), topRight: Radius.circular(20))),
          child: const Text(
            "VEJA MAIS",
            style: TextStyle(
              //fontSize: 18,
              color: Colors.white,
            ),
          ),
        ),
      ),
    );
  }
}
