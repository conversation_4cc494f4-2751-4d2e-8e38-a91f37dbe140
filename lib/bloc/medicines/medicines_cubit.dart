import 'package:bloc/bloc.dart';
import 'package:cooperado_minha_unimed/models/medicine.model.dart';
import 'package:cooperado_minha_unimed/shared/api/services.api.dart';
import 'package:cooperado_minha_unimed/shared/locator.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';

part 'medicines_state.dart';

class MedicinesCubit extends Cubit<MedicinesState> {
  MedicinesCubit() : super(MedicinesInitial());

  syncAllMedicines() async {
    try {
      emit(LoadingMedicinesState());
      final servicesApi = Locator.instance!<ServicesApi>();
      final medicines = await servicesApi.getAllMedicines();

      await servicesApi.saveAllMedicinesInBox(medicines: medicines);

      emit(LoadedListMedicinesState(medicines: medicines));
    } catch (ex) {
      debugPrint(ex.toString());
      emit(ErrorMedicinesState('$ex'));
    }
  }

  getAllMedicines() async {
    try {
      emit(LoadingMedicinesState());
      final servicesApi = Locator.instance!<ServicesApi>();
      List<MedicineModel> medicinesOffline = [];
      medicinesOffline = await servicesApi.getAllMedicinesOffline();

      if (medicinesOffline.isEmpty) {
        await syncAllMedicines();
        medicinesOffline = await servicesApi.getAllMedicinesOffline();
      }

      emit(LoadedListMedicinesState(medicines: medicinesOffline));
    } catch (ex) {
      debugPrint(ex.toString());
      emit(ErrorMedicinesState('$ex'));
    }
  }
}
