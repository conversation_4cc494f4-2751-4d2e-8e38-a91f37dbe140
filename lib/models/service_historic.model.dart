import 'package:collection/collection.dart' show IterableExtension;
import 'package:flutter/material.dart';

class ResponseServiceHistoric {
  List<HistoricoServico>? mediaAtendimentosPrestador;
  List<HistoricoServico>? mediaAtendimentosEspecialidade;

  ResponseServiceHistoric.fromJson(Map<String, dynamic> json) {
    if (json['mediaAtendimentosPrestador'] != null) {
      mediaAtendimentosPrestador = [];
      json['mediaAtendimentosPrestador'].forEach((v) {
        mediaAtendimentosPrestador!.add(HistoricoServico.fromJson(v));
      });
    }
    if (json['mediaAtendimentosEspecialidade'] != null) {
      mediaAtendimentosEspecialidade = [];
      json['mediaAtendimentosEspecialidade'].forEach((v) {
        mediaAtendimentosEspecialidade!.add(HistoricoServico.fromJson(v));
      });
    }

    if (mediaAtendimentosEspecialidade!.length >
        mediaAtendimentosPrestador!.length) _ajustList();
  }

  void _ajustList() {
    //Verifica se a mediaHistoricoPrestador possui registro dos meses e tipo de servico que os registro da media da especialidade,
    //caso nao tenha, adiciona um registro com valor 0 para o mes

    for (HistoricoServico mediaEspecialidade
        in mediaAtendimentosEspecialidade!) {
      HistoricoServico? item = mediaAtendimentosPrestador!.firstWhereOrNull(
          (historicoServico) =>
              historicoServico.tipo == mediaEspecialidade.tipo &&
              historicoServico.mesAno == mediaEspecialidade.mesAno);

      if (item == null) {
        mediaAtendimentosPrestador!.add(HistoricoServico(
          mesPgto: mediaEspecialidade.mesPgto,
          anoPgto: mediaEspecialidade.anoPgto,
          tipo: mediaEspecialidade.tipo,
          total: "0",
        ));
      }
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};

    if (mediaAtendimentosPrestador != null) {
      data['mediaAtendimentosPrestador'] =
          mediaAtendimentosPrestador!.map((v) => v.toJson()).toList();
    }
    if (mediaAtendimentosEspecialidade != null) {
      data['mediaAtendimentosEspecialidade'] =
          mediaAtendimentosEspecialidade!.map((v) => v.toJson()).toList();
    }
    return data;
  }

  List<HistoricoServico> filterProviderHistoryByType(int? type) {
    List<HistoricoServico>? data = mediaAtendimentosPrestador;
    if (type == 0) {
      List<HistoricoServico> result = [];
      Map<String, double> mesesValor = <String, double>{};
      for (var element in data!) {
        mesesValor.update('${element.mesPgto}/${element.anoPgto}',
            (existingValue) => existingValue + double.parse(element.total!),
            ifAbsent: () => double.parse(element.total!));
      }
      mesesValor.forEach((key, value) {
        result.add(HistoricoServico(
            mesPgto: key.substring(0, key.indexOf('/')),
            anoPgto: key.substring(key.indexOf('/') + 1),
            total: '$value'));
      });

      return result;
    } else {
      debugPrint('tipo: ${tipoServicos[type!]}');
      return data!
          .where((element) => element.tipo == tipoServicos[type])
          .toList();
    }
  }

  List<HistoricoServico> filterSpecialtyHistoryByType(int? type) {
    List<HistoricoServico>? data = mediaAtendimentosEspecialidade;
    if (type == 0) {
      List<HistoricoServico> result = [];
      Map<String, double> mesesValor = <String, double>{};
      for (HistoricoServico element in data!) {
        mesesValor.update('${element.mesPgto}/${element.anoPgto}',
            (existingValue) => existingValue + double.parse(element.media!),
            ifAbsent: () => double.parse(element.media!));
      }
      mesesValor.forEach((key, value) {
        result.add(HistoricoServico(
            mesPgto: key.substring(0, key.indexOf('/')),
            anoPgto: key.substring(key.indexOf('/') + 1),
            media: '$value'));
      });
      return result;
    } else {
      return data!
          .where((element) => element.tipo == tipoServicos[type!])
          .toList();
    }
  }
}

const tipoServicos = {
  1: 'CONSULTA ELETIVA',
  2: 'SERVICOS DIVERSOS',
  3: 'HONORÁRIOS',
};

class HistoricService {
  String? mesPgto;
  String? anoPgto;
  String? unimedPrestador;
  String? codigoPrestador;
  String? tipo;
  String? codEspecialidade;
  String? nomeEspecialidade;
  String? total;

  HistoricService(
      {this.mesPgto,
      this.anoPgto,
      this.unimedPrestador,
      this.codigoPrestador,
      this.tipo,
      this.codEspecialidade,
      this.nomeEspecialidade,
      this.total});

  HistoricService.fromJson(Map<String, dynamic> json) {
    mesPgto = json['mesPgto'];
    anoPgto = json['anoPgto'];
    unimedPrestador = json['unimedPrestador'];
    codigoPrestador = json['codigoPrestador'];
    tipo = json['tipo'];
    codEspecialidade = json['codEspecialidade'];
    nomeEspecialidade = json['nomeEspecialidade'];
    total = json['total'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['mesPgto'] = mesPgto;
    data['anoPgto'] = anoPgto;
    data['unimedPrestador'] = unimedPrestador;
    data['codigoPrestador'] = codigoPrestador;
    data['tipo'] = tipo;
    data['codEspecialidade'] = codEspecialidade;
    data['nomeEspecialidade'] = nomeEspecialidade;
    data['total'] = total;
    return data;
  }
}

class HistoricoServico {
  String? mesPgto;
  String? anoPgto;
  String? unimedPrestador;
  String? codigoPrestador;
  String? tipo;
  String? codEspecialidade;
  String? nomeEspecialidade;
  String? total;
  String? media;

  String get ano2Digitos {
    final stringAno = '$anoPgto'.padLeft(4, '0');
    return stringAno.substring(2, 4).toString();
  }

  String get mesAno => '$mesPgto/$anoPgto';

  int services = 0, consults = 0, fees = 0, totalInt = 0;
  HistoricoServico(
      {this.mesPgto,
      this.anoPgto,
      this.unimedPrestador,
      this.codigoPrestador,
      this.tipo,
      this.codEspecialidade,
      this.nomeEspecialidade,
      this.total,
      this.media});

  int getDataToShow(int index) {
    switch (index) {
      case 0:
        return totalInt;
      case 1:
        return consults;
      case 2:
        return services;
      case 3:
        return fees;
      default:
        return 0;
    }
  }

  HistoricoServico.fromJson(Map<String, dynamic> json) {
    mesPgto = json['mesPgto'];
    anoPgto = json['anoPgto'];
    unimedPrestador = json['unimedPrestador'];
    codigoPrestador = json['codigoPrestador'];
    tipo = json['tipo'];
    codEspecialidade = json['codEspecialidade'];
    nomeEspecialidade = json['nomeEspecialidade'];
    total = json['total'];
    media = json['media'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['mesPgto'] = mesPgto;
    data['anoPgto'] = anoPgto;
    data['unimedPrestador'] = unimedPrestador;
    data['codigoPrestador'] = codigoPrestador;
    data['tipo'] = tipo;
    data['codEspecialidade'] = codEspecialidade;
    data['nomeEspecialidade'] = nomeEspecialidade;
    data['total'] = total;
    data['media'] = media;
    return data;
  }
}
