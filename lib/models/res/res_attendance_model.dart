import 'package:intl/intl.dart';

class ResAttendanceModel {
  String? codigo;
  String? tipo;
  String? nomeLocal;
  String? dataEntrada;
  String? dataAlta;
  String? codigoAtendimentoEncode;
  List<ItensAtendimento>? itensAtendimento;

  ResAttendanceModel(
      {this.codigo,
      this.tipo,
      this.nomeLocal,
      this.dataEntrada,
      this.dataAlta,
      this.codigoAtendimentoEncode,
      this.itensAtendimento});

  String get dataEntradaFormatted {
    if (dataEntrada == null) {
      return '';
    }
    final DateTime dateTime = DateTime.parse(dataEntrada!).toLocal();
    return DateFormat('dd/MM/yyyy - HH:mm:ss').format(dateTime);
  }

  ResAttendanceModel.fromJson(Map<String, dynamic> json) {
    codigo = json['codigo'];
    tipo = json['tipo'];
    nomeLocal = json['nomeLocal'];
    dataEntrada = json['dataEntrada'];
    dataAlta = json['dataAlta'];
    codigoAtendimentoEncode = json['codigoAtendimentoEncode'];
    if (json['itensAtendimento'] != null) {
      itensAtendimento = <ItensAtendimento>[];
      json['itensAtendimento'].forEach((v) {
        itensAtendimento!.add(ItensAtendimento.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['codigo'] = codigo;
    data['tipo'] = tipo;
    data['nomeLocal'] = nomeLocal;
    data['dataEntrada'] = dataEntrada;
    data['dataAlta'] = dataAlta;
    data['codigoAtendimentoEncode'] = codigoAtendimentoEncode;
    data['itensAtendimento'] =
        itensAtendimento?.map((v) => v.toJson()).toList();
    return data;
  }
}

class ItensAtendimento {
  late String descricao;

  ItensAtendimento({required this.descricao});

  ItensAtendimento.fromJson(Map<String, dynamic> json) {
    descricao = json['descricao'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['descricao'] = descricao;
    return data;
  }
}
