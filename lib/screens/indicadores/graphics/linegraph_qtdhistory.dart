import 'package:cooperado_minha_unimed/bloc/sensitive-data/sensitive_data_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/sensitive-data/sensitive_data_state.dart';
import 'package:cooperado_minha_unimed/colors.dart';
import 'package:cooperado_minha_unimed/shared/utils/graph_utils.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../attendace-comparative/line_chart_compartative.dart';

class LineGraphQuantityHistory extends StatefulWidget {
  const LineGraphQuantityHistory(
      {super.key,
      required this.dataMediaEspecialidade,
      required this.dataPrestador});

  final List<Valores> dataMediaEspecialidade;
  final List<Valores> dataPrestador;

  @override
  State<LineGraphQuantityHistory> createState() =>
      _LineGraphQuantityHistoryState();
}

class _LineGraphQuantityHistoryState extends State<LineGraphQuantityHistory> {
  List<Color> gradientColorsME = [
    CooperadoColors.tealGreen,
    CooperadoColors.tealGreen,
  ];
  List<Color> gradientColorsProvider = [
    CooperadoColors.green,
    CooperadoColors.green,
  ];
  int leftCount = 0;
  List<double> valuesYmediaEspecialidade = [];
  List<double> valuesYprestador = [];
  @override
  void initState() {
    // leftCount = 0;
    valuesYmediaEspecialidade.clear();
    valuesYprestador.clear();

    for (Valores element in widget.dataMediaEspecialidade) {
      valuesYmediaEspecialidade.add(double.parse(element.valor));
    }

    for (Valores element in widget.dataPrestador) {
      valuesYprestador.add(double.parse(element.valor));
    }
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<SensitiveDataCubit, SensitiveDataState>(
      builder: (context, state) {
        return Stack(
          children: <Widget>[
            AspectRatio(
              aspectRatio: 1.70,
              child: Padding(
                padding: const EdgeInsets.only(
                  right: 18,
                  left: 12,
                  top: 24,
                  bottom: 12,
                ),
                child: LineChart(
                  mainData(!state.isSensitiveDataVisible),
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  Widget bottomTitleWidgets(double value, TitleMeta meta) {
    List<String> dates = [];
    if (widget.dataMediaEspecialidade.isNotEmpty) {
      for (int i = 0; i < widget.dataMediaEspecialidade.length; i++) {
        dates.add(widget.dataMediaEspecialidade[i].mes);
      }
    } else if (widget.dataPrestador.isNotEmpty) {
      for (int i = 0; i < widget.dataPrestador.length; i++) {
        dates.add(widget.dataPrestador[i].mes);
      }
    }

    return SideTitleWidget(
      axisSide: meta.axisSide,
      child: Text(dates.isEmpty ? '' : dates[value.toInt()],
          style: const TextStyle(fontSize: 13)),
    );
  }

  double findTotalValue() {
    var valuesME = GraphUtils.findMax(valuesYmediaEspecialidade);
    var valuesPre = GraphUtils.findMax(valuesYprestador);
    double total = GraphUtils.findMax([valuesME, valuesPre]);
    return total;
  }

  Widget leftTitleWidgets(double value, TitleMeta meta) {
    double length = findTotalValue();
    String text = '';
    int valor = GraphUtils.convertToInterger(value);
    int dataLength = widget.dataPrestador.length;

    if (valor == 0) {
      text = "0";
    } else if (length < dataLength) {
      text = GraphUtils.priceToCurrency(length);
    } else {
      for (int i = 1; i <= 5; i++) {
        if (valor == GraphUtils.convertToInterger((length / dataLength) * i)) {
          text = GraphUtils.priceToCurrency((length / dataLength) * i);
          break;
        }
      }
    }

    return Padding(
      padding: const EdgeInsets.only(right: 10),
      child: Text(
        text,
        textAlign: TextAlign.right,
        style: const TextStyle(fontSize: 11),
      ),
    );
  }

  LineChartData mainData(bool isSensitiveDataVisible) {
    return LineChartData(
      gridData: FlGridData(
        show: true,
        drawHorizontalLine: true,
        drawVerticalLine: false,
        horizontalInterval: _getInterval(),
        verticalInterval: widget.dataPrestador.isEmpty
            ? 1
            : widget.dataPrestador.length.toDouble(),
        getDrawingHorizontalLine: (value) {
          return const FlLine(
            color: Colors.grey,
            strokeWidth: 0.8,
          );
        },
      ),
      titlesData: FlTitlesData(
        show: true,
        rightTitles: const AxisTitles(
          sideTitles: SideTitles(showTitles: false),
        ),
        topTitles: const AxisTitles(
          sideTitles: SideTitles(showTitles: false),
        ),
        bottomTitles: AxisTitles(
          sideTitles: SideTitles(
            showTitles: true,
            reservedSize: 30,
            interval: 1,
            getTitlesWidget: bottomTitleWidgets,
          ),
        ),
        leftTitles: AxisTitles(
          sideTitles: SideTitles(
            showTitles: !isSensitiveDataVisible,
            interval: _getInterval(),
            getTitlesWidget: leftTitleWidgets,
            reservedSize: 60,
          ),
        ),
      ),
      borderData: FlBorderData(
        show: true,
        border: const Border(bottom: BorderSide(), top: BorderSide()),
      ),
      minX: 0,
      maxX: widget.dataPrestador.length.toDouble() - 1,
      minY: 0,
      maxY: findTotalValue(),
      lineTouchData: LineTouchData(
        getTouchLineEnd: (data, index) => double.infinity,
        getTouchedSpotIndicator:
            (LineChartBarData barData, List<int> spotIndexes) {
          return spotIndexes.map((spotIndex) {
            return TouchedSpotIndicatorData(
              const FlLine(color: Colors.grey, strokeWidth: 3),
              FlDotData(
                getDotPainter: (spot, percent, barData, index) =>
                    FlDotCirclePainter(
                  radius: 8,
                  color: barData.color!,
                ),
              ),
            );
          }).toList();
        },
        touchTooltipData: LineTouchTooltipData(
          maxContentWidth: 100,
          tooltipBgColor: Colors.grey[300]!,
          getTooltipItems: (touchedSpots) {
            return touchedSpots.map((LineBarSpot touchedSpot) {
              final textStyle = TextStyle(
                color: touchedSpot.bar.color,
                fontWeight: FontWeight.bold,
                fontSize: 14,
              );
              return LineTooltipItem(
                touchedSpot.bar.color == CooperadoColors.tealGreen
                    ? (!isSensitiveDataVisible
          ? 'Média Especialidade\n  ${GraphUtils.priceToCurrency(touchedSpot.y)}'
          : 'Média Especialidade\n ****')
      : (!isSensitiveDataVisible
          ? 'Cooperado\n  ${GraphUtils.priceToCurrency(touchedSpot.y)}'
          : 'Cooperado\n ****'),
                textStyle,
              );
            }).toList();
          },
        ),
      ),
      lineBarsData: [
        LineChartBarData(
          spots: List.generate(
              valuesYmediaEspecialidade.length,
              (index) =>
                  FlSpot(index.toDouble(), valuesYmediaEspecialidade[index])),
          isCurved: false,
          color: CooperadoColors.tealGreen,
          barWidth: 3,
          isStrokeCapRound: true,
          dotData: const FlDotData(
            show: true,
          ),
          belowBarData: BarAreaData(
            show: true,
            gradient: LinearGradient(
              colors: gradientColorsME
                  .map((color) => color.withOpacity(0.1))
                  .toList(),
            ),
          ),
        ),
        LineChartBarData(
          spots: List.generate(valuesYprestador.length,
              (index) => FlSpot(index.toDouble(), valuesYprestador[index])),
          isCurved: false,
          color: CooperadoColors.green,
          barWidth: 3,
          isStrokeCapRound: true,
          dotData: const FlDotData(
            show: true,
          ),
          belowBarData: BarAreaData(
            show: true,
            gradient: LinearGradient(
              colors: gradientColorsProvider
                  .map((color) => color.withOpacity(0.1))
                  .toList(),
            ),
          ),
        ),
      ],
    );
  }

  double _getInterval() {
    if (findTotalValue() == 0 ||
        (findTotalValue() / widget.dataPrestador.length) < 1 ||
        (findTotalValue() / widget.dataMediaEspecialidade.length) < 1) {
      return 1;
    }
    return findTotalValue() / widget.dataPrestador.length;
  }
}
