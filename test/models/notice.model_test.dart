import 'package:cooperado_minha_unimed/models/notice.model.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  NoticeModel? modelTest;
  Map? json;
  setUpAll(
    () {
      modelTest = NoticeModel(
        id: 1,
        titulo: "titulo",
        status: "status",
        destinatario: "destinatario",
        assunto: "assusnto",
        arquivo: "arquivo",
        url: "https://url",
        seo: Seo(
          metaDescription: "metaDescription",
          metaTitle: "metaTitle",
          locale: "locale",
          image:
              "https://portalhmg.unimedfortaleza.com.br/portaluploads/uploads/2015/10/curso_online.jpg",
        ),
        imagem: "imagem",
        pagina: "pagina",
        paginado: true,
        lido: true,
        content: "content",
        tipoNoticia: ["tipoNoticia"],
        data: Data(
          yearOfEra: 2021,
          weekyear: 2,
          monthOfYear: 09,
          hourOfDay: 2,
          minuteOfHour: 2,
          secondOfMinute: 2,
          year: 2002,
          dayOfMonth: 07,
        ),
        areaRestrita: "areaRestrita",
        type: "type",
      );
      json = {
        "id": 102622,
        "titulo":
            "Colóquio: Ciência e Gestão da Pandemia por SARS-Cov2. Participe!",
        "status": null,
        "destinatario": null,
        "assunto": null,
        "arquivo": null,
        "url":
            "/noticias/coloquio-ciencia-e-gestao-da-pandemia-por-sars-cov2-participe",
        "seo": {
          "meta_description": "",
          "meta_title":
              "Colóquio: Ciência e Gestão da Pandemia por SARS-Cov2. Participe! - Unimed Fortaleza",
          "robots": "index,follow",
          "type": "website",
          "locale": "pt_BR",
          "image":
              "https://portalhmg.unimedfortaleza.com.br/portaluploads/uploads/2015/10/curso_online.jpg"
        },
        "imagem": null,
        "pagina": null,
        "paginado": true,
        "lido": false,
        "content":
            "<img class=\"img-responsive alignnone wp-image-102623 size-full\" src=\"https://www.unimedfortaleza.com.br/portaluploads/uploads/2021/03/Coloquio-Ciencia-e-Gestao-da-Pandemia.jpg\" alt=\"coloquio-ciencia-e-gestao-da-pandemia\" width=\"800\" height=\"1040\" data-id=\"102623\" />\r\n<h2>Ficou interessado?</h2>\r\n<a href=\"https://docs.google.com/forms/d/e/1FAIpQLScSTG7L07BIoPRKj9R1Hp8K5LX2SeDwXHPEOps8pMeSZHowVw/viewform\">Inscreva-se <strong>AQUI!</strong></a>\r\n<h2>Participe!</h2>",
        "tipo_noticia": ["NOTICIAS"],
        "tipo_noticia_blog": [],
        "tags": [],
        "posts_relacionados": null,
        "data": {
          "chronology": {
            "zone": {"fixed": true, "id": "UTC"}
          },
          "centuryOfEra": 20,
          "yearOfEra": 2021,
          "yearOfCentury": 21,
          "weekyear": 2021,
          "monthOfYear": 3,
          "weekOfWeekyear": 12,
          "hourOfDay": 14,
          "minuteOfHour": 24,
          "secondOfMinute": 54,
          "millisOfSecond": 0,
          "millisOfDay": 51894000,
          "year": 2021,
          "dayOfMonth": 23,
          "dayOfWeek": 2,
          "era": 1,
          "dayOfYear": 82,
          "fieldTypes": [
            {
              "rangeDurationType": null,
              "durationType": {"name": "years"},
              "name": "year"
            },
            {
              "rangeDurationType": {"name": "years"},
              "durationType": {"name": "months"},
              "name": "monthOfYear"
            },
            {
              "rangeDurationType": {"name": "months"},
              "durationType": {"name": "days"},
              "name": "dayOfMonth"
            },
            {
              "rangeDurationType": {"name": "days"},
              "durationType": {"name": "millis"},
              "name": "millisOfDay"
            }
          ],
          "values": [2021, 3, 23, 51894000],
          "fields": [
            {
              "rangeDurationField": null,
              "leapDurationField": {
                "unitMillis": 86400000,
                "precise": true,
                "name": "days",
                "type": {"name": "days"},
                "supported": true
              },
              "minimumValue": -292275054,
              "maximumValue": 292278993,
              "lenient": false,
              "durationField": {
                "unitMillis": 31556952000,
                "precise": false,
                "name": "years",
                "type": {"name": "years"},
                "supported": true
              },
              "name": "year",
              "type": {
                "rangeDurationType": null,
                "durationType": {"name": "years"},
                "name": "year"
              },
              "supported": true
            },
            {
              "rangeDurationField": {
                "unitMillis": 31556952000,
                "precise": false,
                "name": "years",
                "type": {"name": "years"},
                "supported": true
              },
              "leapDurationField": {
                "unitMillis": 86400000,
                "precise": true,
                "name": "days",
                "type": {"name": "days"},
                "supported": true
              },
              "minimumValue": 1,
              "maximumValue": 12,
              "lenient": false,
              "durationField": {
                "unitMillis": 2629746000,
                "precise": false,
                "name": "months",
                "type": {"name": "months"},
                "supported": true
              },
              "name": "monthOfYear",
              "type": {
                "rangeDurationType": {"name": "years"},
                "durationType": {"name": "months"},
                "name": "monthOfYear"
              },
              "supported": true
            },
          ]
        },
        "excerpt": null,
        "areaRestrita": "NAO",
        "local_publicacao": ["COOPERADO"],
        "type": null,
        "custom_fields": {
          "lotacao": null,
          "responsavel": null,
          "carga_horaria": null,
          "horario_entrada": null,
          "horario_saida": null,
          "limite_de_entrega": null,
          "escolaridade": null,
          "perfil_candidato": null,
          "conhecimentos_necessarios": null,
          "atividade_desempenhadas": null,
          "email_do_responsavel": null,
          "salario_e_beneficios": null,
          "pagina_personalizada": null,
          "exibir_como_html": null,
          "tipo_conteudo": null,
          "modelo_header": null,
          "header_subtitulo": null,
          "imagem_do_header": null,
          "exibir_sidebar": null,
          "escolha_uma_sidebar": null,
          "escolha_uma_sidebar_personalizada": null,
          "html_sidebar": null,
          "imagem_personalizada_sidebar": null
        }
      };
    },
  );

  group(
    "isInstanceOf NoticeModel model tests",
    () {
      test("Should be return instance of NoticeModel", () {
        expect(modelTest, isInstanceOf<NoticeModel>());
      });

      test("Should be return instance of String", () {
        expect(modelTest!.status, isInstanceOf<String>());
      });
      test("Should be return instance of Seo", () {
        expect(modelTest!.seo, isInstanceOf<Seo>());
      });
      test("Should be return instance of Data", () {
        expect(modelTest!.data, isInstanceOf<Data>());
      });

      test("Should be return instance of Bool", () {
        expect(modelTest!.lido, isInstanceOf<bool>());
      });

      test("Should be return instance of Int", () {
        expect(modelTest!.id, isInstanceOf<int>());
      });
    },
  );

  group("Json test", () {
    test("Should be return instance of NoticeModel to json", () {
      expect(modelTest!.toJson(), isInstanceOf<Map<dynamic, dynamic>>());
    });
    test("Should be return instance of NoticeModel from json", () {
      final json = modelTest!.toJson();
      expect(NoticeModel.fromJson(json), isInstanceOf<NoticeModel>());
    });
    test("Should be return instance of Seo to json", () {
      expect(modelTest!.seo!.toJson(), isInstanceOf<Map<dynamic, dynamic>>());
    });
    test("Should be return instance of Seo from json", () {
      final json = modelTest!.seo!.toJson();
      expect(Seo.fromJson(json), isInstanceOf<Seo>());
    });

    test("Should be return instance of Data to json", () {
      expect(modelTest!.data!.toJson(), isInstanceOf<Map<dynamic, dynamic>>());
    });
    test("Should be return instance of Data from json", () {
      final json = modelTest!.data!.toJson();
      expect(Data.fromJson(json), isInstanceOf<Data>());
    });
  });
  group(
    "Other tests",
    () {
      test("Should be return true", () {
        expect(modelTest!.seo!.image!.contains("https://"), true);
      });

      test("Should be return true if is a valid url", () {
        if (json!["seo"] != null) {
          expect(json!["seo"]["image"].contains("https://"), true);
        }
      });
      test("Should be return true if is a valid extension", () {
        if (json!["seo"] != null) {
          expect(json!["seo"]["image"].contains(".jpg"), true);
        }
      });
      test("Should be return true", () {
        expect(modelTest!.data!.dataFormatted, "07/09/2002");
      });
    },
  );

  group('Json test', () {
    test("Type test json", () {
      expect(json!["id"], isInstanceOf<int>());
      expect(json!["titulo"], isInstanceOf<String>());
      expect(json!["status"], isInstanceOf<String?>());
      expect(json!["destinatario"], isInstanceOf<String?>());
      expect(json!["assunto"], isInstanceOf<String?>());
      expect(json!["arquivo"], isInstanceOf<String?>());
      expect(json!["url"], isInstanceOf<String?>());
      if (json!["seo"] != null) {
        expect(json!["seo"]["meta_description"], isInstanceOf<String?>());
        expect(json!["seo"]["meta_title"], isInstanceOf<String?>());
        expect(json!["seo"]["robots"], isInstanceOf<String?>());
        expect(json!["seo"]["type"], isInstanceOf<String?>());
        expect(json!["seo"]["locale"], isInstanceOf<String?>());
        expect(json!["seo"]["image"], isInstanceOf<String?>());
      }
      expect(json!["imagem"], isInstanceOf<String?>());
      expect(json!["pagina"], isInstanceOf<String?>());
      expect(json!["paginado"], isInstanceOf<bool>());
      expect(json!["lido"], isInstanceOf<bool>());
      expect(json!["content"], isInstanceOf<String?>());
      expect(json!["tipo_noticia"], isInstanceOf<List<String?>>());
      if (json!["data"] != null) {
        expect(json!["data"]["yearOfEra"], isInstanceOf<int>());
        expect(json!["data"]["weekyear"], isInstanceOf<int>());
        expect(json!["data"]["monthOfYear"], isInstanceOf<int>());
        expect(json!["data"]["hourOfDay"], isInstanceOf<int>());
        expect(json!["data"]["minuteOfHour"], isInstanceOf<int>());
        expect(json!["data"]["secondOfMinute"], isInstanceOf<int>());
        expect(json!["data"]["year"], isInstanceOf<int>());
        expect(json!["data"]["dayOfMonth"], isInstanceOf<int>());
      }
      expect(json!["areaRestrita"], isInstanceOf<String?>());
      expect(json!["type"], isInstanceOf<String?>());
    });
  });
}
