import 'package:collection/collection.dart' show IterableExtension;
import 'package:cooperado_minha_unimed/models/diretoria.model.dart';
import 'package:cooperado_minha_unimed/shared/api/diretoria.api.dart';
import 'package:cooperado_minha_unimed/shared/locator.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

part 'directors_state.dart';

class DirectorsCubit extends Cubit<DirectorsState> {
  DirectorsCubit() : super(InitialDirectorsState());

  getDirectors() async {
    try {
      emit(LoadingDirectorsState());
      final retornoDiretoria = await Locator.instance!<DiretoriaApi>().getDiretoria();

      final president = retornoDiretoria.diretores!.firstWhereOrNull((element) => element.cargo == 'Presidente');
      final diretoriaNoPresident = retornoDiretoria.diretores!.where((element) => element.cargo != 'Presidente').toList();

      emit(LoadedDirectorsState(retornoDiretoria: retornoDire<PERSON>, president: president, diretoriaNoPresident: diretoriaNoPresident));
    } catch (e) {
      emit(ErrorDirectorsState(e.toString()));
    }
  }
}
