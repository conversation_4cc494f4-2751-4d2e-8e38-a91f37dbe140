class ResponseDailyAttendance {
  String? dataAutorizacao;
  List<Procedimentos>? procedimentos;

  ResponseDailyAttendance({this.dataAutorizacao, this.procedimentos});

  ResponseDailyAttendance.fromJson(Map<String, dynamic> json) {
    dataAutorizacao = json['dataAutorizacao'];
    if (json['procedimentos'] != null) {
      procedimentos = [];
      json['procedimentos'].forEach((v) {
        procedimentos!.add(Procedimentos.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['dataAutorizacao'] = dataAutorizacao;
    if (procedimentos != null) {
      data['procedimentos'] = procedimentos!.map((v) => v.toJson()).toList();
    }
    return data;
  }

  ResponseDailyGraphic mountObject() {
    var services = 0.0, consults = 0.0, fees = 0.0;
    for (Procedimentos element in procedimentos!) {
      var dateservices = 0, dateConsults = 0, datefees = 0;
      if (element.tipo!.descricao == 'SERVIÇOS DIVERSOS') {
        services += dateservices += element.quantidade!;
      } else if (element.tipo!.descricao == 'HONORÁRIOS') {
        fees += datefees += element.quantidade!;
      } else {
        consults += dateConsults += element.quantidade!;
      }
    }
    return ResponseDailyGraphic(
        service: Service(
            total: services,
            percent: (services / (services + consults + fees)) * 100),
        fee: Fees(
            total: fees, percent: (fees / (services + consults + fees)) * 100),
        consults: Consults(
            total: consults,
            percent: (consults / (services + consults + fees)) * 100),
        average: services / consults);
  }
}

class Procedimentos {
  int? quantidade;
  Tipo? tipo;

  Procedimentos({this.quantidade, this.tipo});

  Procedimentos.fromJson(Map<String, dynamic> json) {
    quantidade = json['quantidade'];
    tipo = json['tipo'] != null ? Tipo.fromJson(json['tipo']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['quantidade'] = quantidade;
    if (tipo != null) {
      data['tipo'] = tipo!.toJson();
    }
    return data;
  }
}

class Tipo {
  String? descricao;

  Tipo({this.descricao});

  Tipo.fromJson(Map<String, dynamic> json) {
    descricao = json['descricao'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['descricao'] = descricao;
    return data;
  }
}

class ResponseDailyGraphic {
  String? total;
  Service? service;
  Fees? fee;
  Consults? consults;
  double? average;
  ResponseDailyGraphic(
      {this.total, this.service, this.fee, this.consults, this.average});
}

class Service {
  Service({double? total, double? percent});
}

class Fees {
  Fees({double? total, double? percent});
}

class Consults {
  Consults({double? total, double? percent});
}
