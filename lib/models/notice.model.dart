import 'package:intl/intl.dart';

class NoticeModel {
  int? id;
  String? titulo;
  String? status;
  String? destinatario;
  String? assunto;
  String? arquivo;
  String? url;
  Seo? seo;
  String? imagem;
  String? pagina;
  bool? paginado;
  bool? lido;
  String? content;
  List<String>? tipoNoticia;
  Data? data;
  String? areaRestrita;
  String? type;

  NoticeModel(
      {this.id,
      this.titulo,
      this.status,
      this.destinatario,
      this.assunto,
      this.arquivo,
      this.url,
      this.seo,
      this.imagem,
      this.pagina,
      this.paginado,
      this.lido,
      this.content,
      this.tipoNoticia,
      this.data,
      this.areaRestrita,
      this.type});

  NoticeModel.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    titulo = json['titulo'];
    status = json['status'];
    destinatario = json['destinatario'];
    assunto = json['assunto'];
    arquivo = json['arquivo'];
    url = json['url'];
    seo = json['seo'] != null ? Seo.fromJson(json['seo']) : null;
    imagem = json['imagem'];
    pagina = json['pagina'];
    paginado = json['paginado'];
    lido = json['lido'];
    content = json['content'];
    tipoNoticia = json['tipo_noticia'].cast<String>();
    data = json['data'] != null ? Data.fromJson(json['data']) : null;
    areaRestrita = json['areaRestrita'];
    type = json['type'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['titulo'] = titulo;
    data['status'] = status;
    data['destinatario'] = destinatario;
    data['assunto'] = assunto;
    data['arquivo'] = arquivo;
    data['url'] = url;
    if (seo != null) {
      data['seo'] = seo!.toJson();
    }
    data['imagem'] = imagem;
    data['pagina'] = pagina;
    data['paginado'] = paginado;
    data['lido'] = lido;
    data['content'] = content;
    data['tipo_noticia'] = tipoNoticia;
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    data['areaRestrita'] = areaRestrita;
    data['type'] = type;
    return data;
  }
}

class Seo {
  String? metaDescription;
  String? metaTitle;
  String? locale;
  String? image;

  Seo({this.metaDescription, this.metaTitle, this.locale, this.image});

  Seo.fromJson(Map<String, dynamic> json) {
    metaDescription = json['meta_description'];
    metaTitle = json['meta_title'];
    locale = json['locale'];
    image = json['image'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['meta_description'] = metaDescription;
    data['meta_title'] = metaTitle;
    data['locale'] = locale;
    data['image'] = image;
    return data;
  }
}

class Data {
  int? yearOfEra;
  int? weekyear;
  int? monthOfYear;
  int? hourOfDay;
  int? minuteOfHour;
  int? secondOfMinute;
  int? year;
  int? dayOfMonth;

  Data(
      {this.yearOfEra,
      this.weekyear,
      this.monthOfYear,
      this.hourOfDay,
      this.minuteOfHour,
      this.secondOfMinute,
      this.year,
      this.dayOfMonth});

  String get dataFormatted => DateFormat("dd/MM/yyyy")
      .format(DateTime.utc(year!, monthOfYear!, dayOfMonth!));

  Data.fromJson(Map<String, dynamic> json) {
    yearOfEra = json['yearOfEra'];
    weekyear = json['weekyear'];
    monthOfYear = json['monthOfYear'];
    hourOfDay = json['hourOfDay'];
    minuteOfHour = json['minuteOfHour'];
    secondOfMinute = json['secondOfMinute'];
    year = json['year'];
    dayOfMonth = json['dayOfMonth'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['yearOfEra'] = yearOfEra;
    data['weekyear'] = weekyear;
    data['monthOfYear'] = monthOfYear;
    data['hourOfDay'] = hourOfDay;
    data['minuteOfHour'] = minuteOfHour;
    data['secondOfMinute'] = secondOfMinute;
    data['year'] = year;
    data['dayOfMonth'] = dayOfMonth;
    return data;
  }
}
