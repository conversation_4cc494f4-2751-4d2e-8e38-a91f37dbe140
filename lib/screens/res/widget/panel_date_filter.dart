import 'package:cooperado_minha_unimed/colors.dart';
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';

class PanelDateFilter extends StatelessWidget {
  const PanelDateFilter(
      {super.key,
      required this.dateStartFilter,
      required this.dateEndFilter,
      required this.onSelectDates,
      this.callendarFirstDate});
  final DateTime dateStartFilter;
  final DateTime dateEndFilter;
  final DateTime? callendarFirstDate;
  final Function(DateTime, DateTime) onSelectDates;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(
        left: 24,
        right: 24,
        top: 8,
        bottom: 8,
      ),
      child: Container(
        alignment: Alignment.topCenter,
        width: MediaQuery.of(context).size.width,
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.all(Radius.circular(8)),
          boxShadow: [
            BoxShadow(
              color: CooperadoColors.grayLight2,
              blurRadius: 3.0,
              spreadRadius: 2.0,
              offset: Offset(0.0, 3.0),
            ),
          ],
        ),
        child: Container(
          padding: const EdgeInsets.symmetric(vertical: 4.0, horizontal: 15),
          child: Center(
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: InkWell(
                    onTap: () {
                      _selectDateRange(
                        context: context,
                        initialDate: dateStartFilter,
                        isStartDate: true,
                      );
                    },
                    child: Row(
                      children: [
                        const Icon(
                          Icons.calendar_today_outlined,
                          color: CooperadoColors.greenDark,
                        ),
                        const SizedBox(
                          width: 10,
                        ),
                        SingleChildScrollView(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const Text(
                                "Data inicial",
                                style: TextStyle(
                                  fontSize: 12,
                                  color: CooperadoColors.grayDark,
                                ),
                              ),
                              Text(
                                "${dateStartFilter.day.toString().padLeft(2, '0')}/${dateStartFilter.month.toString().padLeft(2, '0')}/${dateStartFilter.year.toString().padLeft(2, '0')}",
                                style: const TextStyle(
                                  color: CooperadoColors.grayDark2,
                                ),
                              )
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                InkWell(
                  onTap: () {
                    _selectDateRange(
                      context: context,
                      initialDate: dateEndFilter,
                      isStartDate: false,
                    );
                  },
                  child: Row(
                    children: [
                      const Icon(
                        Icons.calendar_today_outlined,
                        color: CooperadoColors.greenDark,
                      ),
                      const SizedBox(
                        width: 10,
                      ),
                      SingleChildScrollView(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const Text(
                              "Data final",
                              style: TextStyle(
                                fontSize: 12,
                                color: CooperadoColors.grayDark,
                              ),
                            ),
                            Text(
                              "${dateEndFilter.day.toString().padLeft(2, '0')}/${dateEndFilter.month.toString().padLeft(2, '0')}/${dateEndFilter.year.toString().padLeft(2, '0')}",
                              style: const TextStyle(
                                color: CooperadoColors.grayDark2,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _selectDateRange(
      {required BuildContext context,
      required DateTime initialDate,
      required bool isStartDate}) async {
    DateTime? selectedDate;
    DateTime firstDate = callendarFirstDate ??
        DateTime.now().subtract(
          const Duration(days: 90),
        );
    final lastDate = DateTime.now();

    if (initialDate.isBefore(firstDate)) {
      firstDate = initialDate;
    }

    selectedDate = await showDatePicker(
      context: context,
      helpText: 'Selecione a data ${isStartDate ? 'inicial' : 'final'}',
      initialDate: initialDate,
      firstDate: firstDate,
      lastDate: lastDate,
      keyboardType: TextInputType.text,
      builder: (BuildContext context, Widget? child) {
        return Theme(
          data: ThemeData.light().copyWith(
            colorScheme: const ColorScheme.light(
              primary: CooperadoColors.green,
              onPrimary: Colors.white,
              onSurface: Colors.black,
            ),
          ),
          child: child!,
        );
      },
    );

    if (selectedDate != null && isStartDate) {
      if (selectedDate.isBefore(dateEndFilter)) {
        onSelectDates(selectedDate, dateEndFilter);
      } else {
        if (context.mounted) {
          _showMessage(
            context,
            message: 'A data inicial deve ser anterior a data final',
          );
        }
      }
    } else if (selectedDate != null && !isStartDate) {
      if (dateStartFilter.isBefore(selectedDate)) {
        onSelectDates(dateStartFilter, selectedDate);
      } else {
        if (context.mounted) {
          _showMessage(
            context,
            message: 'A data inicial deve ser anterior a data final',
          );
        }
      }
    }
  }

  void _showMessage(BuildContext context, {required String message}) {
    ScaffoldMessenger.of(context).hideCurrentSnackBar();
    SchedulerBinding.instance.addPostFrameCallback((_) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: Colors.red,
        ),
      );
    });
  }
}
