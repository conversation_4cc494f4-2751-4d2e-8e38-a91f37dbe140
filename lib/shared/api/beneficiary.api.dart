import 'dart:convert';

import 'package:cooperado_minha_unimed/models/beneficiary_card.model.dart';
import 'package:cooperado_minha_unimed/shared/api/auth.api.dart';
import 'package:cooperado_minha_unimed/shared/exceptions.dart';
import 'package:cooperado_minha_unimed/shared/flavor_config.dart';
import 'package:http_client/http_client.dart';
import 'package:cooperado_minha_unimed/shared/locator.dart';
import 'package:cooperado_minha_unimed/shared/utils/logger_print.dart';

class BeneficiaryApi {
  final UnimedHttpClient httpClient;

  BeneficiaryApi(this.httpClient);

  final logger = UnimedLogger(className: 'BeneficiaryApi');
  final Duration timeout = const Duration(seconds: 30);

  Future<List<BeneficiaryCardModel>> getCadsData({
    required String cpf,
  }) async {
    final tokenPerfilApps =
        await Locator.instance!.get<AuthApi>().tokenPerfilApps();

    try {
      final url =
          '${FlavorConfig.instance!.values.profilePermissions.url}beneficiary/cards/$cpf/cpf';

      final response = await httpClient.get(
        Uri.parse(url),
        headers: {
          "Content-Type": "application/json",
          "Authorization": "Bearer $tokenPerfilApps",
        },
      );

      final data = jsonDecode(response.body);
      if (response.statusCode == 200) {
        final collection = (data as List)
            .map<BeneficiaryCardModel>((e) => BeneficiaryCardModel.fromJson(e))
            .toList();

        logger.d('getCadsData success list ${collection.length}');
        return collection;
      } else {
        final message = data['message'] ?? 'Não foi possível no momento.';
        logger.e(
            'getCadsData statusCode : ${response.statusCode} ${response.body}');
        throw UnimedException('$message');
      }
    } on UnimedException {
      rethrow;
    } catch (ex) {
      logger.e('getAllInvoice exception : $ex');
      throw UnimedException('Não foi possível no momento.');
    }
  }
}
