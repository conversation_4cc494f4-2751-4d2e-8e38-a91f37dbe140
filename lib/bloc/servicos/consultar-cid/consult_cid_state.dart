import 'package:cooperado_minha_unimed/shared/vo/servicos/cid_list.vo.dart';
import 'package:equatable/equatable.dart';

abstract class ConsultCidState extends Equatable {
  const ConsultCidState();
}

class ConsultCidInitial extends ConsultCidState {
  @override
  List<Object> get props => [];
}

class LoadingGetConsultCidState extends ConsultCidState {
  @override
  List<Object> get props => [];
}

class ErrorGetConsultCidState extends ConsultCidState {
  final String message;
  @override
  List<Object> get props => [message];
  const ErrorGetConsultCidState(this.message);
}

class DoneGetConsultCidState extends ConsultCidState {
  final CidList cidList;
  @override
  List<Object> get props => [cidList];

  const DoneGetConsultCidState(this.cidList);
}
