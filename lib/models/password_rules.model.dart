class PasswordRulesModel {
  final String regex;
  final String message;
  final bool required;

  PasswordRulesModel({
    required this.regex,
    required this.message,
    required this.required,
  });

  PasswordRulesModel.fromJson(Map<String, dynamic> json)
      : regex = json['regex'] as String,
        message = json['message'] as String,
        required = json['required'] as bool;

  Map<String, dynamic> toJson() =>
      {'regex': regex, 'message': message, 'required': required};
}
