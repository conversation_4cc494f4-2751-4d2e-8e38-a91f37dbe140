import 'package:cooperado_minha_unimed/models/club-mais-vantagens.model.dart';
import 'package:equatable/equatable.dart';

abstract class ClubMoreBenefitsState extends Equatable {
  const ClubMoreBenefitsState();

  @override
  List<Object> get props => [];
}

class ClubMoreBenefitsInitialState extends ClubMoreBenefitsState {}

class LoadingClubMoreBenefitsState extends ClubMoreBenefitsState {}

class DoneRegisterUserClubeMaisVantagens extends ClubMoreBenefitsState {}

class DoneCheckUserRegistrationState extends ClubMoreBenefitsState {
  final ClubMaisVantagensModel clubMaisVantagens;

  const DoneCheckUserRegistrationState({required this.clubMaisVantagens});
}

class ErrorClubMoreBenefitsState extends ClubMoreBenefitsState {
  final String message;

  @override
  List<Object> get props => [];

  const ErrorClubMoreBenefitsState({required this.message});
}
