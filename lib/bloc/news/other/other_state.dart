part of 'other_cubit.dart';

abstract class OtherState extends Equatable {
  const OtherState();

  @override
  List<Object?> get props => [];
}

class OtherInitial extends OtherState {}

class LoadingOthersNewsState extends OtherState {
  @override
  List<Object> get props => [];
}

class LoadingOthersNewsStatePagination extends OtherState {
  final List<Noticia> list;
  @override
  List<Object> get props => [list];

  const LoadingOthersNewsStatePagination({required this.list});
}

class ErrorOthersNewsState extends OtherState {
  final String message;
  @override
  List<Object> get props => [message];
  const ErrorOthersNewsState(this.message);
}

class DoneOthersNewsState extends OtherState {
  final List<Noticia> list;
  @override
  List<Object> get props => [list];

  const DoneOthersNewsState({required this.list});
}
