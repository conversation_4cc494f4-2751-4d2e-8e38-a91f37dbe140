package br.com.unimedfortaleza.minhaunimed

import io.flutter.embedding.android.FlutterActivity
import io.flutter.embedding.android.FlutterFragmentActivity
import io.flutter.embedding.engine.FlutterEngine
import io.flutter.plugin.common.MethodChannel
import androidx.annotation.NonNull

class MainActivity: FlutterFragmentActivity() {

      private val CHANNEL = "cooperado/sendNotification"


    override fun configureFlutterEngine(@NonNull flutterEngine: FlutterEngine) {
        super.configureFlutterEngine(flutterEngine)

           MethodChannel(flutterEngine.dartExecutor.binaryMessenger, CHANNEL).setMethodCallHandler { call, result ->
            if(call.method == "sendNotification"){
                val title =  call.argument<String>("title")
                val content =  call.argument<String>("content")

                val notificationHelper = NotificationHelper(applicationContext)
                notificationHelper.createChannel()
                notificationHelper.createNotication(1001,title.toString(), content.toString(),false)

                result.success("Notificateion Created")
            }
            else {
                    result.notImplemented()
            }
           
           }
    }
}
