import 'package:cooperado_minha_unimed/screens/transparencia/loss_ratio/widgets/loss_ratio_table_row.dart';
import 'package:cooperado_minha_unimed/shared/vo/transparencia/indices.dart';
import 'package:flutter/material.dart';

class LossRatioTable extends StatefulWidget {
  final VOIndicatorModel? lossRatio;

  const LossRatioTable({super.key, this.lossRatio});
  @override
  LossRatioTableState createState() => LossRatioTableState();
}

class LossRatioTableState extends State<LossRatioTable> {
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Column(children: <Widget>[
      _rowHeader(),
      ..._rowsBody(),
    ]);
  }

  Widget _rowHeader() {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8.0),
      child: Row(
        children: <Widget>[
          Expanded(
            flex: 4,
            child: Container(),
          ),
          const Expanded(
            flex: 4,
            child: Text(
              'Mensal',
              textAlign: TextAlign.center,
            ),
          ),
          const Expanded(
            flex: 4,
            child: Text(
              'Acumulado',
              textAlign: TextAlign.center,
            ),
          ),
          const Expanded(
            flex: 4,
            child: Text(
              'Projetado',
              textAlign: TextAlign.center,
            ),
          ),
        ],
      ),
    );
  }

  List<Widget> _rowsBody() {
    return widget.lossRatio!.data!
        .map(
          (item) => LossRatioTableRow(
            monthValue: item.monthValue,
            totalValue: item.totalValue,
            projectedValue: item.projectedValue,
            refYear: item.referenceYear,
            refMonth: item.referenceMonth,
          ),
        )
        .toList();
  }
}
