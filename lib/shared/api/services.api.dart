import 'dart:convert';
import 'dart:io';

import 'package:cooperado_minha_unimed/models/glosa_resource/file-attach.model.dart';
import 'package:cooperado_minha_unimed/models/glosa_resource/glosa_resource.model.dart';
import 'package:cooperado_minha_unimed/models/glosa_resource/glose_file.vo.dart';
import 'package:cooperado_minha_unimed/models/medicine.model.dart';
import 'package:cooperado_minha_unimed/models/user.model.dart';
import 'package:cooperado_minha_unimed/objectbox.g.dart';
import 'package:cooperado_minha_unimed/shared/api/auth.api.dart';
import 'package:cooperado_minha_unimed/shared/exceptions.dart';
import 'package:cooperado_minha_unimed/shared/flavor_config.dart';
import 'package:cooperado_minha_unimed/shared/locator.dart';
import 'package:cooperado_minha_unimed/shared/messages.exceptions.dart';
import 'package:cooperado_minha_unimed/shared/utils/file.dart';
import 'package:cooperado_minha_unimed/shared/utils/logger_print.dart';
import 'package:cooperado_minha_unimed/shared/utils/string_utils.dart';
import 'package:cooperado_minha_unimed/shared/vo/servicos/cid_list.vo.dart';
import 'package:cooperado_minha_unimed/shared/vo/servicos/extract_copart.vo.dart';
import 'package:cooperado_minha_unimed/shared/vo/servicos/honorary_solicitation.vo.dart';
import 'package:cooperado_minha_unimed/shared/vo/servicos/opme_list.vo.dart';
import 'package:cooperado_minha_unimed/shared/vo/servicos/procedure_list.vo.dart';
import 'package:http/http.dart';
import 'package:http_client/http_client.dart';
import 'package:intl/intl.dart';
import 'package:path_provider/path_provider.dart';

class ServicesApi {
  final UnimedHttpClient httpClient;

  ServicesApi(this.httpClient);

  final logger = UnimedLogger(className: 'ServicesApi');

  Future<ExtractCopartVO> getExtractCopart(
      {required DateTime lastProduction}) async {
    try {
      UserCredentials? credentials =
          await Locator.instance!.get<AuthApi>().getCredentials();

      final token = credentials != null
          ? await User.createToken(credentials: credentials)
          : "";
      final tokenPerfilApps =
          await Locator.instance!.get<AuthApi>().tokenPerfilApps();

      final dataInicial = DateFormat("dd-MM-yyyy").format(lastProduction);
      final dataFinal = DateFormat("dd-MM-yyyy").format(DateTime.now());

      final url =
          '${FlavorConfig.instance!.values.profilePermissions.url}app/cooperado/extrato-cotapart/inicio/$dataInicial/fim/$dataFinal?tokenPortal=$token&crm=${credentials?.crm}';
      final response = await httpClient.get(
        Uri.parse(url),
        headers: {
          "Content-Type": "application/json",
          "Authorization": "Bearer $tokenPerfilApps",
        },
      );

      if (response.statusCode == 200) {
        logger.d(
            "getExtractCopart success ${StringUtils.limitString(response.body, 200)}");

        final vo = ExtractCopartVO.fromJson(
            jsonDecode(utf8.decode(response.bodyBytes))['retorno']);

        const formatDate = 'd/M/y';
        vo.extrato!.sort((a, b) => -DateFormat(formatDate)
            .parse(a.data!)
            .compareTo(DateFormat(formatDate).parse(b.data!)));

        return vo;
      } else {
        final message = jsonDecode(response.body)['message'];

        logger.e(
            'getExtractCopart statusCode : ${response.statusCode} ${response.body}');
        throw ServicesException('$message}');
      }
    } on UnimedException catch (ex) {
      logger.e('getExtractCopart ${ex.runtimeType} : $ex');
      throw UnimedException(ex.message);
    } catch (ex) {
      logger.e('getExtractCopart  exception : $ex');
      throw ServicesException(
          "Não foi possível atender sua solicitação no momento. Tente novamente mais tarde.");
    }
  }

  String getReportUrl(String? reportCode) {
    logger.i("getReportUrl redirect with -> $reportCode");
    return '${FlavorConfig.instance!.values.profilePermissions.url}app/cooperado/relatorio-reports/id/$reportCode';
  }

  Future<Response> getReportHonorarioVisible(
      {required List<Especialidades> specialities}) async {
    specialities.sort((a, b) {
      if (a.especialidadePrincipal == "S" && b.especialidadePrincipal != "S") {
        return -1;
      } else if (a.especialidadePrincipal != "S" &&
          b.especialidadePrincipal == "S") {
        return 1;
      } else {
        return 0;
      }
    });

    UserCredentials? credentials =
        await Locator.instance!.get<AuthApi>().getCredentials();

    logger.i(
        "getReportHonorarioVisible redirect with -> ${credentials?.crm}, $specialities");
    List<String> codEspecialities = [];
    for (var element in specialities) {
      codEspecialities.add(element.codigo.toString());
    }
    String finalSpecialits = codEspecialities.join('-');
    final tokenPerfilApps =
        await Locator.instance!.get<AuthApi>().tokenPerfilApps();
    var resultURL =
        '${FlavorConfig.instance!.values.profilePermissions.url}app/cooperado/fee-monitoring/visible/${credentials?.crm}/crm?specialities=$finalSpecialits';

    final response = await httpClient.get(
      Uri.parse(resultURL),
      headers: {
        "Content-Type": "application/json",
        "Authorization": "Bearer $tokenPerfilApps",
      },
    );
    logger.d(
        "getReportHonorarioVisible -> crm:  ${credentials?.crm} | specialities:  $finalSpecialits");
    logger.d(
        "getReportHonorarioVisible -> status code: ${response.statusCode} body: ${response.body}");
    return response;
  }

  Future<String?> getReportHonorariosCode(
      {DateTime? date, required String crmId}) async {
    UserCredentials? credentials =
        await Locator.instance!.get<AuthApi>().getCredentials();

    Response response;
    try {
      final tokenPerfilApps =
          await Locator.instance!.get<AuthApi>().tokenPerfilApps();

      final url =
          '${FlavorConfig.instance!.values.profilePermissions.url}app/cooperado/fee-monitoring/pdf/${credentials?.crm}/crm/$crmId/id?year=${date?.year}&month=${date?.month}';
      logger.d(
          "getReportHonorarioVisible -> crm:  ${credentials?.crm} | id: $crmId | year: ${date?.year} | mount:${date?.month} ");

      response = await httpClient.get(
        Uri.parse(url),
        headers: {
          "Authorization": "Bearer $tokenPerfilApps",
        },
      );
      logger.d(
          "getReportHonorarioVisible -> status code: ${response.statusCode}");
      if (response.statusCode == 200) {
        final data = jsonDecode(utf8.decode(response.bodyBytes));
        logger.d(
            "getReportHonorarioCode ${StringUtils.limitString(response.body, 200)}");
        var result = data['base64'];
        return result;
      } else if (response.statusCode == 500) {
        final data = jsonDecode(utf8.decode(response.bodyBytes));
        final message = data['messagem'] ?? MessageException.general;

        logger.e(
            'getReportHonorarioCode statusCode : ${response.statusCode} ${response.body}');
        throw ServicesException(message);
      } else {
        logger.e(
            'getReportHonorarioCode statusCode : ${response.statusCode} ${response.body}');
        throw ServicesException(
            "Não foi possível atender sua solicitação no momento. Tente novamente mais tarde.");
      }
    } on ServicesException catch (e) {
      logger.e('getReportHonorarioCode  service exception : $e');
      throw ServicesException(e.message);
    } on UnimedException catch (ex) {
      logger.e('${ex.runtimeType} - ${ex.message}');
      throw ServicesException(ex.message);
    } catch (ex) {
      logger.e('getReportHonorarioCode  exception : $ex');
      throw ServicesException(
          "Não foi possível atender sua solicitação no momento. Tente novamente mais tarde.");
    }
  }

  /// Fetches a Base64 encoded report of production.
  ///
  /// This method retrieves a report of production in Base64 format.
  ///
  /// Returns a [Future] that completes with a [String] containing the Base64 encoded report.
  Future<String> getBase64ReportProduction({
    required DateTime lastProduction,
  }) async {
    logger.i(
        "getReportProductionCode - prestador solicitou relatorio prod medica");
    UserCredentials? credentials =
        await Locator.instance!.get<AuthApi>().getCredentials();

    Response response;
    try {
      final tokenPerfilApps =
          await Locator.instance!.get<AuthApi>().tokenPerfilApps();

      final lastProductionDate =
          DateFormat("dd-MM-yyyy").format(lastProduction);
      final url =
          '${FlavorConfig.instance!.values.profilePermissions.url}app/cooperado/relatorio-producao/data/$lastProductionDate?&crm=${credentials?.crm}';
      response = await httpClient.get(
        Uri.parse(url),
        headers: {
          "Content-Type": "application/json; charset=UTF-8",
          "Authorization": "Bearer $tokenPerfilApps",
        },
      );
      if (response.statusCode == 200) {
        final data = jsonDecode(utf8.decode(response.bodyBytes));

        logger.d(
            "getReportProductionCode ${StringUtils.limitString(response.body, 200)}");

        return data['retorno'];
      } else {
        final message = jsonDecode(utf8.decode(response.bodyBytes))['message'];

        logger.e(
            'getReportProductionCode statusCode : ${response.statusCode} ${response.body}');
        throw ServicesException(message);
      }
    } on ServicesException catch (e) {
      logger.e('getReportProductionCode  service exception : $e');
      throw ServicesException(e.message);
    } on UnimedException catch (ex) {
      logger.e('${ex.runtimeType} - ${ex.message}');
      throw ServicesException(ex.message);
    } catch (ex) {
      logger.e('getReportProductionCode  exception : $ex');
      throw ServicesException(
          "Não foi possível atender sua solicitação no momento. Tente novamente mais tarde.");
    }
  }

  Future<List<HonorarySolicitation>> getLastParticipationRequests() async {
    UserCredentials? credentials =
        await Locator.instance!.get<AuthApi>().getCredentials();
    final token = credentials != null
        ? await User.createToken(credentials: credentials)
        : "";

    Response response;
    try {
      final tokenPerfilApps =
          await Locator.instance!.get<AuthApi>().tokenPerfilApps();
      final url =
          '${FlavorConfig.instance!.values.profilePermissions.url}app/cooperado/solicitacao-participacao?tokenPortal=$token&crm=${credentials?.crm}';
      response = await httpClient.get(
        Uri.parse(url),
        headers: {
          "Content-Type": "application/json",
          "Authorization": "Bearer $tokenPerfilApps",
        },
      );
      if (response.statusCode == 200) {
        final data = jsonDecode(utf8.decode(response.bodyBytes));

        final collection = (data["retorno"]["listaSolicitacoesHonorario"]
                as List)
            .map<HonorarySolicitation>((e) => HonorarySolicitation.fromJson(e))
            .toList();

        logger.d(
            "getLastParticipationRequests success list: ${collection.length}");
        const formatDate = 'd/M/y';
        collection.sort((a, b) => -DateFormat(formatDate)
            .parse(a.dataSolicitacao!)
            .compareTo(DateFormat(formatDate).parse(b.dataSolicitacao!)));

        return collection;
      } else {
        final message = jsonDecode(utf8.decode(response.bodyBytes))['message'];

        logger.e(
            'getLastParticipationRequests statusCode : ${response.statusCode} ${response.body}');
        throw ServicesException(message);
      }
    } on ServicesException catch (e) {
      throw ServicesException(e.message);
    } on UnimedException catch (ex) {
      logger.e('${ex.runtimeType} - ${ex.message}');
      throw ServicesException(ex.message);
    } catch (ex) {
      logger.e('getLastParticipationRequests  exception : $ex');
      throw ServicesException(
          "Não foi possível atender sua solicitação no momento. Tente novamente mais tarde.");
    }
  }

  Future<ProcedureList> getMedicProcedures(
      {required String keySearch, int? pagina}) async {
    UserCredentials? credentials =
        await Locator.instance!.get<AuthApi>().getCredentials();
    final token = credentials != null
        ? await User.createToken(credentials: credentials)
        : "";

    // ignore: unused_local_variable
    String codigo = "";
    String descricao = "";

    //Verifica se a chave de busca e um numero
    double.tryParse(keySearch) != null
        ? codigo = keySearch
        : descricao = keySearch;

    try {
      final tokenPerfilApps =
          await Locator.instance!.get<AuthApi>().tokenPerfilApps();
      final url =
          '${FlavorConfig.instance!.values.profilePermissions.url}app/cooperado/consultar-procedimento?tokenPortal=$token&descricao=$descricao&pagina=$pagina&crm=${credentials?.crm}';
      final response = await httpClient.get(
        Uri.parse(url),
        headers: {
          "Content-Type": "application/json",
          "Authorization": "Bearer $tokenPerfilApps",
        },
      );

      if (response.statusCode == 200) {
        final data = ProcedureList.fromJson(
            jsonDecode(utf8.decode(response.bodyBytes))['retorno']);

        logger.d(
            "getMedicProcedures success procedimento ${data.procedimento!.tipo!.descricao} | list procedimento ${data.listaProcedimentos!.length}");

        return data;
      } else {
        final message = jsonDecode(response.body)['message'];

        logger.e(
            'getMedicProcedures statusCode : ${response.statusCode} - ${response.body}');
        throw ServicesException('$message');
      }
    } on UnimedException catch (ex) {
      logger.e('getMedicProcedures ${ex.runtimeType} : ${ex.message}');
      throw ServicesException(ex.message);
    } catch (ex) {
      logger.e('getMedicProcedures exception : $ex');
      throw ServicesException(
          "Não foi possível atender sua solicitação no momento. Tente novamente mais tarde.");
    }
  }

  Future<OpmeList> getOpmeList({required String keySearch, int? pagina}) async {
    UserCredentials? credentials =
        await Locator.instance!.get<AuthApi>().getCredentials();
    final token = credentials != null
        ? await User.createToken(credentials: credentials)
        : "";

    // ignore: unused_local_variable
    String codigo = "";
    String descricao = "";

    //Verifica se a chave de busca e um numero
    double.tryParse(keySearch) != null
        ? codigo = keySearch
        : descricao = keySearch;

    try {
      final tokenPerfilApps =
          await Locator.instance!.get<AuthApi>().tokenPerfilApps();
      final url =
          '${FlavorConfig.instance!.values.profilePermissions.url}app/cooperado/consultar-opme?tokenPortal=$token&descricao=$descricao&pagina=$pagina&crm=${credentials?.crm}';
      final response = await httpClient.get(
        Uri.parse(url),
        headers: {
          "Content-Type": "application/json",
          "Authorization": "Bearer $tokenPerfilApps",
        },
      );

      if (response.statusCode == 200) {
        final data = OpmeList.fromJson(
            jsonDecode(utf8.decode(response.bodyBytes))['retorno']);

        logger.d(
            "getOpmeList success procedimento ${data.opme!.descricao} | list opme ${data.listaOPME!.length}");
        return data;
      } else {
        final message = jsonDecode(response.body)['message'];

        logger.e(
            'getOpmeList statusCode : ${response.statusCode} ${response.body}');
        throw ServicesException('$message');
      }
    } on UnimedException catch (ex) {
      logger.e('getOpmeList  ${ex.runtimeType} : $ex');
      throw ServicesException(ex.message);
    } catch (ex) {
      logger.e('getOpmeList  exception : $ex');
      throw ServicesException(
          "Não foi possível atender sua solicitação no momento. Tente novamente mais tarde.");
    }
  }

  Future<CidList> getCidList({required String keySearch, int? pagina}) async {
    UserCredentials? credentials =
        await Locator.instance!.get<AuthApi>().getCredentials();
    final token = credentials != null
        ? await User.createToken(credentials: credentials)
        : "";

    // ignore: unused_local_variable
    String codigo = "";
    String descricao = "";

    //Verifica se a chave de busca e um numero
    double.tryParse(keySearch) != null
        ? codigo = keySearch
        : descricao = keySearch;

    try {
      final tokenPerfilApps =
          await Locator.instance!.get<AuthApi>().tokenPerfilApps();
      final url =
          '${FlavorConfig.instance!.values.profilePermissions.url}app/cooperado/consultar-cid?tokenPortal=$token&descricao=$descricao&pagina=$pagina&crm=${credentials?.crm}';
      final response = await httpClient.get(
        Uri.parse(url),
        headers: {
          "Content-Type": "application/json",
          "Authorization": "Bearer $tokenPerfilApps",
        },
      );

      if (response.statusCode == 200) {
        final data = CidList.fromJson(
            jsonDecode(utf8.decode(response.bodyBytes))['retorno']);
        logger.d(
            "getCidList success procedimento ${data.cid!.descricao} | list opme ${data.listaCid!.length}");

        return data;
      } else {
        final message = jsonDecode(response.body)['message'];
        logger.e(
            'getCidList statusCode : ${response.statusCode} ${response.body}');
        throw ServicesException('$message');
      }
    } on UnimedException catch (ex) {
      logger.e('getCidList  ${ex.runtimeType} : $ex');
      throw ServicesException(ex.message);
    } catch (ex) {
      logger.e('getCidList  exception : $ex');
      throw ServicesException(
          "Não foi possível atender sua solicitação no momento. Tente novamente mais tarde.");
    }
  }

  Future<String?> getIRDeclaration() async {
    UserCredentials? credentials =
        await Locator.instance!.get<AuthApi>().getCredentials();
    final token = credentials != null
        ? await User.createToken(credentials: credentials)
        : "";
    try {
      final tokenPerfilApps =
          await Locator.instance!.get<AuthApi>().tokenPerfilApps();
      final url =
          '${FlavorConfig.instance!.values.profilePermissions.url}app/cooperado/imposto-renda?tokenPortal=$token&crm=${credentials?.crm}';
      final response = await httpClient.get(
        Uri.parse(url),
        headers: {
          "Content-Type": "application/json",
          "Authorization": "Bearer $tokenPerfilApps",
        },
      );
      final data = jsonDecode(utf8.decode(response.bodyBytes));

      if (response.statusCode == 200) {
        logger.d('getIRDeclaration success : ${response.body}');
        return data['retorno'];
      } else {
        final message = data['message'];
        logger.e(
            'getIRDeclaration statusCode : ${response.statusCode} ${response.body}');
        throw ServicesException('$message');
      }
    } on ServicesException catch (e) {
      throw ServicesException(e.message);
    } on UnimedException catch (ex) {
      logger.e('${ex.runtimeType} - ${ex.message}');
      throw ServicesException(ex.message);
    } catch (ex) {
      logger.e('getIRDeclaration exception : $ex');
      throw ServicesException(
          "Não foi possível atender sua solicitação no momento. Tente novamente mais tarde.");
    }
  }

  Future<bool> getListMedicineVisible(
      {required List<Especialidades> specialities}) async {
    specialities.sort((a, b) {
      if (a.especialidadePrincipal == "S" && b.especialidadePrincipal != "S") {
        return -1;
      } else if (a.especialidadePrincipal != "S" &&
          b.especialidadePrincipal == "S") {
        return 1;
      } else {
        return 0;
      }
    });

    UserCredentials? credentials =
        await Locator.instance!.get<AuthApi>().getCredentials();

    List<String> codEspecialities = [];
    for (var element in specialities) {
      codEspecialities.add(element.codigo.toString());
    }
    String finalSpecialits = codEspecialities.join('-');

    logger.d(
        "getListMedicineVisible -> crm:  ${credentials?.crm} | specialities:  $finalSpecialits");

    final tokenPerfilApps =
        await Locator.instance!.get<AuthApi>().tokenPerfilApps();

    final url =
        '${FlavorConfig.instance!.values.profilePermissions.url}app/cooperado/permission-especiality/list-medicines/${credentials?.crm}/crm?specialities=$finalSpecialits';

    final response = await httpClient.get(
      Uri.parse(url),
      headers: {
        "Content-Type": "application/json",
        "Authorization": "Bearer $tokenPerfilApps",
      },
    );

    logger.d(
        "getListMedicineVisible -> status code: ${response.statusCode} body: ${response.body}");
    if (response.statusCode == 200) {
      return true;
    } else {
      return false;
    }
  }

  Future<List<MedicineModel>> getAllMedicines() async {
    try {
      final tokenPerfilApps =
          await Locator.instance!.get<AuthApi>().tokenPerfilApps();
      final url =
          '${FlavorConfig.instance!.values.profilePermissions.url}mv/medicines-price';

      final response = await httpClient.get(
        Uri.parse(url),
        headers: {
          "Content-Type": "application/json",
          "Authorization": "Bearer $tokenPerfilApps",
        },
      );
      if (response.statusCode == 200) {
        final data = jsonDecode(utf8.decode(response.bodyBytes));

        final collection = (data as List)
            .map<MedicineModel>((e) => MedicineModel.fromJson(e))
            .toList();

        logger.d("getAllMedicines success list: ${collection.length}");

        collection.sort((a, b) =>
            (a.valorUltimaEntrada ?? 0).compareTo(b.valorUltimaEntrada ?? 0));

        return collection;
      } else {
        final message = jsonDecode(utf8.decode(response.bodyBytes))['message'];

        logger.e(
            'getAllMedicines statusCode : ${response.statusCode} ${response.body}');
        throw ServicesException(message);
      }
    } on ServicesException catch (e) {
      logger.e('getAllMedicines  ServicesException : $e');
      throw ServicesException(e.message);
    } on UnimedException catch (ex) {
      logger.e(
          'getAllMedicines UnimedException - ${ex.runtimeType} - ${ex.message}');
      throw ServicesException(ex.message);
    } catch (ex) {
      logger.e('getAllMedicines  exception : $ex');
      throw ServicesException(
          "Não foi possível atender sua solicitação no momento. Tente novamente mais tarde.");
    }
  }

  Future<List<MedicineModel>> getAllMedicinesOffline() async {
    try {
      final box = Locator.instance!<Store>().box<MedicineModel>();
      final medicines = await box.getAllAsync();

      medicines.sort((a, b) =>
          (a.valorUltimaEntrada ?? 0).compareTo(b.valorUltimaEntrada ?? 0));
      logger.d("getAllMedicinesOffline success list: ${medicines.length}");

      return medicines;
    } catch (ex) {
      logger.e('getAllMedicinesOffline  exception : $ex');
      throw ServicesException(
          "Não foi possível atender sua solicitação no momento. Tente novamente mais tarde.");
    }
  }

  Future<void> saveAllMedicinesInBox(
      {required List<MedicineModel> medicines}) async {
    try {
      final box = Locator.instance!<Store>().box<MedicineModel>();

      await box.removeAllAsync();

      for (var medicine in medicines) {
        box.putQueued(medicine);
      }

      logger.d("saveAllMedicinesInBox success list: ${medicines.length}");
    } catch (ex) {
      logger.e('saveAllMedicinesInBox  exception : $ex');
      throw ServicesException(
          "Não foi possível atender sua solicitação no momento. Tente novamente mais tarde.");
    }
  }

  Future<List<GlosaResourceData>> getGlosaResource(
      {required String crm, required String guide}) async {
    try {
      final tokenPerfilApps =
          await Locator.instance!.get<AuthApi>().tokenPerfilApps();
      final url =
          '${FlavorConfig.instance!.values.profilePermissions.url}app/cooperado/solicitacao-participacao/$guide/guide/$crm/crm';

      final response = await httpClient.get(
        Uri.parse(url),
        headers: {
          "Content-Type": "application/json",
          "Authorization": "Bearer $tokenPerfilApps",
        },
      );
      if (response.statusCode == 200) {
        final data = jsonDecode(utf8.decode(response.bodyBytes));

        final collection = (data as List)
            .map<GlosaResourceData>((e) => GlosaResourceData.fromJson(e))
            .toList();

        logger.d("getGlosaResource success list: ${collection.length}");

        return collection;
      } else {
        final message = jsonDecode(utf8.decode(response.bodyBytes))['message'];

        logger.e(
            'getGlosaResource statusCode : ${response.statusCode} ${response.body}');
        throw ServicesException(message);
      }
    } on ServicesException catch (e) {
      logger.e('getGlosaResource  ServicesException : $e');
      throw ServicesException(e.message);
    } on UnimedException catch (ex) {
      logger.e(
          'getGlosaResource UnimedException - ${ex.runtimeType} - ${ex.message}');
      throw ServicesException(ex.message);
    } catch (ex) {
      logger.e('getGlosaResource  exception : $ex');
      throw ServicesException(
          "Não foi possível atender sua solicitação no momento. Tente novamente mais tarde.");
    }
  }

  Future<GlosaResourceData> addGlosaResource(
      {required String crm,
      required String guide,
      required String codService,
      required String justification,
      required String numSohoev}) async {
    try {
      final tokenPerfilApps =
          await Locator.instance!.get<AuthApi>().tokenPerfilApps();
      final url =
          '${FlavorConfig.instance!.values.profilePermissions.url}app/cooperado/solicitacao-participacao/resource';

      Map<String, Object> body = {
        "crm": crm,
        "guide": guide,
        "codService": codService,
        "justification": justification,
        "numSohoev": numSohoev,
      };

      final response = await httpClient.post(
        Uri.parse(url),
        body: jsonEncode(body),
        headers: {
          "Content-Type": "application/json",
          "Authorization": "Bearer $tokenPerfilApps",
        },
      );
      if (response.statusCode == 201) {
        final data = jsonDecode(utf8.decode(response.bodyBytes));

        logger.d("addGlosaResource success data: $data");

        return GlosaResourceData.fromJson(data);
      } else {
        final data = jsonDecode(utf8.decode(response.bodyBytes));

        final message =
            data['message'] ?? data['mensagem'] ?? MessageException.general;

        logger.e(
            'addGlosaResource statusCode : ${response.statusCode} ${response.body}');
        throw ServicesException(message);
      }
    } on ServicesException catch (e) {
      logger.e('addGlosaResource  ServicesException : $e');
      throw ServicesException(e.message);
    } on UnimedException catch (ex) {
      logger.e(
          'addGlosaResource UnimedException - ${ex.runtimeType} - ${ex.message}');
      throw ServicesException(ex.message);
    } catch (ex) {
      logger.e('addGlosaResource  exception : $ex');
      throw ServicesException(
          "Não foi possível atender sua solicitação no momento. Tente novamente mais tarde.");
    }
  }

  Future<List<FileAttach>> getGlosaFiles({
    required String sequencial,
  }) async {
    try {
      final tokenPerfilApps =
          await Locator.instance!.get<AuthApi>().tokenPerfilApps();
      final url =
          '${FlavorConfig.instance!.values.profilePermissions.url}app/cooperado/solicitacao-participacao/files/$sequencial/sequencial';

      final response = await httpClient.get(
        Uri.parse(url),
        headers: {
          "Content-Type": "application/json",
          "Authorization": "Bearer $tokenPerfilApps",
        },
      );
      if (response.statusCode == 200) {
        final data = jsonDecode(utf8.decode(response.bodyBytes));

        final collection = (data as List)
            .map<GlosaFileModel>((e) => GlosaFileModel.fromJson(e))
            .toList();

        logger.d("getGlosaFiles success data size: ${collection.length}");

        List<FileAttach> files = [];

        for (GlosaFileModel element in collection) {
          String dir = (await getApplicationDocumentsDirectory()).path;
          File file = File("$dir/${element.anexo.nomeArquivo}");
          await file.writeAsBytes(base64.decode(element.base64));

          File? compressedFile;
          if (!element.anexo.nomeArquivo.toLowerCase().contains(".pdf")) {
            compressedFile = await FileUtils.getCompressedFile(file);
          }

          files.add(FileAttach(
              name: element.anexo.nomeArquivo,
              file: file,
              thumbnail: compressedFile,
              sended: true));
        }

        return files;
      } else {
        final data = jsonDecode(utf8.decode(response.bodyBytes));

        final message =
            data['message'] ?? data['mensagem'] ?? MessageException.general;

        logger.e(
            'getGlosaFiles statusCode : ${response.statusCode} ${response.body}');
        throw ServicesException(message);
      }
    } on ServicesException catch (e) {
      logger.e('getGlosaFiles  ServicesException : $e');
      throw ServicesException(e.message);
    } on UnimedException catch (ex) {
      logger.e(
          'getGlosaFiles UnimedException - ${ex.runtimeType} - ${ex.message}');
      throw ServicesException(ex.message);
    } catch (ex) {
      logger.e('getGlosaFiles  exception : $ex');
      throw ServicesException(
          "Não foi possível atender sua solicitação no momento. Tente novamente mais tarde.");
    }
  }

  Future<String> addGlosaFile({
    required String sequencial,
    required String guide,
    required FileAttach file,
  }) async {
    try {
      final tokenPerfilApps =
          await Locator.instance!.get<AuthApi>().tokenPerfilApps();
      final url =
          '${FlavorConfig.instance!.values.profilePermissions.url}app/cooperado/solicitacao-participacao/file';

      Map<String, Object> body = {
        "sequencial": sequencial,
        "guide": guide,
        "fileName": file.name,
        "fileBase64": base64Encode(file.file.readAsBytesSync()),
      };

      final response = await httpClient.post(
        Uri.parse(url),
        body: jsonEncode(body),
        headers: {
          "Content-Type": "application/json",
          "Authorization": "Bearer $tokenPerfilApps",
        },
        logBody: false,
      );
      if (response.statusCode == 201) {
        final data = jsonDecode(utf8.decode(response.bodyBytes));

        logger.d("addGlosaFile success data: $data");

        return data['mensagem'] ??
            data['message'] ??
            "Arquivo enviado com sucesso";
      } else {
        final data = jsonDecode(utf8.decode(response.bodyBytes));

        final message =
            data['message'] ?? data['mensagem'] ?? MessageException.general;

        logger.e(
            'addGlosaFile statusCode : ${response.statusCode} ${response.body}');
        throw ServicesException(message);
      }
    } on ServicesException catch (e) {
      logger.e('addGlosaFile  ServicesException : $e');
      throw ServicesException(e.message);
    } on UnimedException catch (ex) {
      logger.e(
          'addGlosaFile UnimedException - ${ex.runtimeType} - ${ex.message}');
      throw ServicesException(ex.message);
    } catch (ex) {
      logger.e('addGlosaFile  exception : $ex');
      throw ServicesException(
          "Não foi possível atender sua solicitação no momento. Tente novamente mais tarde.");
    }
  }
}
