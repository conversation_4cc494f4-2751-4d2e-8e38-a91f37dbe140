import 'package:cooperado_minha_unimed/colors.dart';
import 'package:cooperado_minha_unimed/shared/screen-transitions/fade.transition.dart';
import 'package:cooperado_minha_unimed/shared/utils/logger_print.dart';
import 'package:cooperado_minha_unimed/shared/vo/transparencia/demostrativo_resultado.dart';
import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../../shared/widgets/pdf_view/pdf_view_platform.dart';

class CardReport extends StatefulWidget {
  final Noticia? noticeModel;
  const CardReport({super.key, this.noticeModel});
  @override
  CardReportState createState() => CardReportState();
}

class CardReportState extends State<CardReport> {
  final logger = UnimedLogger(className: 'CardReport');
  @override
  Widget build(BuildContext context) {
    return ListTile(
      onTap: () {
        _openFile(widget.noticeModel!.arquivo);
      },
      leading: Container(
        padding: const EdgeInsets.all(8.0),
        child: const Icon(
          Icons.find_in_page,
          color: CooperadoColors.tealGreen,
        ),
      ),
      title: Text(
        widget.noticeModel!.assunto!,
        style: const TextStyle(color: CooperadoColors.grayDark),
      ),
      subtitle: Text(
        widget.noticeModel!.dataFormatted,
        style: const TextStyle(fontSize: 12),
      ),
    );
  }

  _openFile(String? url) async {
    try {
      final assunto = widget.noticeModel!.assunto!.replaceAll(' ', '_');
      final names = widget.noticeModel!.arquivo!.split('.');
      final ext = names[names.length - 1];

      if (ext == 'pdf') {
        Navigator.push(
          context,
          FadeRoute(
            page: PDFViewPlatform(
              url,
              share: true,
              filename: assunto,
              title: widget.noticeModel!.assunto,
            ),
          ),
        );
      } else {
        _launchURL(url);
      }
    } catch (e) {
      logger.e('openFile catch Exception: ${e.toString()}');
      throw 'Unable to open  file : $url';
    }
  }

  Future _launchURL(url) async {
    logger.i('launchURL - abrindo pdf em url');
    if (await canLaunchUrl(url)) {
      await launchUrl(url);
    } else {
      logger.e('launchURL - erro ao abrir url');
      throw 'Unable to open url : $url';
    }
  }
}
