import 'package:intl/intl.dart';

class GraphUtils {
  static double findMax(List<double> numbers) {
    if (numbers.isEmpty) {
      return 0;
    }
    double max = numbers.first;
    for (int i = 1; i < numbers.length; i++) {
      if (numbers[i] > max) {
        max = numbers[i];
      }
    }
    return max;
  }

  static double nextThousand(double number) {
    double nextThousand = ((number / 1000).ceil()) * 1000;
    return nextThousand;
  }

  static List<double> makeHorizontalLines(double valor, {int? factor}) {
    double dividend;
    if (factor != null) {
      dividend = valor / factor;
    } else {
      dividend = valor / 5;
    }

    List<double> lista = [];

    for (double i = 0; i <= valor; i += dividend) {
      if (dividend < 1) {
        lista.add(i);
      } else {
        i = converteToDouble(i);
        lista.add(i);
      }
    }

    return lista;
  }

  static int convertToInterger(double numero) {
    String numeroString = numero.round().toString();
    String parteInteira = numeroString.split('.')[0];
    int resultado = int.parse(parteInteira);

    return resultado;
  }

  static double converteToDouble(double numero) {
    String stringNumber = numero.toString();

    String inPart = stringNumber.split('.')[0];

    double result = double.parse(inPart);

    return result;
  }

  static String priceToCurrency(double price) {
    price = convertToDouble(price.toStringAsFixed(0));
    NumberFormat numberFormat = NumberFormat.currency(symbol: '', decimalDigits: 0);

    return numberFormat.format(price);
  }

  static double roundBasedOnFirstDigit(double numero) {
    double firtDigit = double.parse(numero.toStringAsFixed(1));

    if (numero - firtDigit >= 0.5) {
      return firtDigit + 1.0;
    } else {
      return firtDigit;
    }
  }

  static double convertToDouble(String value) {
    value = removeAllWhitespace(value);
    value = value.trim();
    value = value.replaceFirst(RegExp(r','), '.');
    if (value.contains(",")) {
      value = value.replaceAll(RegExp(r','), '');
    }
    List splitValue = [];

    splitValue = value.split('.');
    value = splitValue[0];
    if (splitValue.length > 1) {
      if (splitValue.length == 2) {
        value += ".";
        value += splitValue[1];
      } else if (splitValue.length >= 3) {
        value += ".";
        value += splitValue[1];
        value += splitValue[2];
      }
    }
    double valor = double.parse(value);
    return valor;
  }

  static String removeAllWhitespace(String value) {
    return value.replaceAll(' ', '');
  }
}
