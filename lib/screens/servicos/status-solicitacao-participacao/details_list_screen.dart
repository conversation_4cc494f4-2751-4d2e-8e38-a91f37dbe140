import 'package:cooperado_minha_unimed/bloc/auth/auth_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/indicators/last_production/last_production_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/sensitive-data/sensitive_data_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/sensitive-data/sensitive_data_state.dart';
import 'package:cooperado_minha_unimed/bloc/servicos/status-solicitacao-participacao/glose_resource/glosa_resource_bloc.dart';
import 'package:cooperado_minha_unimed/bloc/servicos/status-solicitacao-participacao/participation_solicitation_status_bloc.dart';
import 'package:cooperado_minha_unimed/bloc/servicos/status-solicitacao-participacao/participation_solicitation_status_state.dart';
import 'package:cooperado_minha_unimed/colors.dart';
import 'package:cooperado_minha_unimed/shared/screen-transitions/fade.transition.dart';
import 'package:cooperado_minha_unimed/shared/vo/servicos/honorary_solicitation.vo.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';

import 'details_solicitation_screen.dart';
import 'filter_solicitation.dart';

class DetailsListSocitationsScreen extends StatefulWidget {
  final List<HonorarySolicitation> list;

  const DetailsListSocitationsScreen({super.key, required this.list});

  @override
  DetailsListSocitationsScreenState createState() =>
      DetailsListSocitationsScreenState();
}

class DetailsListSocitationsScreenState
    extends State<DetailsListSocitationsScreen> {
  bool showExtract = true;
  DateTime? selectedDateTime;
  bool filtersPanelOpened = false;
  FocusNode? focusTextFieldSearch;

  @override
  void initState() {
    selectedDateTime = context.read<LastProductionCubit>().lastDate;

    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: AppBar(
          title: const Text("Participação"),
          actions: <Widget>[
            Padding(
              padding: const EdgeInsets.only(right: 5.0),
              child: IconButton(
                icon: BlocBuilder<SensitiveDataCubit, SensitiveDataState>(
                  builder: (context, state) {
                    return Icon(
                      state.isSensitiveDataVisible
                          ? Icons.visibility
                          : Icons.visibility_off,
                    );
                  },
                ),
                onPressed: () {
                  context
                      .read<SensitiveDataCubit>()
                      .toggleSensitiveDataVisibility();
                },
              ),
            ),
            IconButton(
              icon: const Icon(Icons.search),
              onPressed: () {
                setState(() {
                  filtersPanelOpened = !filtersPanelOpened;
                });
              },
            )
          ],
          backgroundColor: CooperadoColors.tealGreenDark,
        ),
        body: RefreshIndicator(
          onRefresh: () async {
            context
                .read<SolicitationParticipationStatusCubit>()
                .getSolicitationParticipationStatusEvent();
          },
          color: CooperadoColors.tealGreen,
          child: SafeArea(
            child: SingleChildScrollView(
                child: Padding(
                    padding: const EdgeInsets.only(top: 20),
                    child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: <Widget>[
                          _openFilter(),
                          const SizedBox(height: 20),
                          BlocBuilder<SolicitationParticipationStatusCubit,
                                  SolicitationParticipationStatusState>(
                              builder: (context, state) {
                            if (state
                                is DoneGetSolicitationParticipationStatusState) {
                              return _listSolicitations(
                                  state.list as List<HonorarySolicitation>);
                            } else if (state
                                is LoadingGetSolicitationParticipationStatusState) {
                              return const SpinKitCircle(
                                color: CooperadoColors.tealGreen,
                              );
                            }
                            if (state
                                is ErrorGetSolicitationParticipationStatusState) {
                              return Text(state.message);
                            }

                            return Container();
                          })
                        ]))),
          ),
        ));
  }

  Widget _modelShowInfo(HonorarySolicitation solicitation) {
    return BlocBuilder<SensitiveDataCubit, SensitiveDataState>(
      builder: (context, sensitiveState) {
        bool isSensitiveDataVisible = sensitiveState.isSensitiveDataVisible;
        String displayTicket = isSensitiveDataVisible
            ? solicitation.numeroSolicitacao.toString()
            : ('*' * solicitation.numeroSolicitacao.toString().length);
        String displayStatus = isSensitiveDataVisible
            ? solicitation.situacao!
            : '*' * solicitation.situacao!.length;

        return InkWell(
          onTap: () {
            debugPrint("TEste: ${solicitation.toJson()}");
            if (solicitation.isGlosa) {
              context.read<GlosaResourceCubit>().getGlosaResourceEvent(
                  crm: context.read<AuthCubit>().credentials.crm,
                  guide: solicitation.guiaReferencia?.toString() ?? '');
            }

            Navigator.push(
                context,
                FadeRoute(
                    page: DetailsSocitationScreen(solicitation: solicitation)));
          },
          child: Container(
            decoration: const BoxDecoration(
              border: Border(
                left:
                    BorderSide(width: 1.0, color: CooperadoColors.greenLight3),
              ),
            ),
            child: Row(
              children: <Widget>[
                Expanded(
                  child: Padding(
                    padding: const EdgeInsets.only(left: 10),
                    child: Container(
                      decoration: const BoxDecoration(
                        border: Border(
                          left: BorderSide(
                              width: 1.0, color: CooperadoColors.greenLight3),
                        ),
                      ),
                      child: Padding(
                        padding: const EdgeInsets.only(left: 10),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: <Widget>[
                            const SizedBox(height: 5),
                            Text(
                              "${solicitation.dataSolicitacao!} - ${solicitation.participacao!.descricao!}",
                              style:
                                  const TextStyle(fontWeight: FontWeight.bold),
                            ),
                            Text("Ticket: $displayTicket"),
                            Text("Status: $displayStatus"),
                            const SizedBox(height: 5),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
                const Padding(
                  padding: EdgeInsets.only(right: 10),
                  child: Icon(
                    Icons.call_made,
                    size: 25,
                    color: CooperadoColors.grayDark,
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _listSolicitations(List<HonorarySolicitation> list) {
    List<Widget> widgets = [];

    for (HonorarySolicitation e in list) {
      widgets.add(_modelShowInfo(e));
      widgets.add(const SizedBox(
        height: 20,
      ));
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: widgets,
    );
  }

  Widget _openFilter() {
    // if (widget.filtersPanelOpened) {
    //   FocusScope.of(context).requestFocus(focusTextFieldSearch);
    // } else {
    //   FocusScope.of(context).requestFocus(new FocusNode());
    // }
    return AnimatedContainer(
      height: filtersPanelOpened ? 70 : 0,
      curve: Curves.bounceInOut,
      duration: const Duration(milliseconds: 300),
      child: FiltersPanel(
        focusNode: focusTextFieldSearch,
      ),
    );
  }
}
