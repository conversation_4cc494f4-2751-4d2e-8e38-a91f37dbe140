// ignore_for_file: use_build_context_synchronously

import 'package:cooperado_minha_unimed/bloc/auth/auth_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/profile/profile_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/sensitive-data/sensitive_data_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/sensitive-data/sensitive_data_state.dart';
import 'package:cooperado_minha_unimed/colors.dart';
import 'package:cooperado_minha_unimed/models/general_config_model.dart';
import 'package:cooperado_minha_unimed/screens/config-profile/config_screen.dart';
import 'package:cooperado_minha_unimed/screens/login.dart';
import 'package:cooperado_minha_unimed/screens/main/channel_ethics.dart';
import 'package:cooperado_minha_unimed/screens/main/privacy_policy.dart';
import 'package:cooperado_minha_unimed/screens/main/silver_app_bar_replace.dart';
import 'package:cooperado_minha_unimed/screens/notificacao/notifications_count.dart';
import 'package:cooperado_minha_unimed/shared/locator.dart';
import 'package:cooperado_minha_unimed/shared/screen-transitions/fade.transition.dart';
import 'package:cooperado_minha_unimed/shared/services/version.service.dart';
import 'package:cooperado_minha_unimed/shared/widgets/alert.dart';
import 'package:cooperado_minha_unimed/shared/widgets/unimed_icon_widget.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

const double fontSizeSmall = 14;
const double fontSizeBig = 20;
const double leftPadding = 13;
const double height = 140;

class HeaderCooperado extends StatefulWidget {
  final FirebaseAnalytics analytics;
  final FirebaseAnalyticsObserver observer;
  final ScrollController scrollController;

  const HeaderCooperado({
    super.key,
    required this.analytics,
    required this.observer,
    required this.scrollController,
  });

  @override
  HeaderCooperadoState createState() => HeaderCooperadoState();
}

class HeaderCooperadoState extends State<HeaderCooperado> {
  bool enableButtonGuiaMedico = false;
  bool isLoggingOut = false;
  // final _myFirebaseAnalytics = Locator.instance.get<FirebaseAnalytics>();
  final TextEditingController _tecTexto = TextEditingController();
  String message = 'Olá';
  late ScrollController _scrollController;
  bool _isScrolled = false;
  late Profile _profileConfig;

  @override
  void initState() {
    super.initState();
    _profileConfig =
        context.read<AuthCubit>().modelGeneralConfigModel.home.profile;
    _setHeaderMessage();
    _tecTexto.addListener(_listenerTecTexto);

    widget.scrollController.addListener(_scrollListener);
  }

  void _scrollListener() {
    if (widget.scrollController.hasClients) {
      setState(() {
        _isScrolled = widget.scrollController.offset >
            40; // Detecta se a tela foi rolada para cima
      });
    }
  }

  void _setHeaderMessage() {
    final profile = context.read<ProfileCubit>().user;

    final List<String>? nomes = profile.nome?.split(' ');
    String? nome = profile.nome;

    if (nomes!.length > 2) {
      nome = '${nomes[0]} ${nomes[nomes.length - 1]}';
    }

    message = 'Olá, $nome';
  }

  @override
  void dispose() {
    super.dispose();
    _tecTexto.removeListener(_listenerTecTexto);
    _scrollController.dispose();
  }

  void _listenerTecTexto() {
    setState(() {
      enableButtonGuiaMedico = _tecTexto.text.trim().length >= 3;
    });
  }

  void _alertVersion(BuildContext context) {
    final VersionInfo info = Locator.instance!.get<VersionService>().info!;

    Alert.open(
      context,
      title: 'Versão',
      text: '${info.version} (${info.buildNumber})',
    );
  }

  _logout(context) {
    List<Widget> actions = [
      BlocConsumer<AuthCubit, AuthState>(
        listener: (context, state) {
          if (state is ErrorLogoutUserState) {
            setState(() => isLoggingOut = false);
          }
        },
        builder: (context, state) {
          return ElevatedButton(
            style: ElevatedButton.styleFrom(
                shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(5.0)),
                backgroundColor: CooperadoColors.tealGreen,
                textStyle: const TextStyle(
                  color: Colors.white,
                )),
            onPressed: _clickLogout,
            child: const Text(
              'Sim, quero sair',
            ),
          );
        },
      ),
    ];

    Alert.open(context,
        title: 'Tem certeza?',
        text: 'Quer mesmo sair do aplicativo?',
        actions: actions,
        textButtonClose: 'Cancelar');
  }

  void _clickLogout() async {
    if (!isLoggingOut) {
      setState(() {
        isLoggingOut = true;
      });

      Navigator.of(context).pop();
      Navigator.of(context).pop();

      Navigator.pushReplacement(
        context,
        FadeRoute(
          page: LoginScreen(
            openBiometryOnInit: false,
            analytics: widget.analytics,
            observer: widget.observer,
          ),
        ),
      );

      await context.read<AuthCubit>().signout();
    }
  }

  @override
  Widget build(BuildContext context) {
    return SliverAppBar(
      actions: [
        Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            const NotificationsCountWidget(),
            IconButton(
              icon: BlocBuilder<SensitiveDataCubit, SensitiveDataState>(
                builder: (context, state) {
                  return Icon(
                    state.isSensitiveDataVisible
                        ? Icons.visibility
                        : Icons.visibility_off,
                  );
                },
              ),
              onPressed: () {
                context
                    .read<SensitiveDataCubit>()
                    .toggleSensitiveDataVisibility();
              },
            ),
            IconButton(
              icon: const Icon(Icons.person),
              color: Colors.white,
              onPressed: () => _showOptionsModal(),
            ),
          ],
        )
      ],
      leadingWidth: 100,
      leading: Padding(
        padding: const EdgeInsets.only(right: 20, top: 15, bottom: 15),
        child: SilverAppBarReplace(
          child: InkWell(
            child: const UnimedIconWidget(),
            onTap: () => _alertVersion(context),
          ),
        ),
      ),
      flexibleSpace: FlexibleSpaceBar(
        centerTitle: true,
        titlePadding: EdgeInsetsDirectional.only(
          start:
              _isScrolled ? 0 : 5, // Remove o deslocamento horizontal ao rolar
          bottom: 17.0,
        ),
        title: Padding(
          padding: EdgeInsets.symmetric(horizontal: _isScrolled ? 0 : 5),
          child: BlocBuilder<SensitiveDataCubit, SensitiveDataState>(
            builder: (context, state) {
              return SafeArea(
                child: Padding(
                  padding: _isScrolled
                      ? EdgeInsets.only(
                          right: MediaQuery.of(context).size.width * 0.36,
                          left: MediaQuery.of(context).size.width * 0.2,
                        )
                      : EdgeInsets.zero,
                  child: Text(
                    state.isSensitiveDataVisible
                        ? message
                        : 'Olá, seja bem-vindo!',
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: fontSizeSmall,
                    ),
                    overflow: TextOverflow.ellipsis,
                    textAlign: TextAlign.center,
                  ),
                ),
              );
            },
          ),
        ),
        background: _background(),
        collapseMode: CollapseMode.parallax,
      ),
      expandedHeight: 150,
      floating: false,
      snap: false,
      pinned: true,
    );
  }

  _showOptionsModal() {
    showModalBottomSheet<void>(
      context: context,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(15)),
      builder: (BuildContext context) {
        return Padding(
          padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Align(
                alignment: Alignment.topRight,
                child: IconButton(
                  color: CooperadoColors.tealGreen,
                  icon: const Icon(Icons.close),
                  onPressed: () => Navigator.pop(context),
                ),
              ),
              Visibility(
                visible: _profileConfig.myData,
                child: _optionTile(
                  icon: Icons.person,
                  label: 'Meus Dados',
                  onPressed: () {
                    _close(context);
                    Navigator.push(
                      context,
                      FadeRoute(
                        page: ConfigProfileScreen(
                          analytics: widget.analytics,
                          observer: widget.observer,
                        ),
                      ),
                    );
                  },
                ),
              ),
              _optionTile(
                icon: Icons.sticky_note_2,
                label: 'Política de Privacidade',
                onPressed: () {
                  _close(context);
                  Navigator.push(
                    context,
                    FadeRoute(
                      page: WebViewPrivacyPolicy(
                        analytics: widget.analytics,
                        observer: widget.observer,
                      ),
                    ),
                  );
                },
              ),
              _optionTile(
                icon: Icons.browser_updated_outlined,
                label: 'Canal de Ética',
                onPressed: () {
                  _close(context);
                  Navigator.push(
                    context,
                    FadeRoute(
                      page: WebViewChannelEthics(
                        analytics: widget.analytics,
                        observer: widget.observer,
                      ),
                    ),
                  );
                },
              ),
              const SizedBox(height: 10),
              ElevatedButton(
                onPressed: () => _logout(context),
                child: const Text('Sair'),
              ),
              const SizedBox(height: 30),
            ],
          ),
        );
      },
    );
  }

  void _close(BuildContext context) {
    if (Navigator.canPop(context)) {
      Navigator.pop(context);
    }
  }

  Widget _optionTile({
    required IconData icon,
    required String label,
    required VoidCallback onPressed,
    double iconSize = 24,
  }) {
    return InkWell(
      onTap: onPressed,
      child: Column(
        children: [
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 8.0),
            child: Row(
              children: [
                Icon(icon, color: CooperadoColors.tealGreen, size: iconSize),
                const SizedBox(width: 16.0),
                Expanded(
                    child: Text(
                  label,
                  style: const TextStyle(color: CooperadoColors.tealGreen),
                )),
              ],
            ),
          ),
          const Divider(color: Colors.grey)
        ],
      ),
    );
  }

  Widget _background() {
    return Container(
      decoration: const BoxDecoration(
        color: CooperadoColors.tealGreenDark,
      ),
      child: Padding(
        padding: const EdgeInsets.only(top: 5), // Ajustado de 20 para 5
        child: Stack(
          children: <Widget>[
            Padding(
              padding: EdgeInsets.only(
                  top: MediaQuery.of(context).padding.top -
                      5), // Subindo um pouco
              child: GestureDetector(
                onDoubleTap: () => _alertVersion(context),
                child: Container(
                  alignment: Alignment.topCenter,
                  height: height,
                  child: const SafeArea(
                    child: UnimedIconWidget(width: 100),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
