import 'package:cooperado_minha_unimed/screens/res-internal/res_image_result.dart';
import 'package:cooperado_minha_unimed/screens/res-internal/res_lab_exam.dart';
import 'package:flutter/material.dart';

class TabBarExam extends StatelessWidget {
  final String card;
  const TabBarExam({super.key, required this.card});

  @override
  Widget build(BuildContext context) {
    return DefaultTabController(
      initialIndex: 0,
      length: 2,
      child: Scaffold(
        appBar: AppBar(
          title: const Text('Exames'),
          bottom: const TabBar(
            indicatorWeight: 2,
            indicatorPadding: EdgeInsets.only(bottom: 2),
            indicatorColor: Colors.white,
            tabs: <Widget>[
              Tab(
                text: "Laboratoriais",
              ),
              Tab(
                text: "Imagem",
              ),
            ],
          ),
        ),
        body: TabBarView(
          children: <Widget>[
            ResLabExamScreen(card: card),
            ResImageExamResultScreen(card: card)
          ],
        ),
      ),
    );
  }
}
