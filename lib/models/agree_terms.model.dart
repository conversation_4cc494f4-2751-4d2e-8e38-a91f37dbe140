import 'package:json_annotation/json_annotation.dart';

part 'agree_terms.model.g.dart';

@JsonSerializable(anyMap: true)
class AgreeTermsModel {
  @Json<PERSON>ey(name: 'codigo')
  int code;

  @Json<PERSON>ey(name: 'titulo')
  String title;

  @Json<PERSON>ey(name: 'descricao')
  String subtitle;

  @Json<PERSON>ey(name: 'parametro')
  String pdf;

  AgreeTermsModel({
    required this.code,
    required this.title,
    required this.subtitle,
    required this.pdf,
  });

  factory AgreeTermsModel.fromJson(Map json) => _$AgreeTermsModelFromJson(json);
  Map<String, dynamic> toJson() => _$AgreeTermsModelToJson(this);
}
