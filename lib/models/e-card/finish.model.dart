class EcardFinishCardModel {
  final String message;

  EcardFinishCardModel({required this.message});

  factory EcardFinishCardModel.fromJson(Map<String, dynamic> json) {
    return EcardFinishCardModel(
      message: json['message'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'data': {
        'eCardDisableCard': {
          'message': message,
        },
      },
    };
  }
}
