import 'package:cooperado_minha_unimed/models/channel_ethics.model.dart';
import 'package:equatable/equatable.dart';

abstract class ChannelEthicsState extends Equatable {
  const ChannelEthicsState();
}

class ChannelEthicsInital extends ChannelEthicsState {
  @override
  List<Object> get props => [];
}

class ChannelEthicsLoading extends ChannelEthicsState {
  @override
  List<Object> get props => [];
}

class ChannelEthicsStateDone extends ChannelEthicsState {
  final ChannelEthicsResponsive channelResposive;

  @override
  List<Object> get props => [];
  const ChannelEthicsStateDone({required this.channelResposive});
}

class ChannelEthicsError extends ChannelEthicsState {
  final String message;
  @override
  List<Object> get props => [message];
  const ChannelEthicsError(this.message);
}
