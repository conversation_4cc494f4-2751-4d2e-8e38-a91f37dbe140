import 'dart:async';

import 'package:cooperado_minha_unimed/bloc/news/news_cubit.dart';
import 'package:cooperado_minha_unimed/colors.dart';
import 'package:cooperado_minha_unimed/screens/news/card_news.dart';
import 'package:cooperado_minha_unimed/screens/news/card_other_news.dart';
import 'package:cooperado_minha_unimed/shared/vo/transparencia/demostrativo_resultado.dart';
import 'package:cooperado_minha_unimed/shared/widgets/error-banner/error_banner.component.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';

class ListAllNews extends StatefulWidget {
  final List<String> categories;
  final TextEditingController searchText;
  const ListAllNews(
      {super.key, required this.categories, required this.searchText});
  @override
  ListAllNewsState createState() => ListAllNewsState();
}

class ListAllNewsState extends State<ListAllNews> {
  ScrollController controller = ScrollController();
  Timer? _debounce;
  int page = 1;
  String textOld = '';

  listenerSearch() {
    if (textOld != widget.searchText.text) {
      if (_debounce?.isActive ?? false) _debounce!.cancel();
      _debounce = Timer(const Duration(milliseconds: 500), () {
        context
            .read<NewsCubit>()
            .getListNewsSearch(search: widget.searchText.text);
      });
      setState(() {
        textOld = widget.searchText.text;
      });
    }
  }

  @override
  void initState() {
    context.read<NewsCubit>().getListNewsEventPagination(
          categories: widget.categories,
          page: page,
        );
    widget.searchText.addListener(listenerSearch);
    //TODO ajustar a lógica de paginação

    controller.addListener(() {
      if (controller.position.pixels == controller.position.maxScrollExtent) {
        setState(() {
          page++;
        });
        _updateListNoticias();
      }
    });

    super.initState();
  }

  @override
  void dispose() {
    _debounce?.cancel();
    widget.searchText.removeListener(listenerSearch);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return _loadBloc();
  }

  Widget _loadBloc() {
    return BlocBuilder<NewsCubit, NewsState>(
      builder: (context, state) {
        if (state is DoneGetListNewsState) {
          return Column(
            children: [
              Expanded(child: _listNoticias(state.list)),
            ],
          );
        } else if (state is ErrorGetListNewsState) {
          return Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              ErrorBanner(message: state.message),
            ],
          );
        } else if (state is LoadingGetListNewsStatePagination) {
          return Column(
            children: [
              Expanded(child: _listNoticias(state.list)),
              const SpinKitCircle(color: CooperadoColors.tealGreen),
            ],
          );
        } else if (state is LoadingGetListNewsState) {
          return const SpinKitCircle(color: CooperadoColors.tealGreen);
        } else {
          return Container();
        }
      },
    );
  }

  Widget _listNoticias(List<Noticia> noticias) {
    double height = MediaQuery.of(context).size.height * 0.21;
    double width = MediaQuery.of(context).size.width * 0.545;
    return Padding(
      padding: const EdgeInsets.all(5),
      child: SizedBox(
        height: height,
        width: MediaQuery.of(context).size.width,
        child: noticias.isEmpty
            ? const Center(child: Text("Não há noticias no momento"))
            : ListView.builder(
                controller: controller,
                shrinkWrap: true,
                itemCount: noticias.length,
                itemBuilder: (context, index) {
                  final item = noticias.elementAt(index);
                  return index == 0
                      ? CardNews(
                          noticia: item,
                          height: height,
                          width: width,
                        )
                      : CardOtherNews(
                          noticiasPortal: item,
                        );
                }),
      ),
    );
  }

  _updateListNoticias() async {
    if (page <= 5 && widget.searchText.text.isEmpty) {
      context.read<NewsCubit>().getListNewsEventPagination(
            categories: widget.categories,
            page: page,
          );
    }
  }
}
