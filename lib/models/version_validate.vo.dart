class VersionValidateResponse {
  String? message;
  String? lastVersion;
  String? version;
  bool? force;
  bool? isOutOfDate;

  VersionValidateResponse(
      {this.message,
      this.lastVersion,
      this.version,
      this.force,
      this.isOutOfDate});

  VersionValidateResponse.fromJson(Map<String, dynamic> json) {
    message = json['message'];
    lastVersion = json['lastVersion'];
    version = json['version'];
    force = json['force'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['message'] = message;
    data['lastVersion'] = lastVersion;
    data['version'] = version;
    data['force'] = force;
    data['isOutOfDate'] = isOutOfDate;
    return data;
  }
}
