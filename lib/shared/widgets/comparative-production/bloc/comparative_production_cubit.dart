import 'package:bloc/bloc.dart';
import 'package:cooperado_minha_unimed/colors.dart';
import 'package:cooperado_minha_unimed/shared/locator.dart';
import 'package:cooperado_minha_unimed/shared/widgets/comparative-production/models/comparative_production_model.dart';
import 'package:cooperado_minha_unimed/shared/widgets/comparative-production/service/comparative_production_service.dart';
import 'package:cooperado_minha_unimed/shared/widgets/comparative-production/view/widget/production_graphics.dart';
import 'package:equatable/equatable.dart';
import 'package:intl/intl.dart';

part 'comparative_production_state.dart';

class ComparativeProductionCubit extends Cubit<ComparativeProductionState> {
  ComparativeProductionCubit() : super(InitialComparativeProductionState());

  List<ProductionSerie> _productionList = [];
  List<ProductionSerie> get prodcutionList => _productionList;
  DateTime? _selectedDate;
  DateTime? get selectedDate => _selectedDate;
  String? _formattedDate;
  String? get formattedDate => _formattedDate;

  getIndicators(DateTime date) async {
    try {
      emit(LoadingComparativeProductionState());
      _initialize();
      final comparative = await Locator.instance!
              <ComparativeProductionService>()
          .getIndicadoresCompProducao(periodo: date);
      _mount(comparative);
      emit(LoadedComparativeProductionState(comparative));
    } catch (e) {
      emit(ErrorComparativeProductionState(e.toString()));
    }
  }

  void _initialize() {
    _productionList.clear();
    _productionList = <ProductionSerie>[
      ProductionSerie(title: "M.E", valor: 0, barColor: CooperadoColors.green),
      ProductionSerie(
          title: "Prod", valor: 0, barColor: CooperadoColors.tealGreen),
    ];
  }

  void _mount(ComparativeProductionModel comparativeProductionModel) {
    _productionList.add(ProductionSerie(
        title: "M.E",
        valor: comparativeProductionModel.mediaPrestadores,
        barColor: CooperadoColors.tealGreen));
    _productionList.add(
      ProductionSerie(
        title: "Prod",
        valor: comparativeProductionModel.valorPrestador!.toDouble(),
        barColor: CooperadoColors.green,
      ),
    );
  }

  selectDate(DateTime date) {
    emit(SelectDateLoading());
    _selectedDate = date;
    _formattedDate = DateFormat('MM/yyyy').format(_selectedDate!);
    emit(SelectedDate(_selectedDate));
  }
}
