import 'package:cooperado_minha_unimed/models/redefine_password.model.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  RedefinePasswordModel? redefinePasswordModel;
  dynamic redefinePasswordModelJson;

  setUpAll(
    () {
      redefinePasswordModel = RedefinePasswordModel(
        crm: '1234',
        token: '48578',
        senha: '123456',
        confirmarSenha: '123456',
      );
      redefinePasswordModelJson = {
        "crm": "1234",
        "token": "551818",
        "senha": "123456",
        "confirmarSenha": "123456",
      };
    },
  );

  group(
    "isInstanceOf RedefinePasswordModel model tests",
    () {
      test("Should be return instance of redefinePassword", () {
        expect(redefinePasswordModel, isInstanceOf<RedefinePasswordModel>());
      });

      test("Should be return instance of crm String", () {
        expect(redefinePasswordModel!.crm, isInstanceOf<String>());
      });

      test("Should be return instance of password String", () {
        expect(redefinePasswordModel!.senha, isInstanceOf<String>());
      });
    },
  );

  group(
    "Json test",
    () {
      test("Should be return instance of RedefinePasswordModel from json", () {
        expect(RedefinePasswordModel.fromJson(redefinePasswordModelJson),
            isInstanceOf<RedefinePasswordModel>());
      });
      test("Should be return instance of RedefinePassword to json", () {
        expect(redefinePasswordModel!.toJson(),
            isInstanceOf<Map<dynamic, dynamic>>());
      });
      test("Should be return instance of RedefinePassword from json", () {
        final json = redefinePasswordModel!.toJson();
        expect(RedefinePasswordModel.fromJson(json),
            isInstanceOf<RedefinePasswordModel>());
      });
    },
  );
}
