import 'package:flutter/material.dart';

import '../../../colors.dart';

class ErrorBanner extends StatelessWidget {
  final String? message;
  final IconData? icon;
  final String? title;
  final Color backgroundColor;
  final Color? iconColor;
  final double? elevation;
  final MainAxisAlignment alignment;
  const ErrorBanner(
      {super.key,
      required this.message,
      this.icon,
      this.title,
      this.backgroundColor = Colors.white,
      this.iconColor,
      this.elevation,
      this.alignment = MainAxisAlignment.start});

  @override
  Widget build(BuildContext context) {
    return Container(
        width: double.infinity,
        margin: const EdgeInsets.all(8.0),
        child: Card(
          color: backgroundColor,
          elevation: elevation ?? 0,
          child: SizedBox(
            width: 500,
            child: Container(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                mainAxisAlignment: alignment,
                children: [
                  Icon(
                    icon ?? Icons.report_problem_rounded,
                    size: 48,
                    color: iconColor ?? unimedOrange,
                  ),
                  const SizedBox(height: 2.0),
                  Text(title ?? "",
                      style: const TextStyle(fontWeight: FontWeight.bold),
                      textAlign: TextAlign.center),
                  const SizedBox(height: 4.0),
                  Text(message!, textAlign: TextAlign.center),
                ],
              ),
            ),
          ),
        ));
  }
}
