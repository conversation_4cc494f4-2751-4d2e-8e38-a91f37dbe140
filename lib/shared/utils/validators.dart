import 'package:cooperado_minha_unimed/shared/utils/string_utils.dart';
import 'package:intl/intl.dart';

abstract class TextFieldValidators {
  static String? email(String value) {
    Pattern pattern =
        r'^(([^<>()[\]\\.,;:\s@\"]+(\.[^<>()[\]\\.,;:\s@\"]+)*)|(\".+\"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$';
    RegExp regex = RegExp(pattern as String);
    if (!regex.hasMatch(value)) {
      return 'Digite um e-mail válido';
    } else {
      return null;
    }
  }

  static String? cpf(String? value) {
    Pattern pattern = r'^\d{3}\.\d{3}\.\d{3}\-\d{2}$';
    RegExp regex = RegExp(pattern as String);

    if (!regex.hasMatch(value!)) {
      return 'Digite um cpf válido';
    } else if (!StringUtils.validateCpf(
        value.replaceAll(RegExp(r'[^0-9]'), ''))) {
      return 'Digite um cpf válido';
    } else {
      return null;
    }
  }

  static String? crm(String value) {
    if (value.isEmpty) {
      return 'Seu CRM deve ter no mínimo 1 dígitos';
    } else if (value.length > 8) {
      return 'Seu CRM deve ter no máximo 8 dígitos';
    } else {
      return null;
    }
  }

  static String? password(String value) {
    if (value.length < 12) {
      return 'Sua senha deve ter no mínimo 12 dígitos';
    } else {
      return null;
    }
  }

  static String? requiredField(String value) {
    return value.isEmpty ? "Campo obrigatório" : null;
  }

  static bool validadeLandline(String value) {
    final landlineNumber = value.replaceAll(RegExp(r"[-() ]"), "");
    return landlineNumber.length == 10;
  }

  static bool validadePhone(String value) {
    final cellphone = value.replaceAll(RegExp(r"[-() ]"), "");
    return cellphone.length >= 10;
  }

  static String? validateDate(String value) {
    final dateRegExp = RegExp(r"^\d{2}/\d{2}/\d{4}$");
    if (!dateRegExp.hasMatch(value)) {
      return 'Data inválida.';
    }

    final dateFormat = DateFormat('dd/MM/yyyy');
    try {
      dateFormat.parseStrict(value);
    } catch (e) {
      return 'Data inválida.';
    }
    return null;
  }
}
