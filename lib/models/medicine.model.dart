import 'package:cooperado_minha_unimed/shared/utils/string_utils.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:objectbox/objectbox.dart';

@Entity()
class MedicineModel {
  @Id()
  int id = 0;
  @Index()
  final String? codProduto;
  final String? descricaoProduto;
  final String? codigoUnidade;
  final String? descricaoUnidade;
  final String? descricaoClasse;
  final String? valorFormatadoUltimaEntrada;
  final String? descricaoSubstancia;
  final String? dataUltimaEntrada;
  final String? createdAt;

  MedicineModel({
    this.codProduto,
    this.descricaoProduto,
    this.codigoUnidade,
    this.descricaoUnidade,
    this.descricaoClasse,
    this.valorFormatadoUltimaEntrada,
    this.descricaoSubstancia,
    this.dataUltimaEntrada,
    this.createdAt,
  });

  String? get valorUltimaEntradaFormatado {
    // debugPrint('valorUltimaEntrada: $valorUltimaEntrada');
    if (valorUltimaEntrada == null) return null;
    try {
      return StringUtils.formatMoney(valorUltimaEntrada!);
    } catch (e) {
      debugPrint('Erro ao formatar valorUltimaEntrada: $e');
      return null;
    }
  }

  double? get valorUltimaEntrada => (valorFormatadoUltimaEntrada ?? '').isEmpty
      ? null
      : double.parse(valorFormatadoUltimaEntrada!.replaceAll('R\$ ', ''));

  String? get dataUltimaEntradaFormatada {
    if (dataUltimaEntrada == null) return null;
    try {
      return DateFormat('dd/MM/yyyy')
          .format(DateTime.parse(dataUltimaEntrada!));
    } catch (e) {
      return null;
    }
  }

  MedicineModel.fromJson(Map<String, dynamic> json)
      : codProduto = json['codProduto'] as String?,
        descricaoProduto = json['descricaoProduto'] as String?,
        codigoUnidade = json['codigoUnidade'] as String?,
        descricaoUnidade = json['descricaoUnidade'] as String?,
        descricaoClasse = json['descricaoClasse'] as String?,
        valorFormatadoUltimaEntrada =
            json['valorFormatadoUltimaEntrada'] as String?,
        descricaoSubstancia = json['descricaoSubstancia'] as String?,
        dataUltimaEntrada = json['dataUltimaEntrada'] as String?,
        createdAt = json['createdAt'] as String?;

  Map<String, dynamic> toJson() => {
        'codProduto': codProduto,
        'descricaoProduto': descricaoProduto,
        'codigoUnidade': codigoUnidade,
        'descricaoUnidade': descricaoUnidade,
        'descricaoClasse': descricaoClasse,
        'valorFormatadoUltimaEntrada': valorFormatadoUltimaEntrada,
        'descricaoSubstancia': descricaoSubstancia,
        'dataUltimaEntrada': dataUltimaEntrada,
        'createdAt': createdAt,
      };
}
