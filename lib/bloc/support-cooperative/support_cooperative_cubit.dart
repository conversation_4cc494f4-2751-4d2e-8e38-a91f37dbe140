import 'package:cooperado_minha_unimed/models/user.model.dart';
import 'package:cooperado_minha_unimed/shared/api/support_cooperative.api.dart';
import 'package:cooperado_minha_unimed/shared/locator.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

part 'support_cooperative_state.dart';

class SupportCooperativeCubit extends Cubit<SupportCooperativeState> {
  SupportCooperativeCubit() : super(InitialSupportCooperativeState());

  User? _user;
  User get user => _user ?? User.empty;

  void loadSupportCooperative(String link) async {
    try {
      emit(LoadingSupportCooperativeState());

      final content = await Locator.instance!<SupportCooperativeApi>()
          .getSupportCooperativeContent(link);

      emit(LoadedSupportCooperativeState(content: content));
    } catch (e) {
      emit(ErrorSupportCooperativeState(e.toString()));
    }
  }
}
