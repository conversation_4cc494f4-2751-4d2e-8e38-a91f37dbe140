import 'package:cooperado_minha_unimed/models/res/res_exam_result_image_detail_model.dart';

import 'package:cooperado_minha_unimed/models/res/res_exam_result_laboratory_detail_model.dart';
import 'package:cooperado_minha_unimed/shared/api/graphql-res.api.dart';
import 'package:cooperado_minha_unimed/shared/locator.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

part 'res_exam_result_detail_state.dart';

class ResExamResultDetailCubit extends Cubit<ResExamResultDetailState> {
  ResExamResultDetailCubit() : super(InitialResExamResultDetailState());

  List<ResExamResultLaboratoryDetailModel>? _resExamResultLaboratoryDetailModel;
  List<ResExamResultLaboratoryDetailModel>?
      get resExamResultLaboratoryDetailModel =>
          _resExamResultLaboratoryDetailModel;

  List<ResExamResultImageDetailModel> _resExamResultImageDetailModel = [];
  List<ResExamResultImageDetailModel> get resExamResultImageDetailModel =>
      _resExamResultImageDetailModel;

  void getExamResultLaboratoryDetail(
      {required String crm,
      required String card,
      required String code,
      int index = 0}) async {
    try {
      emit(LoadingResExamResultDetailState());

      _resExamResultLaboratoryDetailModel = await Locator.instance!
              <ResGraphQlApi>()
          .resExamResultLaboratoryDetail(crm: crm, card: card, code: code);

      if (_resExamResultLaboratoryDetailModel!.isEmpty) {
        emit(NoDataResExamResultDetailState());
      } else {
        emit(LoadedResExamResultLaboratoryDetailState(
            resExamResultLaboratoryDetailModel:
                _resExamResultLaboratoryDetailModel!,
            index: index));
      }
    } catch (e) {
      emit(ErrorResExamResultDetailState(message: e.toString()));
    }
  }

  void getExamResultImageDetail({
    required String crm,
    required String card,
    required String code,
  }) async {
    try {
      emit(LoadingResExamResultDetailState());

      _resExamResultImageDetailModel = await Locator.instance!<ResGraphQlApi>()
          .resExamResultImageDetail(crm: crm, card: card, code: code);

      emit(LoadedResExamResultImageDetailState(
        resExamResultImageDetailModel: _resExamResultImageDetailModel,
      ));
    } catch (e) {
      emit(ErrorResExamResultDetailState(message: e.toString()));
    }
  }
}
