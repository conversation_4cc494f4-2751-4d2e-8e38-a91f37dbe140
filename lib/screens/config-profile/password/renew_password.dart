import 'package:cooperado_minha_unimed/bloc/auth/auth_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/config-profile/config_profile_cubit.dart';
import 'package:cooperado_minha_unimed/colors.dart';
import 'package:cooperado_minha_unimed/models/user.model.dart';
import 'package:cooperado_minha_unimed/screens/login.dart';
import 'package:cooperado_minha_unimed/shared/screen-transitions/fade.transition.dart';
import 'package:cooperado_minha_unimed/shared/widgets/alert.dart';
import 'package:cooperado_minha_unimed/shared/widgets/unimed_card.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';

class RenewPassword extends StatefulWidget {
  final FirebaseAnalytics analytics;
  final FirebaseAnalyticsObserver observer;

  const RenewPassword({
    super.key,
    required this.analytics,
    required this.observer,
  });

  @override
  RenewPasswordState createState() => RenewPasswordState();
}

class RenewPasswordState extends State<RenewPassword> {
  TextEditingController passController = TextEditingController();
  TextEditingController newPassController = TextEditingController();
  TextEditingController confirmPassontroller = TextEditingController();

  @override
  void initState() {
    super.initState();
    widget.analytics.logScreenView(
      screenName: 'Redefinir senha perfil',
      screenClass: 'RenewPassword',
    );
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<AuthCubit, AuthState>(
      listener: (context, state) async {
        if (state is LoadedAuthState) {
          passController.clear();
          newPassController.clear();
        } else if (state is ErrorAuthState) {
          passController.clear();
          newPassController.clear();
        }
      },
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 15.0, horizontal: 10.0),
        child: UnimedCard(
          child: Padding(
            padding: const EdgeInsets.all(15.0),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                const Text('Senha atual', textAlign: TextAlign.left),
                TextFormField(
                  controller: passController,
                  obscureText: true,
                  decoration: const InputDecoration(
                    border: OutlineInputBorder(),
                    contentPadding:
                        EdgeInsets.symmetric(vertical: 0, horizontal: 10.0),
                  ),
                ),
                const SizedBox(height: 15.0),
                const Text('Nova senha', textAlign: TextAlign.left),
                TextFormField(
                  controller: newPassController,
                  obscureText: true,
                  decoration: const InputDecoration(
                    border: OutlineInputBorder(),
                    contentPadding:
                        EdgeInsets.symmetric(vertical: 0, horizontal: 10.0),
                  ),
                ),
                const SizedBox(height: 15.0),
                const Text('Confirmar nova senha', textAlign: TextAlign.left),
                TextFormField(
                  controller: confirmPassontroller,
                  obscureText: true,
                  decoration: const InputDecoration(
                    border: OutlineInputBorder(),
                    contentPadding:
                        EdgeInsets.symmetric(vertical: 0, horizontal: 10.0),
                  ),
                ),
                const SizedBox(height: 15.0),
                _indicatorState(),
                const SizedBox(height: 15.0),
                _buttonAction()
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _indicatorState() {
    return BlocConsumer<ConfigProfileCubit, ConfigProfileState>(
      listener: (context, state) {
        if (state is ErrorChangePasswordState) {
          showDialog(
            context: context,
            builder: (context) => CooperadoAlertDialog(
              textWidget: Text(state.message, textAlign: TextAlign.center),
              onPressed: () => Navigator.of(context).pop(),
            ),
          );
        } else if (state is ChangedPasswordState) {
          _reLogin(context);
          showDialog(
            context: context,
            builder: (context) => CooperadoAlertDialog(
                textWidget: Text(state.message, textAlign: TextAlign.center),
                onPressed: () {
                  if (context.read<AuthCubit>().state is ErrorAuthState) {
                    Navigator.pushAndRemoveUntil(
                        context,
                        FadeRoute(
                            page: LoginScreen(
                          openBiometryOnInit: false,
                          analytics: widget.analytics,
                          observer: widget.observer,
                        )),
                        (route) => route.isFirst);

                    context.read<AuthCubit>().signout();
                  } else {
                    Navigator.of(context).pop();
                  }
                }),
          );
        }
      },
      builder: (context, state) {
        if (state is ChangingPasswordState) {
          return const SpinKitThreeBounce(
              color: CooperadoColors.tealGreen, size: 30);
        } else {
          return Container();
        }
      },
    );
  }

  Widget _buttonAction() {
    return Container(
      margin: const EdgeInsets.only(top: 15.0),
      child: Material(
        child: InkWell(
          onTap: () => _changePassword(),
          borderRadius: BorderRadius.circular(7.0),
          child: Ink(
            padding: const EdgeInsets.symmetric(vertical: 15.0),
            decoration: BoxDecoration(
                color: CooperadoColors.tealGreen,
                borderRadius: BorderRadius.circular(7.0)),
            child: const Text(
              'ALTERAR',
              style: TextStyle(color: Colors.white),
              textAlign: TextAlign.center,
            ),
          ),
        ),
      ),
    );
  }

  _changePassword() async {
    if (confirmPassontroller.text != newPassController.text) {
      showDialog(
          context: context,
          builder: (context) => CooperadoAlertDialog(
                textWidget: const Text(
                    'Nova senha e confirmação não conferem...',
                    textAlign: TextAlign.center),
                onPressed: () => Navigator.of(context).pop(),
              ));
    } else if (passController.text == confirmPassontroller.text) {
      showDialog(
          context: context,
          builder: (context) => CooperadoAlertDialog(
                textWidget: const Text(
                    'Nova senha deve ser diferente da atual...',
                    textAlign: TextAlign.center),
                onPressed: () => Navigator.of(context).pop(),
              ));
    } else {
      context.read<ConfigProfileCubit>().renewPassword(passController.text,
          newPassController.text, confirmPassontroller.text);
      // passController.clear();
      // newPassController.clear();
      confirmPassontroller.clear();
    }
  }

  void _reLogin(BuildContext context) {
    if (newPassController.text.isNotEmpty) {
      context.read<AuthCubit>().autenticate(
            credentials: UserCredentials(
              crm: context.read<AuthCubit>().credentials.crm,
              password: newPassController.text,
            ),
          );
    }
  }
}
