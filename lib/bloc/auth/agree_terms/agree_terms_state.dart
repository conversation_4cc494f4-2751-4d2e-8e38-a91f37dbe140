part of 'agree_terms_cubit.dart';

abstract class AgreeTermsState extends Equatable {
  const AgreeTermsState(this.agreeTerm);
  final bool agreeTerm;
  @override
  List<Object?> get props => [];
}

class InitialAgreeTerms extends AgreeTermsState {
  const InitialAgreeTerms(super.agreeTerm);
}

class AlertState extends AgreeTermsState {
  final int termsQuantity;
  final List<AgreeTermsModel> agreeTermsModel;
  @override
  List<Object?> get props => [termsQuantity, agreeTermsModel];
  const AlertState(
    this.agreeTermsModel,
    this.termsQuantity,
    super.agreeTerm,
  );
}

class LoadedAgreeTermsState extends AgreeTermsState {
  @override
  List<Object> get props => [];

  const LoadedAgreeTermsState(
    super.agreeTerm,
  );
}

class LoadingState extends AgreeTermsState {
  @override
  List<Object> get props => [];

  const LoadingState(
    super.agreeTerm,
  );
}

class LoadingDownloadState extends AgreeTermsState {
  @override
  List<Object> get props => [];

  const LoadingDownloadState(
    super.agreeTerm,
  );
}

class ErrorPermissionState extends AgreeTermsState {
  @override
  List<Object> get props => [];

  const ErrorPermissionState(
    super.agreeTerm,
  );
}

class SuccessDownloadState extends AgreeTermsState {
  final File file;
  @override
  List<Object> get props => [file];

  const SuccessDownloadState(
    this.file,
    super.agreeTerm,
  );
}

class ErrorState extends AgreeTermsState {
  final String message;
  const ErrorState(
    this.message,
    super.agreeTerm,
  );
  @override
  List<Object> get props => [message];
}

class PopAgreeTermsState extends AgreeTermsState {
  @override
  List<Object> get props => [];

  const PopAgreeTermsState(
    super.agreeTerm,
  );
}

class PopAgreeStoragePermissionState extends AgreeTermsState {
  final String pdf;
  final String title;
  final PermissionStatus? status;
  @override
  List<Object> get props => [];

  const PopAgreeStoragePermissionState(
    this.pdf,
    this.title,
    this.status,
    super.agreeTerm,
  );
}
