import 'package:bloc/bloc.dart';
import 'package:cooperado_minha_unimed/models/medicine.model.dart';
import 'package:cooperado_minha_unimed/objectbox.g.dart';
import 'package:cooperado_minha_unimed/shared/locator.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';

part 'search_medicines_state.dart';

class SearchMedicinesCubit extends Cubit<SearchMedicinesState> {
  SearchMedicinesCubit() : super(SearchMedicinesInitial());

  setToInitial() {
    emit(SearchMedicinesInitial());
  }

  searchMedicine({required String query}) async {
    try {
      emit(LoadingSearchMedicinesState());
      final box = Locator.instance!<Store>().box<MedicineModel>();

      final medicineQuery = box
          .query(
            MedicineModel_.descricaoProduto
                .contains(
                  query.toUpperCase(),
                  caseSensitive: true,
                )
                .or(
                  MedicineModel_.descricaoSubstancia.contains(
                    query.toUpperCase(),
                    caseSensitive: true,
                  ),
                ),
          )
          .build();

      final result = await medicineQuery.findAsync();

      emit(LoadedSearchListMedicinesState(medicines: result));

      medicineQuery.close();
    } catch (ex) {
      debugPrint(ex.toString());
      emit(ErrorSearchMedicinesState('$ex'));
    }
  }
}
