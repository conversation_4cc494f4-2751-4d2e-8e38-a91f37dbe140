import 'package:bloc/bloc.dart';
import 'package:cooperado_minha_unimed/shared/api/services.api.dart';
import 'package:cooperado_minha_unimed/shared/locator.dart';
import 'package:cooperado_minha_unimed/shared/vo/servicos/extract_copart.vo.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

part 'extract_quota_part_state.dart';

class ExtractQuotaPartCubit extends Cubit<ExtractQuotaPartState> {
  ExtractQuotaPartCubit() : super(ExtractQuotaPartInitial());
  DateTime? _selectedDate;
  DateTime? get selectedDate => _selectedDate;
  String? _formattedDate;
  String? get formattedDate => _formattedDate;

  selectDate(DateTime? date) {
    emit(SelectDateLoading());
    _selectedDate = date;
    _formattedDate = DateFormat('MM/yyyy').format(_selectedDate!);
    emit(DoneSelectDate(_selectedDate));
  }

  getExtractQuotaPart(lastProduction) async {
    try {
      emit(LoadingGetExtractQuotaPartState());
      final ExtractCopartVO extractCopart = await Locator.instance!
              <ServicesApi>()
          .getExtractCopart(lastProduction: lastProduction);

      emit(DoneGetExtractQuotaPartState(extractCopart));
    } catch (ex) {
      debugPrint(ex.toString());
      emit(ErrorGetExtractQuotaPartState('$ex'));
    }
  }
}
