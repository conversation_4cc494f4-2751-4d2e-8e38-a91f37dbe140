import 'package:cooperado_minha_unimed/colors.dart';
import 'package:cooperado_minha_unimed/screens/config-profile/widgets/header_config_buttom.dart';
import 'package:cooperado_minha_unimed/shared/utils/uicons.dart';
import 'package:flutter/material.dart';

class HeaderConfigProfile extends StatefulWidget {
  final Function? onPressProfile;
  final Function? onPressNewPass;
  final int activePage;

  const HeaderConfigProfile(
      {super.key,
      this.onPressProfile,
      this.onPressNewPass,
      this.activePage = 0});

  @override
  HeaderConfigProfileState createState() => HeaderConfigProfileState();
}

class HeaderConfigProfileState extends State<HeaderConfigProfile> {
  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 10.0, horizontal: 10.0),
      decoration: const BoxDecoration(
        color: CooperadoColors.grayLight,
        borderRadius: BorderRadius.only(
          bottomLeft: Radius.circular(15),
          bottomRight: Radius.circular(15),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.grey,
            offset: Offset(0.0, 1.0), //(x,y)
            blurRadius: 6.0,
          ),
        ],
      ),
      child: Row(
        children: [
          Expanded(
            child: Padding(
              padding: const EdgeInsets.all(5.0),
              child: HeaderConfigButtom(
                  icon: UIcons.doctor,
                  label: 'Perfil',
                  active: widget.activePage == 0,
                  action: () {
                    if (widget.onPressProfile != null) widget.onPressProfile!();
                    // context.read<ConfigProfileCubit>().getAddresses();
                    // context.read<ConfigProfileCubit>().setPage(0);
                  }),
            ),
          ),
          Expanded(
            child: Padding(
              padding: const EdgeInsets.all(5.0),
              child: HeaderConfigButtom(
                  icon: Icons.phonelink_lock,
                  label: 'Alterar\nSenha',
                  active: widget.activePage == 1,
                  action: () {
                    if (widget.onPressNewPass != null) widget.onPressNewPass!();
                  }),
            ),
          ),
        ],
      ),
    );
  }
}
