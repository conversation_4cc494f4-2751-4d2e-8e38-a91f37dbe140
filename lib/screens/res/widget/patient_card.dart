import 'package:cooperado_minha_unimed/colors.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

class PatientCard extends StatelessWidget {
  final String name;
  final String cpf;

  const PatientCard({required this.name, required this.cpf, super.key});

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: EdgeInsets.zero,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
      ),
      elevation: 0,
      child: Padding(
        padding: const EdgeInsets.only(
          left: 12,
          right: 12,
          bottom: 12,
        ),
        child: Column(
          children: [
            ListTile(
              contentPadding: EdgeInsets.zero,
              leading: Container(
                width: 40,
                height: 40,
                padding: EdgeInsets.zero,
                margin: EdgeInsets.zero,
                decoration: BoxDecoration(
                  color: CooperadoColors.greenDark,
                  borderRadius: BorderRadius.circular(25),
                ),
                child: Center(
                  child: SvgPicture.asset(
                    'assets/svg/icon_user.svg',
                    width: 14,
                    height: 14,
                  ),
                ),
              ),
              title: Text(
                name,
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w400,
                  color: CooperadoColors.blackText,
                ),
              ),
              subtitle: Text(_formatCpf(cpf)),
            ),

            // const Divider(),
            // const Text(
            //   'Último atendimento: 3 de agosto as 14:15',
            //   style: TextStyle(
            //     fontSize: 12,
            //     fontWeight: FontWeight.w400,
            //     color: CooperadoColors.opcionalGray3,
            //   ),
            // ),
          ],
        ),
      ),
    );
  }

  String _formatCpf(String cpf) {
    return '${cpf.substring(0, 3)}.${cpf.substring(3, 6)}.${cpf.substring(6, 9)}-${cpf.substring(9)}';
  }
}
