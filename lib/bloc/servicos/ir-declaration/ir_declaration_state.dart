part of 'ir_declaration_cubit.dart';

abstract class IRDeclarationState extends Equatable {
  const IRDeclarationState();

  @override
  List<Object?> get props => [];
}

class IRDeclarationInitial extends IRDeclarationState {}

class LoadingGetIRDeclarationState extends IRDeclarationState {
  @override
  List<Object> get props => [];
}

class ErrorGetIRDeclarationState extends IRDeclarationState {
  final String message;
  @override
  List<Object> get props => [message];
  const ErrorGetIRDeclarationState(this.message);
}

class DoneGetIRDeclarationState extends IRDeclarationState {
  final String? link;
  @override
  List<Object?> get props => [link];

  const DoneGetIRDeclarationState(this.link);
}
