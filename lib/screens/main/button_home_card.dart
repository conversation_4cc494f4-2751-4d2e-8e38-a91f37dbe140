import 'package:cooperado_minha_unimed/colors.dart';
import 'package:flutter/material.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:flutter_svg/flutter_svg.dart';

class ButtonHome extends StatelessWidget {
  final Function onPressed;
  final Color? backgroundColor;
  final IconData? icomoon;
  final String? text;
  final String? subtitle;
  final bool bold;
  final bool loading;
  final String? pathIcon;
  const ButtonHome(
      {super.key,
      required this.onPressed,
      this.backgroundColor,
      this.icomoon,
      this.text,
      this.subtitle,
      this.bold = false,
      this.pathIcon,
      this.loading = false});
  @override
  Widget build(BuildContext context) {
    return _button();
  }

  Widget _button() {
    return Padding(
      padding: const EdgeInsets.all(8.0),
      child: InkWell(
        onTap: loading ? null : onPressed as void Function()?,
        child: Container(
          width: 110,
          height: 120,
          decoration: const BoxDecoration(
            borderRadius: BorderRadius.only(
              bottomLeft: Radius.circular(15),
              topLeft: Radius.circular(15),
              topRight: Radius.circular(15),
            ),
            color: Colors.white,
            boxShadow: [
              BoxShadow(
                color: CooperadoColors.grayLight5,
                blurRadius: 5,
                offset: Offset(3, 5),
              ),
            ],
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: loading
                ? <Widget>[
                    const SpinKitThreeBounce(
                      color: CooperadoColors.tealGreen,
                      size: 20,
                    )
                  ]
                : <Widget>[
                    (pathIcon != null && pathIcon!.isNotEmpty)
                        ? SvgPicture.asset(
                            pathIcon!,
                            // ignore: deprecated_member_use
                            //color: CooperadoColors.tealGreen,
                            colorFilter: backgroundColor == null
                                ? null
                                : ColorFilter.mode(
                                    backgroundColor!,
                                    BlendMode.srcIn,
                                  ),
                            width: text != null ? 35 : 70,
                          )
                        : Icon(
                            icomoon,
                            size: text != null ? 35 : 70,
                            color: CooperadoColors.tealGreen,
                          ),
                    if (text != null)
                      Padding(
                        padding: const EdgeInsets.all(4.0),
                        child: Text(
                          text!,
                          textAlign: TextAlign.center,
                          style: TextStyle(
                            fontSize: 13,
                            color: CooperadoColors.grayDark2,
                            fontWeight:
                                bold ? FontWeight.bold : FontWeight.normal,
                          ),
                        ),
                      ),
                    if (subtitle != null) ...[
                      const SizedBox(
                          height: 2), // Pequeno espaço entre título e subtítulo
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 8.0),
                        child: Text(
                          subtitle!,
                          textAlign: TextAlign.center,
                          style: const TextStyle(
                            fontSize: 10, // Fonte menor para o subtítulo
                            color: CooperadoColors.grayDark2,
                          ),
                        ),
                      ),
                    ]
                  ],
          ),
        ),
      ),
    );
  }
}
