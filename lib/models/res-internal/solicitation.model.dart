import 'package:intl/intl.dart';

class ResSolicitationModel {
  late String numInvoice;
  late String typeSolicitation;
  late String card;
  late String codProvider;
  late String nameProvider;
  late String nameBeneficiary;
  late String attendanceDate;

  ResSolicitationModel(
      {required this.numInvoice,
      required this.typeSolicitation,
      required this.card,
      required this.codProvider,
      required this.nameProvider,
      required this.nameBeneficiary,
      required this.attendanceDate});

  String get dateAttendanceFormatted {
    return DateFormat('dd/MM/yyyy HH:mm')
        .format(DateTime.parse(attendanceDate));
  }

  ResSolicitationModel.fromJson(Map<String, dynamic> json) {
    numInvoice = json['numInvoice'];
    typeSolicitation = json['typeSolicitation'];
    card = json['card'];
    codProvider = json['codProvider'];
    nameProvider = json['nameProvider'];
    nameBeneficiary = json['nameBeneficiary'];
    attendanceDate = json['attendanceDate'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['numInvoice'] = numInvoice;
    data['typeSolicitation'] = typeSolicitation;
    data['card'] = card;
    data['codProvider'] = codProvider;
    data['nameProvider'] = nameProvider;
    data['nameBeneficiary'] = nameBeneficiary;
    data['attendanceDate'] = attendanceDate;

    return data;
  }
}
