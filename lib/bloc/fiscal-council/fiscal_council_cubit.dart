import 'package:bloc/bloc.dart';
import 'package:cooperado_minha_unimed/models/fiscal_council.model.dart';
import 'package:cooperado_minha_unimed/shared/api/fiscal_council.api.dart';
import 'package:cooperado_minha_unimed/shared/api/notice.api.dart';
import 'package:cooperado_minha_unimed/shared/locator.dart';
import 'package:cooperado_minha_unimed/shared/vo/transparencia/demostrativo_resultado.dart';
import 'package:equatable/equatable.dart';

part 'fiscal_council_state.dart';

class FiscalCouncilCubit extends Cubit<FiscalCouncilState> {
  FiscalCouncilCubit() : super(FiscalCouncilInitial());

  List<Noticia>? _listCouncil;
  List<Noticia>? _listCouncilFull;

  getCouncil() async {
    emit(LoadingGetCouncil());
    try {
      final retorno =
          await Locator.instance!<FiscalCouncilApi>().getFiscalCouncil();
      emit(DoneGetCouncil(list: retorno));
    } catch (ex) {
      emit(ErrorGetCouncil('$ex'));
    }
  }

  getReports() async {
    emit(LoadingGetReports());
    try {
      _listCouncilFull = await Locator.instance!<NoticeApi>()
          .getNoticeByType(page: 1, categories: ["relatorio", "visita"]);
      emit(DoneGetReports(list: _listCouncilFull));
    } catch (ex) {
      emit(ErrorGetReports('$ex'));
    }
  }

  getInternalCommunic() async {
    emit(LoadingGetInternalCommunic());
    try {
      _listCouncilFull = await Locator.instance!<NoticeApi>()
          .getNoticeByType(page: 1, categories: ["ci"]);
      emit(DoneGetInternalCommunic(list: _listCouncilFull));
    } catch (ex) {
      emit(ErrorGetInternalCommunic('$ex'));
    }
  }

  filterReport(String filter) {
    emit(LoadingGetReports());
    if (filter.isEmpty) {
      _listCouncil = _listCouncilFull;
    } else {
      _listCouncil = [];
      for (Noticia item in _listCouncilFull!) {
        if (item
            .toJson()
            .toString()
            .toLowerCase()
            .contains(filter.toLowerCase())) _listCouncil!.add(item);
      }
    }
    emit(DoneGetReports(list: _listCouncil));
  }

  filterInternalCom(String filter) {
    emit(LoadingGetInternalCommunic());
    if (filter.isEmpty) {
      _listCouncil = _listCouncilFull;
    } else {
      _listCouncil = [];
      for (Noticia item in _listCouncilFull!) {
        if (item
            .toJson()
            .toString()
            .toLowerCase()
            .contains(filter.toLowerCase())) _listCouncil!.add(item);
      }
    }
    emit(DoneGetInternalCommunic(list: _listCouncil));
  }
}
