import 'package:cooperado_minha_unimed/bloc/indicators/last_production/last_production_cubit.dart';
import 'package:cooperado_minha_unimed/colors.dart';
import 'package:cooperado_minha_unimed/shared/utils/string_utils.dart';
import 'package:cooperado_minha_unimed/shared/widgets/graphics/choose_date.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';

class LastProductionDateWidget extends StatefulWidget {
  const LastProductionDateWidget(
      {super.key, required this.onPressed, this.date});
  final Function(DateTime?) onPressed;
  final DateTime? date;

  @override
  State<LastProductionDateWidget> createState() =>
      _LastProductionDateWidgetState();
}

class _LastProductionDateWidgetState extends State<LastProductionDateWidget> {
  final _selectedDate = ValueNotifier<DateTime?>(null);

  @override
  void initState() {
    super.initState();
    if (context.read<LastProductionCubit>().lastDate == null) {
      context.read<LastProductionCubit>().listLastProduction();
    }
    if (widget.date != null) _selectedDate.value = widget.date;
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<LastProductionCubit, LastProductionState>(
      builder: (context, state) {
        if (state is LoadingLastProductionState) {
          return const SpinKitCircle(
            color: CooperadoColors.tealGreen,
          );
        } else if (state is ErrorLastProductionState) {
          return ValueListenableBuilder<DateTime?>(
            valueListenable: _selectedDate,
            builder: (context, value, child) {
              return ChooseDateWidget(
                date: value ?? DateTime.now(),
                textBox: Text(StringUtils.formatDate(value ?? DateTime.now())),
                onPressed: (selectedDate) {
                  if (selectedDate != null) {
                    _selectedDate.value = selectedDate;
                    widget.onPressed(selectedDate);
                  }
                },
              );
            },
          );
        } else if (state is LoadedLastProductionState) {
          return ValueListenableBuilder<DateTime?>(
            valueListenable: _selectedDate,
            builder: (context, value, child) {
              return ChooseDateWidget(
                date: value ?? state.lastDate,
                textBox: Text(StringUtils.formatDate(value ?? state.lastDate)),
                onPressed: (selectedDate) {
                  if (selectedDate != null) {
                    _selectedDate.value = selectedDate;
                    widget.onPressed(selectedDate);
                  }
                },
              );
            },
          );
        }
        return const Center();
      },
    );
  }
}
