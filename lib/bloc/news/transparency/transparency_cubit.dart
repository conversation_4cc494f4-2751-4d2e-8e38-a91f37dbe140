import 'package:bloc/bloc.dart';

import 'package:cooperado_minha_unimed/shared/api/notice.api.dart';
import 'package:cooperado_minha_unimed/shared/locator.dart';
import 'package:cooperado_minha_unimed/shared/vo/transparencia/demostrativo_resultado.dart';
import 'package:equatable/equatable.dart';

part 'transparency_state.dart';

class TransparencyCubit extends Cubit<TransparencyState> {
  TransparencyCubit() : super(TransparencyInitial());

  List<Noticia> _listTrasnparencyNews = [];
  List<Noticia> _listTransparencyNewsSearch = [];
  int _pageCurrent = 0;

  getTransparencyNews(
      {required List<String> categories,
      required int page,
      String search = ''}) async {
    emit(LoadingTransparencyNewsState());
    try {
      final List<Noticia> listPortal = await Locator.instance!<NoticeApi>()
          .getNoticeByType(categories: categories, page: page);

      emit(DoneTransparencyNewsState(list: listPortal));
    } catch (ex) {
      emit(ErrorTransparencyNewsState('$ex'));
    }
  }

  getListNewsEventPagination(
      {required List<String> categories, required int page}) async {
    try {
      if (page == 1) {
        _clearNews();
        emit(LoadingTransparencyNewsState());
      } else {
        emit(LoadingTransparencyNewsStatePagination(
            list: _listTrasnparencyNews));
      }
      if (page > _pageCurrent) {
        final List<Noticia> listPortal = await Locator.instance!<NoticeApi>()
            .getNoticeByType(categories: categories, page: page);
        _addItensPagination(newsResponse: listPortal, page: page);
        _pageCurrent = page;
      }

      emit(DoneTransparencyNewsState(list: _listTrasnparencyNews));
    } catch (ex) {
      emit(ErrorTransparencyNewsState('$ex'));
    }
  }

  getListNewsSearch({String search = ''}) async {
    try {
      emit(LoadingTransparencyNewsStatePagination(
          list: _listTransparencyNewsSearch));

      _searchNewsEvent(search: search);
      await Future.delayed(const Duration(milliseconds: 100));
      emit(DoneTransparencyNewsState(list: _listTransparencyNewsSearch));
    } catch (ex) {
      emit(ErrorTransparencyNewsState('$ex'));
    }
  }

  _clearNews() {
    _listTransparencyNewsSearch = [];
    _listTrasnparencyNews = [];
    _pageCurrent = 0;
  }

  _addItensPagination(
      {required List<Noticia> newsResponse, required int page}) {
    if (page > 1) {
      _listTrasnparencyNews.addAll(newsResponse);
    } else {
      _listTrasnparencyNews.clear();
      _listTrasnparencyNews.addAll(newsResponse);
    }
  }

  _searchNewsEvent({required String search}) {
    _listTransparencyNewsSearch.clear();
    if (search.isEmpty) {
      _listTransparencyNewsSearch.addAll(_listTrasnparencyNews);
    } else {
      for (Noticia element in _listTrasnparencyNews) {
        if (element.titulo!.toUpperCase().contains(search.toUpperCase())) {
          _listTransparencyNewsSearch.add(element);
        }
      }
    }
  }
}
