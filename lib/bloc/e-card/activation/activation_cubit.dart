import 'package:cooperado_minha_unimed/bloc/e-card/activation/activation_state.dart';
import 'package:cooperado_minha_unimed/shared/api/graphqlecard.api.dart';
import 'package:cooperado_minha_unimed/shared/locator.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class EcardActivationCubit extends Cubit<EcardActivationState> {
  EcardActivationCubit() : super(InitialEcardActivationState());

  void activatedAndReactivatedEcard({
    required String codPrestador,
    required int periodInHours,
    bool isReactivated = false,
  }) async {
    try {
      emit(LoadingEcardActivationState());

      final result = await Locator.instance!
          .get<GraphQlApiEcard>()
          .activatedAndReactivatedEcard(
            codPrestador: codPrestador,
            periodInHours: periodInHours,
            isReactivated: isReactivated,
          );

      emit(LoadedEcardActivationState(message: result));
    } catch (e) {
      emit(ErrorEcardActivationState(message: e.toString()));
    }
  }
}
