import 'package:cooperado_minha_unimed/colors.dart';
import 'package:flutter/material.dart';

class BtnAction extends StatelessWidget {
  final Function? action;
  final Color? color;
  final String? text;
  final Color textColor;

  const BtnAction(
      {super.key,
      this.action,
      this.color,
      this.text,
      this.textColor = CooperadoColors.grayDark});

  @override
  Widget build(BuildContext context) {
    return Material(
      child: InkWell(
        onTap: action as void Function()?,
        child: Ink(
          decoration: BoxDecoration(
              color: color,
              borderRadius: const BorderRadius.all(Radius.circular(7.0))),
          height: 45,
          child: Center(
              child: Text(text!,
                  textAlign: TextAlign.center,
                  style: TextStyle(color: textColor))),
        ),
      ),
    );
  }
}
