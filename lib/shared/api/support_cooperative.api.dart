import 'dart:convert';

import 'package:cooperado_minha_unimed/models/user.model.dart';
import 'package:cooperado_minha_unimed/shared/api/auth.api.dart';
import 'package:cooperado_minha_unimed/shared/exceptions.dart';
import 'package:cooperado_minha_unimed/shared/locator.dart';
import 'package:cooperado_minha_unimed/shared/utils/logger_print.dart';
import 'package:http_client/http_client.dart';

class SupportCooperativeApi {
  final UnimedHttpClient httpClient;
  final logger = UnimedLogger(className: 'SupportCooperativeApi');

  SupportCooperativeApi(this.httpClient);

  Future<String> getSupportCooperativeContent(String link) async {
    //capturar token do crm
    UserCredentials? credentials =
        await Locator.instance!.get<AuthApi>().getCredentials();
    final token = credentials != null
        ? await User.createToken(credentials: credentials)
        : "";

    try {
      final url = '$link?token=${token.toUpperCase()}';
      final response = await httpClient.post(Uri.parse(url), headers: {
        "Content-Type": "application/json",
      });

      if (response.statusCode == 200) {
        final data = jsonDecode(utf8.decode(response.bodyBytes));

        return data['retorno']['page']['content'];
      } else {
        final message = jsonDecode(response.body);
        logger.e(
            'getSupportCooperative error statusCode: ${response.statusCode} ${response.body}');
        throw UnimedException('$message');
      }
    } on UnimedException catch (ex) {
      logger.e('getSupportCooperative ${ex.runtimeType} : $ex');
      throw UnimedException(ex.message);
    } catch (ex) {
      logger.e('getSupportCooperative exception : $ex');
      throw UnimedException('Não foi possível no momento.');
    }
  }
}
