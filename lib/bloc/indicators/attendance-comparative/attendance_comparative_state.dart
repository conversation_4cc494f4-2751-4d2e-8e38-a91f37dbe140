part of 'attendance_comparative_cubit.dart';

abstract class AttendanceComparativeState extends Equatable {
  const AttendanceComparativeState();

  @override
  List<Object?> get props => [];
}

class InitialAttendanceComparative extends AttendanceComparativeState {}

class LoadingAttendanceComparativeState extends AttendanceComparativeState {
  @override
  List<Object> get props => [];
}

class ReLoadingAttendanceComparativeState extends AttendanceComparativeState {
  @override
  List<Object> get props => [];
}

class ErrorAttendanceComparativeState extends AttendanceComparativeState {
  final String? message;
  @override
  List<Object?> get props => [message];
  const ErrorAttendanceComparativeState(this.message);
}

class LoadedAttendanceComparativeState extends AttendanceComparativeState {
  final ResponseServiceHistoric? serviceHistoric;

  @override
  List<Object?> get props => [serviceHistoric];

  const LoadedAttendanceComparativeState(this.serviceHistoric);
}
