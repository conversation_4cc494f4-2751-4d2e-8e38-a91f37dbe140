part of 'res_lab_exam_detail_cubit.dart';

abstract class ResLabExamDetailState extends Equatable {
  const ResLabExamDetailState();

  @override
  List<Object> get props => [];
}

class InitialResLabExamDetailState extends ResLabExamDetailState {}

class LoadingResLabExamDetailState extends ResLabExamDetailState {
  @override
  List<Object> get props => [];
}

class ErrorResLabExamDetailState extends ResLabExamDetailState {
  final String message;

  @override
  List<Object> get props => [message];

  const ErrorResLabExamDetailState({required this.message});
}

class LoadedResLabExamDetailState extends ResLabExamDetailState {
  final List<ResLabExamDetailModel> listLabExamDetails;

  @override
  List<Object> get props => [listLabExamDetails];

  const LoadedResLabExamDetailState({required this.listLabExamDetails});
}
