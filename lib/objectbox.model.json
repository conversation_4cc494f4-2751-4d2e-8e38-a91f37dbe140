{"_note1": "KEEP THIS FILE! Check it into a version control system (VCS) like git.", "_note2": "ObjectBox manages crucial IDs for your object model. See docs for details.", "_note3": "If you have VCS merge conflicts, you must resolve them according to ObjectBox docs.", "entities": [{"id": "1:6065420062645272092", "lastPropertyId": "13:4718767397104344303", "name": "MedicineModel", "properties": [{"id": "1:1052784538226618901", "name": "id", "type": 6, "flags": 1}, {"id": "4:1666465937154841173", "name": "codProduto", "type": 9, "flags": 2048, "indexId": "2:4433880279138473262"}, {"id": "5:9037578023627495238", "name": "descricaoProduto", "type": 9}, {"id": "6:8642648986981787316", "name": "codigoUnidade", "type": 9}, {"id": "7:6455795718173183460", "name": "descricaoUnidade", "type": 9}, {"id": "8:5548511493012502856", "name": "descricaoClasse", "type": 9}, {"id": "9:9219227405121542620", "name": "valorFormatadoUltimaEntrada", "type": 9}, {"id": "10:6837400757240078012", "name": "descricaoSubstancia", "type": 9}, {"id": "12:5079960661590223253", "name": "dataUltimaEntrada", "type": 9}, {"id": "13:4718767397104344303", "name": "createdAt", "type": 9}], "relations": []}], "lastEntityId": "1:6065420062645272092", "lastIndexId": "2:4433880279138473262", "lastRelationId": "0:0", "lastSequenceId": "0:0", "modelVersion": 5, "modelVersionParserMinimum": 5, "retiredEntityUids": [], "retiredIndexUids": [1464655163667718871], "retiredPropertyUids": [8595638658350706797, 2631742396173689119, 5357227378264756403], "retiredRelationUids": [], "version": 1}