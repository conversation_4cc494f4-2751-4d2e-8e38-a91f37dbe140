import 'package:cooperado_minha_unimed/models/zipcode.model.dart';
import 'package:json_annotation/json_annotation.dart';

part 'address.model.g.dart';

@JsonSerializable(anyMap: true)
class AddressModel {
  @Json<PERSON>ey(name: 'codigo')
  int? code;

  @Json<PERSON>ey(name: 'tipo')
  String? type;

  @Json<PERSON>ey(name: 'numero')
  String? number;

  @Json<PERSON>ey(name: 'complemento')
  String? complement;

  @Json<PERSON><PERSON>(name: 'correspondencia')
  String? correspondence;

  @Json<PERSON>ey(name: 'cep')
  ZipCodeModel? zipcode;

  @Json<PERSON>ey(name: 'contatos')
  List<ContactModel>? contacts;

  AddressModel({
    this.code,
    this.complement,
    this.contacts,
    this.correspondence,
    this.number,
    this.type,
    this.zipcode,
  });

  factory AddressModel.fromJson(Map json) => _$AddressModelFromJson(json);
  Map<String, dynamic> toJson() => _$AddressModelToJson(this);
}

@JsonSerializable(anyMap: true)
class ContactModel {
  @Json<PERSON>ey(name: 'contato')
  String? contact;

  @Json<PERSON>ey(name: 'tipo')
  TypeContactModel? type;

  ContactModel({this.contact, this.type});

  factory ContactModel.fromJson(Map json) => _$ContactModelFromJson(json);
  Map<String, dynamic> toJson() => _$ContactModelToJson(this);
}

@JsonSerializable(anyMap: true)
class TypeContactModel {
  @JsonKey(name: 'codigo')
  int? code;

  @JsonKey(name: 'nome')
  String? name;

  TypeContactModel({this.code, this.name});

  factory TypeContactModel.fromJson(Map json) =>
      _$TypeContactModelFromJson(json);
  Map<String, dynamic> toJson() => _$TypeContactModelToJson(this);
}
