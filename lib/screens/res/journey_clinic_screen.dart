import 'package:cooperado_minha_unimed/bloc/auth/auth_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/res/beneficiary/res_brasil_beneficiary_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/res/configs/res_configs_cubit.dart';
import 'package:cooperado_minha_unimed/colors.dart';
import 'package:cooperado_minha_unimed/models/general_config_model.dart';
import 'package:cooperado_minha_unimed/models/res/res_brasil_beneficiary_model.dart';
import 'package:cooperado_minha_unimed/screens/res/alerts/res_alerts_screen.dart';
import 'package:cooperado_minha_unimed/screens/res/allergies/res_internal_allergies_screen.dart';
import 'package:cooperado_minha_unimed/screens/res/diagnostic-screen/res_brazil_diagnostics_screen.dart';
import 'package:cooperado_minha_unimed/screens/res/documents/res_internal_document_screen.dart';
import 'package:cooperado_minha_unimed/screens/res/exam-result-screen/res_exam_result_screen.dart';
import 'package:cooperado_minha_unimed/screens/res/indicators/res_internal_indicators_screen.dart';
import 'package:cooperado_minha_unimed/screens/res/procedures/res_internal_precedure_screen.dart';
import 'package:cooperado_minha_unimed/screens/res/res_service_screen.dart';
import 'package:cooperado_minha_unimed/screens/res/widget/card_res_icon_button.dart';
import 'package:cooperado_minha_unimed/screens/res/widget/empty_list.dart';
import 'package:cooperado_minha_unimed/screens/res/widget/erro_service.dart';
import 'package:cooperado_minha_unimed/screens/res/widget/patient_card.dart';
import 'package:cooperado_minha_unimed/shared/widgets/search_text_field.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_masked_text2/flutter_masked_text2.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';

class JourneyClinicScreen extends StatefulWidget {
  const JourneyClinicScreen({super.key});

  @override
  State<JourneyClinicScreen> createState() => _JourneyClinicScreenState();
}

class _JourneyClinicScreenState extends State<JourneyClinicScreen> {
  final MaskedTextController _controllerSearch =
      MaskedTextController(mask: '000.000.000-00');

  late Buttons _configHomeButton;
  List<Widget> listButton = [];

  ResBrazilBeneficiaryCubit? _resBrazilBeneficiaryCubit;

  void _searchBeneficiary({required String cpf}) {
    String cleanedCpf = cpf.replaceAll(RegExp(r'\D'), '');
    if (cleanedCpf.length == 11) {
      FocusScope.of(context).unfocus();
      context.read<ResBrazilBeneficiaryCubit>().resBrazilBeneficiaryByCpf(
            crm: context.read<AuthCubit>().credentials.crm,
            cpf: cleanedCpf,
          );
    } else if (cpf.isEmpty) {
      context.read<ResBrazilBeneficiaryCubit>().resBrazilBeneficiaryByCpf(
            crm: context.read<AuthCubit>().credentials.crm,
            cpf: '',
          );
    }
  }

  @override
  void initState() {
    _configHomeButton =
        context.read<AuthCubit>().modelGeneralConfigModel.home.buttons;

    context.read<ResConfigCubit>().getResConfigs(
        crm: context.read<AuthCubit>().credentials.crm,
        cpf: _controllerSearch.text);

    super.initState();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    _resBrazilBeneficiaryCubit = context.read<ResBrazilBeneficiaryCubit>();
  }

  @override
  void dispose() {
    _resBrazilBeneficiaryCubit?.resetState();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(
          'Histórico de Saúde (RES)',
          style: TextStyle(fontSize: 14),
        ),
        centerTitle: true,
      ),
      backgroundColor: CooperadoColors.backgroundWhiteColor,
      body: Padding(
        padding: const EdgeInsets.only(left: 20, right: 20, top: 24),
        child: Column(
          children: [
            SearchTextField(
              controller: _controllerSearch,
              onChanged: (cpf) {
                _searchBeneficiary(cpf: cpf);
              },
              onClearPressed: () {
                FocusScope.of(context).unfocus();
                _controllerSearch.clear();
                _searchBeneficiary(cpf: '');
              },
              hintText: 'Pesquisar paciente por CPF',
            ),
            BlocBuilder<ResBrazilBeneficiaryCubit, ResBrasilBeneficiaryState>(
              builder: (context, state) {
                if (state is LoadingResBrasilBeneficiaryState) {
                  return Center(
                    child: Padding(
                      padding: EdgeInsets.only(
                        bottom: 24,
                        top: MediaQuery.of(context).size.height * 0.2,
                      ),
                      child: const Column(
                        mainAxisAlignment: MainAxisAlignment.start,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          SpinKitCircle(color: CooperadoColors.tealGreen),
                        ],
                      ),
                    ),
                  );
                } else if (state is ErrorResBrasilBeneficiaryState) {
                  return Center(
                    child: Padding(
                      padding: EdgeInsets.only(
                        bottom: 24,
                        top: MediaQuery.of(context).size.height * 0.2,
                      ),
                      child: ErroService(
                        message: state.message,
                        onPressed: () {
                          context
                              .read<ResBrazilBeneficiaryCubit>()
                              .resBrazilBeneficiaryByCpf(
                                crm: context.read<AuthCubit>().credentials.crm,
                                cpf: _controllerSearch.text,
                              );
                        },
                      ),
                    ),
                  );
                } else if (state is LoadedResBrasilBeneficiaryState) {
                  return Expanded(
                    child: Padding(
                      padding: const EdgeInsets.only(top: 24),
                      child: Column(
                        children: [
                          Padding(
                            padding: const EdgeInsets.only(bottom: 15),
                            child: PatientCard(
                              name: state.resBrazilBeneficiaryModel
                                              .socialName !=
                                          null &&
                                      state.resBrazilBeneficiaryModel
                                          .socialName!.isNotEmpty
                                  ? state.resBrazilBeneficiaryModel.socialName!
                                  : state.resBrazilBeneficiaryModel.name,
                              cpf: state.resBrazilBeneficiaryModel.cpf,
                            ),
                          ),
                          Expanded(
                            child: GridView.count(
                              crossAxisCount: 2,
                              childAspectRatio: 1.4,
                              crossAxisSpacing: 8,
                              mainAxisSpacing: 8,
                              children: [
                                ..._createListButton(
                                  context: context,
                                  beneficiaryModel:
                                      state.resBrazilBeneficiaryModel,
                                ).map((item) {
                                  return item;
                                }),
                              ],
                            ),
                          ),
                          const SizedBox(height: 10),
                        ],
                      ),
                    ),
                  );
                }

                return const EmptyList(
                  pathIcon: 'assets/svg/icon_search.svg',
                  message: 'Nenhum paciente listado no momento',
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  List<Widget> _createListButton({
    required BuildContext context,
    required ResBrazilBeneficiaryModel beneficiaryModel,
  }) {
    listButton.isNotEmpty ? listButton.clear() : listButton;
    String nameBeneficiary = beneficiaryModel.socialName != null &&
            beneficiaryModel.socialName!.isNotEmpty
        ? beneficiaryModel.socialName!
        : beneficiaryModel.name;

    String card = beneficiaryModel.card;

    if (_configHomeButton.res.resExternal.resExternalService &&
        beneficiaryModel.resExternalService) {
      listButton.add(
        CardResIconButton(
          pathIcon: 'assets/svg/icon_service.svg',
          title: "Atendimentos",
          onClick: () {
            FocusScope.of(context).requestFocus(FocusNode());
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => ResServiceScreen(
                  nameBeneficiary: nameBeneficiary,
                  crm: context.read<AuthCubit>().credentials.crm,
                  card: card,
                ),
              ),
            );
          },
        ),
      );
    }

    if (_configHomeButton.res.resExternal.resExternalResultExam) {
      listButton.add(
        CardResIconButton(
          pathIcon: 'assets/svg/icon_exam_result.svg',
          title: "Resultados de Exames",
          onClick: () {
            FocusScope.of(context).requestFocus(FocusNode());
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => ResExameResultScreen(
                  crm: context.read<AuthCubit>().credentials.crm,
                  card: card,
                  nameBeneficiary: nameBeneficiary,
                ),
              ),
            );
          },
        ),
      );
    }

    if (_configHomeButton.res.resExternal.resExternalDiagnostic &&
        beneficiaryModel.resExternalDiagnostic) {
      listButton.add(
        CardResIconButton(
          pathIcon: 'assets/svg/icon_diagnostico.svg',
          title: "Diagnósticos",
          onClick: () {
            FocusScope.of(context).requestFocus(FocusNode());
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => ResBrazilDiagnosticoScreen(
                  nameBeneficiary: nameBeneficiary,
                  crm: context.read<AuthCubit>().credentials.crm,
                  card: card,
                  listDiagnosticoResult: const [],
                ),
              ),
            );
          },
        ),
      );
    }
    if (_configHomeButton.res.resExternal.resExternalAllergy &&
        beneficiaryModel.resExternalAllergy) {
      listButton.add(
        CardResIconButton(
          pathIcon: 'assets/svg/icon_allergy.svg',
          title: "Alergias",
          onClick: () {
            FocusScope.of(context).requestFocus(FocusNode());
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => ResInternalAllergiesScreen(
                  nameBeneficiary: nameBeneficiary,
                  beneficiaryCard: card,
                ),
              ),
            );
          },
        ),
      );
    }
    if (_configHomeButton.res.resExternal.resExternalDocument &&
        beneficiaryModel.resExternalDocument) {
      listButton.add(
        CardResIconButton(
          pathIcon: 'assets/svg/icon_procedure.svg',
          title: "Documentos",
          onClick: () {
            FocusScope.of(context).requestFocus(FocusNode());
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => ResInternalDocumentsScreen(
                  nameBeneficiary: nameBeneficiary,
                  beneficiaryCard: card,
                ),
              ),
            );
          },
        ),
      );
    }

    if (_configHomeButton.res.resExternal.resExternalProcedure &&
        beneficiaryModel.resExternalProcedure) {
      listButton.add(
        CardResIconButton(
          pathIcon: 'assets/svg/icon_procedure.svg',
          title: "Procedimentos",
          onClick: () {
            FocusScope.of(context).requestFocus(FocusNode());
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => ResInternalProceduresScreen(
                  nameBeneficiary: nameBeneficiary,
                  beneficiaryCard: card,
                ),
              ),
            );
          },
        ),
      );
    }

    if (_configHomeButton.res.resExternal.resExternalAlerts) {
      listButton.add(
        CardResIconButton(
          pathIcon: 'assets/svg/icon_alert.svg',
          title: "Alertas",
          onClick: () {
            FocusScope.of(context).requestFocus(FocusNode());
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => ResAlertsScreen(
                  nameBeneficiary: nameBeneficiary,
                  beneficiaryCard: card,
                ),
              ),
            );
          },
        ),
      );
    }
    //if (_configHomeButton.res.resExternal.resExternalIndicators) {
    listButton.add(
      CardResIconButton(
        pathIcon: 'assets/svg/icon_indicators.svg',
        title: "Indicadores",
        onClick: () {
          context.read<ResConfigCubit>().getResConfigs(
              crm: context.read<AuthCubit>().credentials.crm,
              cpf: _controllerSearch.text);
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => ResIndicatorsScreen(
                nameBeneficiary: nameBeneficiary,
                cpfBeneficiary: beneficiaryModel.cpf,
              ),
            ),
          );
        },
      ),
    );
    //}

    return listButton;
  }
}
