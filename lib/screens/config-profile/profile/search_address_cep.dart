import 'package:biometria_perfilapps/main.dart';
import 'package:cooperado_minha_unimed/bloc/auth/auth_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/config-profile/config_profile_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/profile/profile_cubit.dart';
import 'package:cooperado_minha_unimed/colors.dart';
import 'package:cooperado_minha_unimed/models/address.model.dart';
import 'package:cooperado_minha_unimed/models/user.model.dart';
import 'package:cooperado_minha_unimed/models/zipcode.model.dart';
import 'package:cooperado_minha_unimed/screens/config-profile/profile/complement_address.dart';
import 'package:cooperado_minha_unimed/shared/flavor_config.dart';
import 'package:cooperado_minha_unimed/shared/utils/biometry_utils.dart';
import 'package:cooperado_minha_unimed/shared/utils/logger_print.dart';
import 'package:cooperado_minha_unimed/shared/vo/profile/profile-payload.vo.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:mask_text_input_formatter/mask_text_input_formatter.dart';

class SearchAddressByCEP extends StatefulWidget {
  final Enderecos? address;
  final FirebaseAnalytics analytics;
  final FirebaseAnalyticsObserver observer;

  const SearchAddressByCEP({
    super.key,
    this.address,
    required this.analytics,
    required this.observer,
  });

  @override
  SearchAddressByCEPState createState() => SearchAddressByCEPState();
}

class SearchAddressByCEPState extends State<SearchAddressByCEP> {
  TextEditingController zipcodeController = TextEditingController();
  TextEditingController numberController = TextEditingController();
  TextEditingController complementController = TextEditingController();

  AddressZipCodeModel? selectedAddress;
  List<ContactModel> contacts = [];
  FocusNode? focusTextFieldSearch;

  final _formKey = GlobalKey<FormState>();
  final zipcodeFormatter = MaskTextInputFormatter(
    mask: '##.###-###',
    filter: {"#": RegExp(r'[0-9]')},
  );

  bool validadeCEP = false;
  String? beforeValue;

  @override
  void initState() {
    context.read<ConfigProfileCubit>().setInitial();
    focusTextFieldSearch = FocusNode();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: MediaQuery.of(context).size.width * 0.8,
      child: Form(
        key: _formKey,
        child: ListView(
          shrinkWrap: true,
          children: [
            const Padding(
              padding: EdgeInsets.only(bottom: 10.0),
              child: Center(
                child: Text(
                  'Novo endereço',
                  style: TextStyle(fontWeight: FontWeight.bold, fontSize: 20),
                ),
              ),
            ),
            if (selectedAddress == null)
              _searchForm()
            else
              ComplementAddress(
                currentAddress: widget.address,
                selectedAddress: selectedAddress,
                analytics: widget.analytics,
                observer: widget.observer,
                cep: zipcodeController.text,
              ),
          ],
        ),
      ),
    );
  }

  Widget _indicatorStateSearchCEP() {
    return BlocBuilder<ConfigProfileCubit, ConfigProfileState>(
      builder: (context, state) {
        if (state is LoadingAddressesByZipcodeState) {
          return const SpinKitThreeBounce(
              color: CooperadoColors.tealGreen, size: 30);
        } else if (state is ErrorLoadAddressByZipcodeState) {
          return Center(
            child: Text(
              state.message,
              style: const TextStyle(color: Colors.red, fontSize: 12),
            ),
          );
        } else if (state is LoadedZipcodeAddressesState) {
          return Padding(
              padding: const EdgeInsets.only(top: 15.0),
              child: _addressesResult());
        } else {
          return Container();
        }
      },
    );
  }

  Widget _searchForm() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const SizedBox(height: 15),
        Container(
          decoration: BoxDecoration(
              borderRadius: const BorderRadius.all(Radius.circular(10)),
              border: Border.all()),
          child: Row(
            children: [
              Expanded(
                child: Padding(
                  padding: const EdgeInsets.only(left: 8, top: 15, bottom: 15),
                  child: TextField(
                    autofocus: true,
                    focusNode: focusTextFieldSearch,
                    decoration: const InputDecoration.collapsed(
                        hintText: 'Digite o CEP'),
                    keyboardType: TextInputType.number,
                    controller: zipcodeController,
                    inputFormatters: [zipcodeFormatter],
                    onChanged: (currentValue) {
                      if (beforeValue == currentValue) return;

                      setState(() {
                        validadeCEP = currentValue.length == 10 ? true : false;
                      });
                      if (validadeCEP) _onConfirm();
                    },
                  ),
                ),
              ),
              const Padding(
                padding: EdgeInsets.all(8.0),
                child: Icon(Icons.search),
              ),
            ],
          ),
        ),
        const SizedBox(height: 15),
        _indicatorStateSearchCEP(),
        const SizedBox(height: 15),
        Padding(
          padding: const EdgeInsets.only(top: 15.0),
          child: Row(
            children: [
              Expanded(
                  child: ElevatedButton(
                style: ElevatedButton.styleFrom(
                    backgroundColor: CooperadoColors.tealGreenSecondary),
                child: const Text('Fechar'),
                onPressed: () => Navigator.pop(context),
              )),
              const SizedBox(width: 10.0),
            ],
          ),
        ),
      ],
    );
  }

  _onConfirm() {
    if (!_formKey.currentState!.validate()) return;

    if (selectedAddress == null && validadeCEP) {
      context
          .read<ConfigProfileCubit>()
          .searchAddressesByZipcode(zipcodeController.text);
      beforeValue = zipcodeController.text;
    } else {
      final User user = context.read<ProfileCubit>().user;
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => BiometryUnimed(
            perfilAppsCredentials: BiometriaUtils.convertDefaultCredentials(
              FlavorConfig.instance!.values.profilePermissions,
            ),
            codPrestador: user.codPrestador,
            name: user.nome ?? '',
            environment: FlavorConfig.instance!.values.validadeBioIdEnv,
            maxHeight: 720,
            maxQuality: 60,
            cameraPreview: true,
            theme: ThemeData(
              useMaterial3: false,
              primaryColor: cooperadoTealGreen,
              colorScheme: ColorScheme.fromSwatch().copyWith(
                secondary: CooperadoColors.grayDark2,
              ),
              progressIndicatorTheme: const ProgressIndicatorThemeData(
                color: cooperadoTealGreen,
              ),
              elevatedButtonTheme: ElevatedButtonThemeData(
                style: ElevatedButton.styleFrom(
                  backgroundColor: cooperadoTealGreen,
                  foregroundColor: Colors.white,
                ),
              ),
              fontFamily: 'UnimedSans',
              primaryIconTheme: const IconThemeData(color: cooperadoTealGreen),
            ),
            onValid: () {
              Navigator.of(context).pop();

              final crm = context.read<AuthCubit>().credentials.crm;
              final cep = zipcodeController.text
                  .replaceAll('.', '')
                  .replaceAll('-', '');
              final newAdders = Enderecos.fromJson(widget.address!.toJson());
              newAdders.cep = cep;
              newAdders.nomeLogradouro = selectedAddress!.nomeLogradouro;
              newAdders.codLogradouro = selectedAddress!.codLogradouro;
              newAdders.nomeBairro = selectedAddress!.nomeBairro;
              newAdders.nomeCidade = selectedAddress!.nomeCidade;
              newAdders.numEndereco = numberController.text;
              newAdders.complEndereco = complementController.text;
              context
                  .read<ConfigProfileCubit>()
                  .updateEndereco(crm: crm, endereco: newAdders);
            },
            onCancel: () {
              Navigator.of(context).pop();
            },
            logger: BiometryLogger.biometryLogger,
          ),
        ),
      );
    }
  }

  Widget _addressTile(
      bool showDivider, AddressZipCodeModel address, String zipCode) {
    return Material(
      child: InkWell(
        onTap: () {
          setState(() {
            selectedAddress = address;
          });
        },
        child: Ink(
          padding: const EdgeInsets.only(bottom: 10.0),
          child: Column(
            children: [
              Row(
                children: [
                  const Padding(
                    padding: EdgeInsets.only(right: 7.0),
                    child: Icon(Icons.place),
                  ),
                  Flexible(
                    child: Text(
                      _formattedAddress(address, zipCode),
                      style: const TextStyle(fontSize: 11),
                    ),
                  ),
                ],
              ),
              if (showDivider)
                const Padding(
                  padding: EdgeInsets.only(top: 8.0),
                  child: Divider(
                    thickness: 0.5,
                    color: CooperadoColors.grayDark,
                  ),
                )
            ],
          ),
        ),
      ),
    );
  }

  String _formattedAddress(AddressZipCodeModel address, String zipcode) {
    return '${address.nomeLogradouro}, ${address.nomeBairro!}, $zipcode, ${address.nomeCidade}-${address.nomeUf}.';
  }

  Widget _addressesResult() {
    return BlocBuilder<ConfigProfileCubit, ConfigProfileState>(
      builder: (context, state) {
        if (state is LoadedZipcodeAddressesState) {
          return Column(
            children: state.zipcodeResult.adressZipCodeModel!
                .asMap()
                .entries
                .map((address) => _addressTile(
                    address.key <
                        state.zipcodeResult.adressZipCodeModel!.length - 1,
                    address.value,
                    address.value.cep.toString()))
                .toList(),
          );
        }
        return Container();
      },
    );
  }
}
