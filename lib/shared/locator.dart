import 'dart:io';

import 'package:cooperado_minha_unimed/objectbox.g.dart';
import 'package:cooperado_minha_unimed/shared/api/auth.api.dart';
import 'package:cooperado_minha_unimed/shared/api/beneficiary.api.dart';
import 'package:cooperado_minha_unimed/shared/api/channel_ethics.api.dart';
import 'package:cooperado_minha_unimed/shared/api/club_more_benefits_graphql.api.dart';
import 'package:cooperado_minha_unimed/shared/api/config_profile.api.dart';
import 'package:cooperado_minha_unimed/shared/api/diretoria.api.dart';
import 'package:cooperado_minha_unimed/shared/api/economic_indicators.api.dart';
import 'package:cooperado_minha_unimed/shared/api/financial.api.dart';
import 'package:cooperado_minha_unimed/shared/api/fiscal_council.api.dart';
import 'package:cooperado_minha_unimed/shared/api/general_config.api.dart';
import 'package:cooperado_minha_unimed/shared/api/graphql-res-internal.api.dart';
import 'package:cooperado_minha_unimed/shared/api/graphql-res.api.dart';
import 'package:cooperado_minha_unimed/shared/api/graphqlecard.api.dart';
import 'package:cooperado_minha_unimed/shared/api/indicadores.api.dart';
import 'package:cooperado_minha_unimed/shared/api/notice.api.dart';
import 'package:cooperado_minha_unimed/shared/api/notificacao-graphql.api.dart';
import 'package:cooperado_minha_unimed/shared/api/office_notices.api.dart';
import 'package:cooperado_minha_unimed/shared/api/pdf.api.dart';
import 'package:cooperado_minha_unimed/shared/api/redirect_buttons.api.dart';
import 'package:cooperado_minha_unimed/shared/api/score.api.dart';
import 'package:cooperado_minha_unimed/shared/api/services.api.dart';
import 'package:cooperado_minha_unimed/shared/api/support_cooperative.api.dart';
import 'package:cooperado_minha_unimed/shared/api/terms.api.dart';
import 'package:cooperado_minha_unimed/shared/api/transparencia.api.dart';
import 'package:cooperado_minha_unimed/shared/flavor_config.dart';
import 'package:cooperado_minha_unimed/shared/services/geolocation.service.dart';
import 'package:cooperado_minha_unimed/shared/services/version.service.dart';
import 'package:cooperado_minha_unimed/shared/utils/logger_print.dart';
import 'package:cooperado_minha_unimed/shared/widgets/comparative-production/service/comparative_production_service.dart';
import 'package:get_it/get_it.dart';
import 'package:http_client/http_client.dart';
import 'package:remote_log_elastic/remote_log_elastic.dart';

class Locator {
  static GetIt? _i;
  static GetIt? get instance => _i;

  Locator.setup() {
    _i = GetIt.I;
    _i!.registerSingleton<RemoteLog>(
      RemoteLog(
          environment: FlavorConfig.instance!.values.remoteLogEnv,
          username: 'cooperados-app',
          password: '6Z8VeyR3SZpnGxy8GEL8fu5Sf',
          disableUpload: false,
          // disableUpload: FlavorConfig.instance.values.remoteLogEnv !=
          //     RemoteLogEnv.PROD, // Enable remote_log only production
          showLogs: false),
    );
    _i!.registerSingleton<UnimedHttpClient>(UnimedHttpClient<UnimedLogger>(
      defaultHeaders: {"Content-Type": "application/json"},
      logger: UnimedLogger(),
    ));

    _i!.registerSingleton<VersionService>(VersionService());

    _i!.registerSingleton<AuthApi>(
      AuthApi(
        _i!.get<UnimedHttpClient>(),
      ),
    );

    _i!.registerLazySingleton<TransparenciaApi>(
      () => TransparenciaApi(_i!.get<UnimedHttpClient>()),
    );

    _i!.registerLazySingleton<GeolocationService>(
      () => GeolocationService(),
    );

    _i!.registerLazySingleton<ComparativeProductionService>(
      () => ComparativeProductionService(_i!.get<UnimedHttpClient>()),
    );

    _i!.registerLazySingleton<DiretoriaApi>(
      () => DiretoriaApi(_i!.get<UnimedHttpClient>()),
    );

    _i!.registerLazySingleton<IndicadoresApi>(
      () => IndicadoresApi(_i!.get<UnimedHttpClient>()),
    );
    _i!.registerLazySingleton<FiscalCouncilApi>(
      () => FiscalCouncilApi(_i!.get<UnimedHttpClient>()),
    );
    _i!.registerLazySingleton<NoticeApi>(
      () => NoticeApi(_i!.get<UnimedHttpClient>()),
    );
    _i!.registerLazySingleton<PdfApi>(
      () => PdfApi(_i!.get<UnimedHttpClient>()),
    );

    _i!.registerLazySingleton<TermsApi>(
      () => TermsApi(_i!.get<UnimedHttpClient>()),
    );

    _i!.registerLazySingleton<ServicesApi>(
      () => ServicesApi(_i!.get<UnimedHttpClient>()),
    );
    _i!.registerLazySingleton<ScoreApi>(
      () => ScoreApi(_i!.get<UnimedHttpClient>()),
    );
    _i!.registerFactory<ConfigProfileApi>(
      () => ConfigProfileApi(
        _i!.get<UnimedHttpClient>(),
      ),
    );
    _i!.registerFactory<GeneralConfigsApi>(
      () => GeneralConfigsApi(
        _i!.get<UnimedHttpClient>(),
      ),
    );

    _i!.registerLazySingleton<RedirectButtonsApi>(
      () => RedirectButtonsApi(_i!.get<UnimedHttpClient>()),
    );
    _i!.registerLazySingleton<EconomicIndicatorsApi>(
      () => EconomicIndicatorsApi(_i!.get<UnimedHttpClient>()),
    );

    _i!.registerLazySingleton<FinancialApi>(
      () => FinancialApi(_i!.get<UnimedHttpClient>()),
    );

    _i!.registerLazySingleton<BeneficiaryApi>(
      () => BeneficiaryApi(_i!.get<UnimedHttpClient>()),
    );

    _i!.registerLazySingleton<ChannelEthicsApi>(
      () => ChannelEthicsApi(_i!.get<UnimedHttpClient>()),
    );

    _i!.registerLazySingleton<GraphQlApiInternal>(
      () => GraphQlApiInternal(_i!.get<UnimedHttpClient>()),
    );

    _i!.registerLazySingleton<ClubMoreBenefitsGraphQlApi>(
      () => ClubMoreBenefitsGraphQlApi(_i!.get<UnimedHttpClient>()),
    );

    _i!.registerLazySingleton<GraphQlApiEcard>(
      () => GraphQlApiEcard(_i!.get<UnimedHttpClient>()),
    );

    _i!.registerLazySingleton<GraphQlApiNotificacao>(
      () => GraphQlApiNotificacao(_i!.get<UnimedHttpClient>()),
    );

    _i!.registerLazySingleton<ResGraphQlApi>(
      () => ResGraphQlApi(_i!.get<UnimedHttpClient>()),
    );

    _i!.registerLazySingleton<OfficeNoticesApi>(
      () => OfficeNoticesApi(_i!.get<UnimedHttpClient>()),
    );

    _i!.registerLazySingleton<SupportCooperativeApi>(
      () => SupportCooperativeApi(_i!.get<UnimedHttpClient>()),
    );
  }

  Locator.initializeStore({required Directory directory}) {
    if (!_i!.isRegistered<Store>()) {
      _i!.registerLazySingleton<Store>(
        () => Store(getObjectBoxModel(),
            directory:
                '${directory.path}/medicine-db${FlavorConfig.isDevelopment() ? '-dev' : ''}'),
      );
    }
  }
}
