// ignore_for_file: public_member_api_docs, sort_constructors_first

import 'package:cooperado_minha_unimed/bloc/sensitive-data/sensitive_data_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/sensitive-data/sensitive_data_state.dart';
import 'package:cooperado_minha_unimed/colors.dart';
import 'package:cooperado_minha_unimed/shared/widgets/graphics/bar_data.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class CostBarGraph extends StatelessWidget {
  const CostBarGraph({
    super.key,
    required this.maxY,
    required this.data,
  });
  final double maxY;
  final dynamic data; // sunAmount

  Widget getBottomTiles(double value, TitleMeta meta) {
    Widget text;
    switch (value.toInt()) {
      case 0:
        text = const Text("M.E");
        break;
      case 1:
        text = const Text("P");
        break;

      default:
        text = const Text('');
    }
    return SideTitleWidget(axisSide: meta.axisSide, child: text);
  }

  double nextThousand(double number) {
    double nextThousand = ((number / 10000).ceil()) * 10000;
    return nextThousand;
  }

  @override
  Widget build(BuildContext context) {
    BarData myBarData = BarData(
      meAmount: data[0].valor ?? 0.0,
      prodAmount: data[1].valor ?? 0.0,
    );
    myBarData.initializeBarData();
    double formattedMaxY = nextThousand(maxY);

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 3),
      child: BlocBuilder<SensitiveDataCubit, SensitiveDataState>(
  builder: (context, sensitiveState) {
    bool isSensitiveDataVisible = !sensitiveState.isSensitiveDataVisible;
    return BarChart(
      BarChartData(
        barTouchData: BarTouchData(
          allowTouchBarBackDraw: false,
          touchTooltipData: BarTouchTooltipData(
            tooltipBgColor: Colors.blueGrey,
            tooltipHorizontalAlignment: FLHorizontalAlignment.right,
            tooltipMargin: -10,
            getTooltipItem: (group, groupIndex, rod, rodIndex) {
              return null;
              //return BarTooltipItem(rod.toY.toString(), TextStyle());
            },
          ),
        ),
        maxY: formattedMaxY,
        minY: 0,
        gridData: const FlGridData(show: false, drawVerticalLine: false),
        titlesData: FlTitlesData(
          show: true,
          topTitles: const AxisTitles(
            sideTitles: SideTitles(showTitles: false),
          ),
          rightTitles: const AxisTitles(
            sideTitles: SideTitles(showTitles: false),
          ),
          leftTitles: AxisTitles(
            sideTitles: SideTitles(
              interval: formattedMaxY < 1 ? 1 : formattedMaxY,
              reservedSize: 60,
              showTitles: !isSensitiveDataVisible,
            ),
          ),
          bottomTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              getTitlesWidget: getBottomTiles,
            ),
          ),
        ),
        borderData: FlBorderData(
          show: true,
          border: const Border(
            top: BorderSide(color: Colors.grey),
            bottom: BorderSide(color: Colors.grey),
          ),
        ),
        barGroups: myBarData.barData
            .map(
              (individualbar) => BarChartGroupData(
                x: individualbar.x, //individualbar.x,

                barRods: individualbar.x == 0
                    ? [
                        BarChartRodData(
                          toY: individualbar.y,
                          color: CooperadoColors.paleGreenish,
                          width: 30,
                          borderRadius: const BorderRadius.only(
                            topLeft: Radius.circular(3),
                            topRight: Radius.circular(3),
                          ),
                        ),
                      ]
                    : [
                        BarChartRodData(
                          toY: individualbar.y,
                          color: CooperadoColors.limaColor,
                          width: 30,
                          borderRadius: const BorderRadius.only(
                            topLeft: Radius.circular(3),
                            topRight: Radius.circular(3),
                          ),
                        ),
                      ],
              ),
            )
            .toList(),
      ),
    );
  },
),
    );
  }
}
