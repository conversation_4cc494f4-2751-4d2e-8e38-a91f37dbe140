import 'package:auto_size_text/auto_size_text.dart';
import 'package:cooperado_minha_unimed/bloc/economic-indicators/economic_indicators_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/sensitive-data/sensitive_data_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/sensitive-data/sensitive_data_state.dart';
import 'package:cooperado_minha_unimed/colors.dart';
import 'package:cooperado_minha_unimed/models/economic_indicators.model.dart';
import 'package:cooperado_minha_unimed/shared/widgets/error-banner/error_banner.component.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:two_dimensional_scrollables/two_dimensional_scrollables.dart';
import 'package:unimed_select/unimed-select.dart';

class EconomicIndicatorsScreen extends StatefulWidget {
  final FirebaseAnalytics analytics;
  final FirebaseAnalyticsObserver observer;

  const EconomicIndicatorsScreen(
      {super.key, required this.analytics, required this.observer});

  @override
  State<EconomicIndicatorsScreen> createState() =>
      _EconomicIndicatorsScreenState();
}

class _EconomicIndicatorsScreenState extends State<EconomicIndicatorsScreen> {
  //late String tempDate;

  List<String> meses = [
    "Indicador",
    "Objetivo",
    "Formula",
    "Janeiro",
    "Fevereiro",
    "Março",
    "Abril",
    "Maio",
    "Junho",
    "Julho",
    "Agosto",
    "Setembro",
    "Outubro",
    "Novembro",
    "Dezembro",
  ];

  final ScrollController _verticalController = ScrollController();
  late int _rowCount;
  late List<EconomicIndicatorsModel> listEconomic;
  int columnCount = 15;
  late String selectedYear;
  List<String> suIndicators = [];

  TextEditingController controller = TextEditingController();

  List<EconomicIndicatorsModel> _selectByYear(
      String year, List<EconomicIndicatorsModel> economicIndicators) {
    return economicIndicators.where((item) => item.nuAno == year).toList();
  }

  _initFields(String year, List<EconomicIndicatorsModel> economicIndicators) {
    listEconomic = _selectByYear(selectedYear, economicIndicators);

    suIndicators = _selectIndicators(listEconomic);
    _rowCount = suIndicators.length + 1;
  }

  List<String> _selectIndicators(List<EconomicIndicatorsModel> list) {
    List<String> values = [];
    for (EconomicIndicatorsModel economicindicator in list) {
      if (!values.contains(economicindicator.dsIndicador)) {
        values.add(economicindicator.dsIndicador.toString());
      }
    }
    return values;
  }

  _updateTable(
      String ano, List<EconomicIndicatorsModel> economicIndicators) async {
    listEconomic = _selectByYear(ano, economicIndicators);
    suIndicators = _selectIndicators(listEconomic);
    _rowCount = suIndicators.length + 1;
    setState(() {});
  }

  @override
  void initState() {
    super.initState();
    _enableRotation();
    selectedYear = DateTime.now().year.toString();
    controller.text = selectedYear.toString();
    context.read<EconomicIndicatorsCubit>().getEconomicIndicators(selectedYear);
    widget.analytics.logScreenView(
      screenName: 'Indicadores Econômicos e Financeiros',
      screenClass: 'EconomicIndicatorsScreen',
    );
  }

  @override
  void dispose() {
    _disableRotation();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: CooperadoColors.backgroundColor,
      appBar: AppBar(
        title: const AutoSizeText(
          "Indicadores Econômicos e Financeiros",
          minFontSize: 10,
        ),
        actions: [
             Padding(
             padding: const EdgeInsets.only(right: 15.0),
             child: IconButton(
                       icon: BlocBuilder<SensitiveDataCubit, SensitiveDataState>(
              builder: (context, state) {
                return Icon(
                  state.isSensitiveDataVisible ? Icons.visibility : Icons.visibility_off,
                );
              },
                       ),
                       onPressed: () {
              context.read<SensitiveDataCubit>().toggleSensitiveDataVisibility();
                       },
                     ),
           ),
        ],
      ),
      body: RefreshIndicator(
        onRefresh: () async {
          context
              .read<EconomicIndicatorsCubit>()
              .getEconomicIndicators(selectedYear);
        },
        color: CooperadoColors.tealGreen,
        child: BlocBuilder<EconomicIndicatorsCubit, EconomicIndicatorsState>(
          builder: (context, state) {
            if (state is LoadingEconomicIndicatorsState) {
              return const SpinKitCircle(
                color: CooperadoColors.tealGreen,
              );
            } else if (state is ErrorEconomicIndicatorsState) {
              return Padding(
                padding: const EdgeInsets.all(8.0),
                child: Padding(
                  padding: const EdgeInsets.symmetric(vertical: 10),
                  child: SizedBox(
                    height: MediaQuery.of(context).size.height,
                    child: ListView(
                      children: [
                        _selectYear((String? item) {
                          selectedYear = item.toString();
                          controller.text = selectedYear.toString();
                          context
                              .read<EconomicIndicatorsCubit>()
                              .getEconomicIndicators(item ?? selectedYear);
                        }),
                        Center(
                            child: ErrorBanner(
                          message: state.message,
                          backgroundColor: Colors.transparent,
                        )),
                      ],
                    ),
                  ),
                ),
              );
            } else if (state is LoadedEconomicIndicatorsState) {
              _initFields(selectedYear, state.economicIndicators);
              if (suIndicators.isNotEmpty && _rowCount > 1) {
                return Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: Padding(
                    padding: const EdgeInsets.symmetric(vertical: 10),
                    child: SizedBox(
                      height: MediaQuery.of(context).size.height,
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          _selectYear((String? item) {
                            selectedYear = item.toString();
                            controller.text = selectedYear.toString();
                            context
                                .read<EconomicIndicatorsCubit>()
                                .getEconomicIndicators(selectedYear);
                            Future.delayed(const Duration(milliseconds: 500),
                                () async {
                              await _updateTable(
                                  selectedYear, state.economicIndicators);
                            });
                          }),
                          Expanded(
                            child: TableView.builder(
                              verticalDetails: ScrollableDetails.vertical(
                                  controller: _verticalController),
                              cellBuilder: _buildCell,
                              columnCount: columnCount,
                              columnBuilder: _buildColumnSpan,
                              rowCount: _rowCount,
                              rowBuilder: _buildRowSpan,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                );
              } else {
                return const Center(
                  child: SizedBox(
                      height: 100,
                      width: 100,
                      child: CircularProgressIndicator()),
                );
              }
            }
            return const Center();
          },
        ),
      ),
    );
  }

  Widget _selectYear(Function(String? item) selectItem) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const SizedBox(
          height: 30,
        ),
        UnimedSelect<String?>(
          showClearButton: false,
          title: 'Escolha o ano',
          controller: controller,
          items: List.generate(5, (index) => '${DateTime.now().year - index}')
              .map(
                (entry) => UnimedSelectItemModel(
                  value: entry,
                  label: entry,
                ),
              )
              .toList(),
          onSelect: selectItem,
        ),
        const SizedBox(
          height: 16,
        )
      ],
    );
  }

  Widget _buildCell(BuildContext context, TableVicinity vicinity) {
    const textStyle = TextStyle(
      color: CooperadoColors.blackText,
      fontSize: 16,
      fontWeight: FontWeight.bold,
    );
    //cria o cabeçalho da tabela
    if (vicinity.column == 0 && vicinity.row == 0) {
      return Center(
        child: Text(
          meses[vicinity.column],
          style: textStyle,
        ),
      );
    } else if (vicinity.column == 1 && vicinity.row == 0) {
      return Center(
        child: Text(
          meses[vicinity.column],
          style: textStyle,
        ),
      );
    } else if (vicinity.column == 2 && vicinity.row == 0) {
      return Center(
        child: Text(
          meses[vicinity.column],
          style: textStyle,
        ),
      );
    } else if (vicinity.row == 0) {
      return Center(
        child: Text(
          meses[vicinity.column].substring(0, 3),
          style: textStyle,
        ),
      );
      //cria a coluna de indicadores
    } else if (vicinity.column == 0 &&
        vicinity.row > 0 &&
        vicinity.row <= suIndicators.length) {
      return Center(
        child: Text(suIndicators[vicinity.row - 1]),
      );

      //cria coluna de objetivos
    } else if (vicinity.column == 1 &&
        vicinity.row > 0 &&
        vicinity.row <= suIndicators.length) {
      return BlocBuilder<SensitiveDataCubit, SensitiveDataState>(
  builder: (context, sensitiveState) {
    bool isSensitiveDataVisible = !sensitiveState.isSensitiveDataVisible;
    String displayValue = isSensitiveDataVisible
        ? '***'
        : listEconomic[vicinity.row - 1].objetivo.toString();

    return Center(
      child: Text(displayValue),
    );
  },
);

      //cria coluna de formulas
    } else if (vicinity.column == 2 &&
        vicinity.row > 0 &&
        vicinity.row <= suIndicators.length) {
      return BlocBuilder<SensitiveDataCubit, SensitiveDataState>(
  builder: (context, sensitiveState) {
    bool isSensitiveDataVisible = !sensitiveState.isSensitiveDataVisible;
    String displayValue = isSensitiveDataVisible
        ? '**'
        : listEconomic[vicinity.row - 1].formula.toString();

    return Center(
      child: Text(displayValue),
    );
  },
);

      //preenche as colunas de meses
    } else if (vicinity.column > 2 &&
        vicinity.row > 0 &&
        vicinity.row <= suIndicators.length) {
  return BlocBuilder<SensitiveDataCubit, SensitiveDataState>(
    builder: (context, sensitiveState) {
      bool isSensitiveDataVisible = !sensitiveState.isSensitiveDataVisible;
      String displayValue;

      switch (vicinity.column) {
        case 3:
          displayValue = isSensitiveDataVisible
              ? '***'
              : listEconomic[vicinity.row - 1].jan.toString();
          break;
        case 4:
          displayValue = isSensitiveDataVisible
              ? '***'
              : listEconomic[vicinity.row - 1].fev.toString();
          break;
        case 5:
          displayValue = isSensitiveDataVisible
              ? '***'
              : listEconomic[vicinity.row - 1].mar.toString();
          break;
        case 6:
          displayValue = isSensitiveDataVisible
              ? '***'
              : listEconomic[vicinity.row - 1].abr.toString();
          break;
        case 7:
          displayValue = isSensitiveDataVisible
              ? '***'
              : listEconomic[vicinity.row - 1].mai.toString();
          break;
        case 8:
          displayValue = isSensitiveDataVisible
              ? '***'
              : listEconomic[vicinity.row - 1].jun.toString();
          break;
        case 9:
          displayValue = isSensitiveDataVisible
              ? '***'
              : listEconomic[vicinity.row - 1].jul.toString();
          break;
        case 10:
          displayValue = isSensitiveDataVisible
              ? '***'
              : listEconomic[vicinity.row - 1].ago.toString();
          break;
        case 11:
          displayValue = isSensitiveDataVisible
              ? '***'
              : listEconomic[vicinity.row - 1].theSet.toString();
          break;
        case 12:
          displayValue = isSensitiveDataVisible
              ? '***'
              : listEconomic[vicinity.row - 1].out.toString();
          break;
        case 13:
          displayValue = isSensitiveDataVisible
              ? '***'
              : listEconomic[vicinity.row - 1].nov.toString();
          break;
        case 14:
          displayValue = isSensitiveDataVisible
              ? '***'
              : listEconomic[vicinity.row - 1].dez.toString();
          break;
        default:
          displayValue = 'Data inválida';
      }

      return Center(
        child: Text(displayValue),
      );
    },
  );
}

    return const Center(
      child: Text(
        "--",
      ),
    );
  }

  TableSpan _buildColumnSpan(int index) {
    switch (index) {
      case 0:
        return TableSpan(
          extent: FixedTableSpanExtent(MediaQuery.of(context).size.width * 0.5),
          padding: const TableSpanPadding.all(8),
          onEnter: (_) => debugPrint('Entered column $index'),
          recognizerFactories: <Type, GestureRecognizerFactory>{
            TapGestureRecognizer:
                GestureRecognizerFactoryWithHandlers<TapGestureRecognizer>(
              () => TapGestureRecognizer(),
              (TapGestureRecognizer t) =>
                  t.onTap = () => debugPrint('Tap column $index'),
            ),
          },
        );
      case 1:
        return TableSpan(
          extent: FixedTableSpanExtent(MediaQuery.of(context).size.width * 0.2),
          padding: const TableSpanPadding.all(5),
          onEnter: (_) => debugPrint('Entered column $index'),
          recognizerFactories: <Type, GestureRecognizerFactory>{
            TapGestureRecognizer:
                GestureRecognizerFactoryWithHandlers<TapGestureRecognizer>(
              () => TapGestureRecognizer(),
              (TapGestureRecognizer t) =>
                  t.onTap = () => debugPrint('Tap column $index'),
            ),
          },
        );
      case 2:
        return TableSpan(
          extent: FixedTableSpanExtent(MediaQuery.of(context).size.width * 0.2),
          padding: const TableSpanPadding.all(5),
          onEnter: (_) => debugPrint('Entered column $index'),
          recognizerFactories: <Type, GestureRecognizerFactory>{
            TapGestureRecognizer:
                GestureRecognizerFactoryWithHandlers<TapGestureRecognizer>(
              () => TapGestureRecognizer(),
              (TapGestureRecognizer t) =>
                  t.onTap = () => debugPrint('Tap column $index'),
            ),
          },
        );

      default:
        return TableSpan(
          extent: FixedTableSpanExtent(MediaQuery.of(context).size.width * 0.3),
          padding: const TableSpanPadding.all(5),
          onEnter: (_) => debugPrint('Entered column $index'),
        );
    }
  }

  TableSpan _buildRowSpan(int index) {
    final decoration = TableSpanDecoration(
      color: index == 0
          ? CooperadoColors.lightSeaGreen
          : CooperadoColors.aliceBlue,
      border: const TableSpanBorder(
        trailing: BorderSide(
          width: 5,
          color: Colors.white,
        ),
      ),
    );
    return TableSpan(
      backgroundDecoration: decoration,
      extent: FixedTableSpanExtent(index == 0 ? 25 : 90),
      padding: const TableSpanPadding.all(5),
      recognizerFactories: <Type, GestureRecognizerFactory>{
        TapGestureRecognizer:
            GestureRecognizerFactoryWithHandlers<TapGestureRecognizer>(
          () => TapGestureRecognizer(),
          (TapGestureRecognizer t) =>
              t.onTap = () => debugPrint('Tap row $index'),
        ),
      },
    );
  }

  _enableRotation() {
    SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,
      DeviceOrientation.portraitDown,
      DeviceOrientation.landscapeLeft,
      DeviceOrientation.landscapeRight,
    ]);
  }

  _disableRotation() {
    SystemChrome.setPreferredOrientations([DeviceOrientation.portraitUp]);
  }
}
