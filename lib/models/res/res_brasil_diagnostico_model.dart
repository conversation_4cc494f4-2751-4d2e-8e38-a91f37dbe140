import 'package:intl/intl.dart';

class ResBrazilDiagnosticoModel {
  final String? descricao;
  final String? nomeMedico;
  final String? dataEntrada;

  ResBrazilDiagnosticoModel({
    this.descricao,
    this.nomeMedico,
    this.dataEntrada,
  });

  String get dateDiagnosticoFormatted {
    return DateFormat('dd/MM/yyyy HH:mm').format(DateTime.parse(dataEntrada!));
  }

  ResBrazilDiagnosticoModel.fromJson(Map<String, dynamic> json)
      : descricao = json['descricao'],
        nomeMedico = json['nomeMedico'],
        dataEntrada = json['dataEntrada'];

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['descricao'] = descricao;
    data['nomeMedico'] = nomeMedico;
    data['dataEntrada'] = dataEntrada;
    return data;
  }
}
