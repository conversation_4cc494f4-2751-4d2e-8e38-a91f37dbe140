part of 'res_configs_cubit.dart';

abstract class ResConfigsState extends Equatable {
  const ResConfigsState();

  @override
  List<Object> get props => [];
}

class InitialResConfigsState extends ResConfigsState {}

class LoadingResConfigsState extends ResConfigsState {
  @override
  List<Object> get props => [];
}

class ErrorResConfigsState extends ResConfigsState {
  final String message;

  @override
  List<Object> get props => [message];

  const ErrorResConfigsState({required this.message});
}

class LoadedResConfigsState extends ResConfigsState {
  final ResConfigModel resConfigModel;

  @override
  List<Object> get props => [resConfigModel];

  const LoadedResConfigsState({required this.resConfigModel});
}
