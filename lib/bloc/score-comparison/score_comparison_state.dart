part of 'score_comparison_cubit.dart';

abstract class ScoreComparisonState extends Equatable {
  const ScoreComparisonState();

  @override
  List<Object?> get props => [];
}

class ScoreComparisonInitial extends ScoreComparisonState {}

class LoadingGetScoreComparisonState extends ScoreComparisonState {
  @override
  List<Object> get props => [];
}

class ErrorGetScoreComparisonState extends ScoreComparisonState {
  final String message;
  @override
  List<Object> get props => [message];
  const ErrorGetScoreComparisonState(this.message);
}

class DoneGetScoreComparisonState extends ScoreComparisonState {
  final ResponseScore? responseScore;
  @override
  List<Object?> get props => [responseScore];

  const DoneGetScoreComparisonState({this.responseScore});
}
