import 'package:cooperado_minha_unimed/bloc/sensitive-data/sensitive_data_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/sensitive-data/sensitive_data_state.dart';
import 'package:cooperado_minha_unimed/colors.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:flutter/material.dart';

import 'package:cooperado_minha_unimed/shared/vo/transparencia/custos_assistenciais.vo.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class DonutChart extends StatefulWidget {
  final bool animate;
  final List<Custo>? custos;

  const DonutChart({
    super.key,
    required this.animate,
    this.custos,
  });

  @override
  DonutChartState createState() => DonutChartState();
}

class DonutChartState extends State<DonutChart> {
  double total = 0.0;
  int touchedIndex = -1;
  List<PieChartSectionData> pieDataCustos = [];

  @override
  void initState() {
    pieDataCustos.clear();
    pieDataCustos = _createData();
    super.initState();
  }

  @override
 Widget build(BuildContext context) {
  return BlocBuilder<SensitiveDataCubit, SensitiveDataState>(
    builder: (context, sensitiveState) {
      bool isSensitiveDataVisible = sensitiveState.isSensitiveDataVisible;
      return Center(
        child: AspectRatio(
          aspectRatio: 1,
          child: PieChart(
            PieChartData(
              pieTouchData: PieTouchData(),
              borderData: FlBorderData(
                show: false,
              ),
              sectionsSpace: 2,
              centerSpaceRadius: 50,
              startDegreeOffset: 270,
              sections: !isSensitiveDataVisible
                  ? [
                      PieChartSectionData(
                        color: Colors.grey,
                        value: 100,
                        showTitle: false,
                      ),
                    ]
                  : showingSections(),
            ),
          ),
        ),
      );
    },
  );
}

  _createData() {
    List<PieChartSectionData> data = [];
    const double fontSize = 16.0;
    const double radius = 50.0;
    const shadows = [Shadow(color: Colors.black, blurRadius: 2)];

    for (int i = 0; i < widget.custos!.length; i++) {
      data.add(PieChartSectionData(
        color: _getColor(CooperadoColors.chartColors[i]),
        value: widget.custos?[i].valor,
        title: "",
        radius: radius,
        titleStyle: const TextStyle(
          fontSize: fontSize,
          fontWeight: FontWeight.bold,
          color: Colors.white,
          shadows: shadows,
        ),
      ));
    }
    return data;
  }

  Color _getColor(Color color) {
    return Color.fromARGB(color.alpha, color.red, color.green, color.blue);
  }

  List<PieChartSectionData> showingSections() {
    return pieDataCustos;
  }
}
