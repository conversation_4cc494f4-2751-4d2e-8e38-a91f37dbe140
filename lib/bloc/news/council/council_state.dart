part of 'council_cubit.dart';

abstract class CouncilState extends Equatable {
  const CouncilState();

  @override
  List<Object?> get props => [];
}

class CouncilInitial extends CouncilState {}

class LoadingFiscalCouncilNewsState extends CouncilState {
  @override
  List<Object> get props => [];
}

class LoadingFiscalCouncilNewsStatePagination extends CouncilState {
  final List<Noticia> list;
  @override
  List<Object> get props => [list];

  const LoadingFiscalCouncilNewsStatePagination({required this.list});
}

class ErrorFiscalCouncilNewsState extends CouncilState {
  final String message;
  @override
  List<Object> get props => [message];
  const ErrorFiscalCouncilNewsState(this.message);
}

class DoneFiscalCouncilNewsState extends CouncilState {
  final List<Noticia> list;
  @override
  List<Object> get props => [list];

  const DoneFiscalCouncilNewsState({required this.list});
}
