import 'package:cooperado_minha_unimed/bloc/transparency/contingency_index/contingency_index_cubit.dart';
import 'package:cooperado_minha_unimed/colors.dart';
import 'package:cooperado_minha_unimed/screens/transparencia/contingency-index/contingency_index_detail.dart';
import 'package:cooperado_minha_unimed/screens/transparencia/graph-transparency/contingency_barchart.dart';
import 'package:cooperado_minha_unimed/shared/screen-transitions/fade.transition.dart';
import 'package:cooperado_minha_unimed/shared/widgets/buttons.dart';
import 'package:cooperado_minha_unimed/shared/widgets/card_refresh.dart';
import 'package:cooperado_minha_unimed/shared/widgets/error-banner/error_banner.component.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';

import '../../../shared/vo/transparencia/indices.dart';

class ContingencyIndexCard extends StatefulWidget {
  final bool isVisible;
  final Orientation orientation;

  const ContingencyIndexCard({super.key, this.isVisible = true, required this.orientation});

  @override
  CardIndiceContingenciaState createState() => CardIndiceContingenciaState();
}

class CardIndiceContingenciaState extends State<ContingencyIndexCard> {
  double total = 0.0;
  DateTime? selectedDateTime;
  int dataLimit = 6;
  List<String> years = [];

  @override
  void initState() {
    if (widget.isVisible) _getContingencyIndex();
    super.initState();
  }

  void _getContingencyIndex() {
    context.read<ContingencyIndexCubit>().getContingencyIndex();
  }

    List<String> extractYearsFromData(List<VODataModel> data) {
    List<VODataModel> limitedData;
    if (widget.orientation == Orientation.portrait) {
      int startIndex = data.length - dataLimit;
      limitedData = data.sublist(startIndex >= 0 ? startIndex : 0);
    } else {
      limitedData = data.take(dataLimit).toList();
    }

    Set<String> years = {};

    for (var item in limitedData) {
      if (item.referenceYear != null) {
        years.add(item.referenceYear?.toString() ?? '');
      }
    }

    List<String> yearsList = years.toList()..sort();

    return yearsList;
  }

  

  @override
  Widget build(BuildContext context) {
    return Visibility(
      visible: widget.isVisible,
      child: CardRefresh(
        title: const Text('Índice de Contingência',
            style: TextStyle(
              color: CooperadoColors.blackText,
              fontSize: 16,
              fontWeight: FontWeight.bold,
            )),
        refresh: _iconRefresh(),
        child: BlocBuilder<ContingencyIndexCubit, ContingencyIndexState>(
          builder: (context, state) {
            if (state is LoadedContingencyIndexState) {
                years = extractYearsFromData(state.contingencyIndex.data!.reversed
                  .toList()
                  .sublist(0, dataLimit));
              return Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: <Widget>[
                  SizedBox(
                    height: 200,
                    child: ContingencyBarChart(
                      animate: true,
                      data: state.contingencyIndex.data,
                      colors: [
                        CooperadoColors.chartColors[9],
                      ],
                      orientation: MediaQuery.of(context).orientation,
                      dataLimit: dataLimit,
                    ),
                  ),
                  const SizedBox(height: 20),
                  ButtonSeeMore(
                    onPress: (){
                      _getContingencyIndex();
                      Navigator.push(
                      context,
                      FadeRoute(
                        page: ContingencyIndexDetail(
                            contingencyIndex: state.contingencyIndex),
                      ),
                    );
                    },
                  ),
                ],
              );
            } else if (state is LoadingContingencyIndexState) {
              return const SpinKitCircle(color: CooperadoColors.tealGreen);
            } else if (state is ErrorContingencyIndexState) {
              return ErrorBanner(message: state.message);
            } else {
              return Container();
            }
          },
        ),
      ),
    );
  }

  Widget _iconRefresh() {
    return BlocBuilder<ContingencyIndexCubit, ContingencyIndexState>(
        builder: (context, state) {
      if (state is ErrorContingencyIndexState) {
        return InkWell(
            child: const Icon(Icons.refresh),
            onTap: () => _getContingencyIndex());
      } else {
        return Container();
      }
    });
  }
}
