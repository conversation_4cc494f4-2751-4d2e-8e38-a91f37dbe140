import 'package:auto_size_text/auto_size_text.dart';
import 'package:cooperado_minha_unimed/colors.dart';
import 'package:cooperado_minha_unimed/screens/login.dart';
import 'package:cooperado_minha_unimed/shared/screen-transitions/fade.transition.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/material.dart';
import 'package:flutter_swiper_plus/flutter_swiper_plus.dart';

class SlideScreen extends StatefulWidget {
  final FirebaseAnalytics analytics;
  final FirebaseAnalyticsObserver observer;

  const SlideScreen({
    super.key,
    required this.analytics,
    required this.observer,
  });

  @override
  SlideScreenState createState() => SlideScreenState();
}

class SlideScreenState extends State<SlideScreen> {
  final _controller = SwiperController();
  List list = [
    TutorialModel(
        img: "assets/medico1.png",
        title: "Acompanhe seus indicadores pelo cooperativo"),
    TutorialModel(
        img: "assets/medico2.png",
        title: "Mantenha-se informado com o cooperativo"),
    TutorialModel(
        img: "assets/medico3.png", title: "Verifique aqui sua produção médica"),
  ];

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        top: false,
        child: Swiper(
          controller: _controller,
          itemBuilder: (BuildContext context, int index) {
            final item = list.elementAt(index);
            return Container(
                color: CooperadoColors.tealGreenDark,
                width: double.infinity,
                child: _childItem(item, index));
          },
          loop: false,
          itemCount: list.length,
          pagination: const SwiperPagination(),
        ),
      ),
    );
  }

  Widget _childItem(TutorialModel tutorialModel, int index) {
    return Stack(
      children: [
        if (index != 2)
          Positioned(
            top: 20,
            right: 20,
            child: TextButton(
              child: const Text(
                'PULAR',
                style:
                    TextStyle(color: CooperadoColors.grayLight3, fontSize: 14),
              ),
              onPressed: () => _controller.next(),
            ),
          ),
        Positioned(
          top: 50,
          left: 10,
          child: SizedBox(
            width: MediaQuery.of(context).size.width * 0.8,
            child: Padding(
              padding: const EdgeInsets.only(top: 8.0, left: 24.0),
              child: AutoSizeText(
                tutorialModel.title!,
                style: const TextStyle(color: Colors.white, fontSize: 26),
              ),
            ),
          ),
        ),
        index == 1
            ? Positioned(
                bottom: 0,
                right: 0,
                child: Image.asset(
                  tutorialModel.img!,
                  height: (MediaQuery.of(context).size.height / 2) * 1.3,
                ),
              )
            : Positioned(
                bottom: 0,
                left: 0,
                child: Image.asset(
                  tutorialModel.img!,
                  height: (MediaQuery.of(context).size.height / 2) * 1.3,
                ),
              ),
        index == 2
            ? Positioned(
                bottom: 50,
                right: 0,
                left: 0,
                child: Padding(
                  padding:
                      const EdgeInsets.symmetric(vertical: 0, horizontal: 24),
                  child: ElevatedButton(
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.white,
                      shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                          side: const BorderSide(
                              color: CooperadoColors.tealGreenDark)),
                    ),
                    child: const Text(
                      "COMEÇAR",
                      style: TextStyle(color: CooperadoColors.tealGreenDark),
                    ),
                    onPressed: () {
                      widget.analytics.logScreenView(
                        screenName: 'Login',
                        screenClass: 'LoginScreen',
                      );

                      Navigator.push(
                          context,
                          FadeRoute(
                              page: LoginScreen(
                            analytics: widget.analytics,
                            observer: widget.observer,
                          )));
                      // LoginScreen
                    },
                  ),
                ))
            : Container()
      ],
    );
  }
}

class TutorialModel {
  final String? img;
  final String? title;

  TutorialModel({this.img, this.title});
}
