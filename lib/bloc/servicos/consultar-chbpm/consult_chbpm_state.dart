import 'package:cooperado_minha_unimed/shared/vo/servicos/procedure_list.vo.dart';
import 'package:equatable/equatable.dart';

abstract class ConsultChbpmState extends Equatable {
  const ConsultChbpmState();
}

class ConsultChbpmInitial extends ConsultChbpmState {
  @override
  List<Object> get props => [];
}

class LoadingGetConsultChbpmState extends ConsultChbpmState {
  @override
  List<Object> get props => [];
}

class ErrorGetConsultChbpmState extends ConsultChbpmState {
  final String message;
  @override
  List<Object> get props => [message];
  const ErrorGetConsultChbpmState(this.message);
}

class DoneGetConsultChbpmState extends ConsultChbpmState {
  final ProcedureList procedureList;
  @override
  List<Object> get props => [procedureList];

  const DoneGetConsultChbpmState(this.procedureList);
}
