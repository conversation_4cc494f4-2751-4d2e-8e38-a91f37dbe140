import 'package:bloc/bloc.dart';
import 'package:cooperado_minha_unimed/shared/api/financial.api.dart';
import 'package:cooperado_minha_unimed/shared/constants.dart';
import 'package:cooperado_minha_unimed/shared/locator.dart';
import 'package:cooperado_minha_unimed/shared/utils/file.dart';
import 'package:equatable/equatable.dart';

part 'invoice_state.dart';

class InvoiceCubit extends Cubit<InvoiceState> {
  InvoiceCubit() : super(FinancialInitial());

  getFatura(
      {required String carteira,
      required String data,
      required int tipoRetorno}) async {
    emit(LoadingInvoiceState(dataReferencia: data, tipoRetorno: tipoRetorno));
    try {
      final retorno = await Locator.instance!<FinancialApi>().getInvoice(
          carteira: carteira, dataReferencia: data, tipoRetorno: tipoRetorno);

      if (tipoRetorno == TipoRetornoFaturas.pdf) {
        final pathFile = await FileUtils.createFileFromString(
            base64String: retorno, extension: FileExtension.pdf);

        emit(LoadedInvoicePdfState(pathFile: pathFile, invoiceDate: data));
      } else {
        emit(LoadedInvoiceBarCodeState(barCode: retorno, invoiceDate: data));
      }
    } catch (ex) {
      emit(ErrorInvoiceState('$ex'));
    }
  }
}
