import 'dart:convert';

import 'package:cooperado_minha_unimed/models/office_notices.model.dart';
import 'package:cooperado_minha_unimed/shared/api/auth.api.dart';
import 'package:cooperado_minha_unimed/shared/exceptions.dart';
import 'package:cooperado_minha_unimed/shared/flavor_config.dart';
import 'package:cooperado_minha_unimed/shared/locator.dart';
import 'package:cooperado_minha_unimed/shared/utils/logger_print.dart';
import 'package:http_client/http_client.dart';

class OfficeNoticesApi {
  final UnimedHttpClient httpClient;
  OfficeNoticesApi(this.httpClient);

  final logger = UnimedLogger(className: 'OfficeNoticesApi');

  Future<List<OfficeNoticeModel>> getOfficeNotices({required String codPrestador, required bool showHideNotifications, required int page, required int pageSize}) async {
    try {
      final tokenPerfilApps = await Locator.instance!.get<AuthApi>().tokenPerfilApps();

      final url = '${FlavorConfig.instance!.values.profilePermissions.url}app/cooperado/office-notices/$codPrestador/prestador?showHideNotifications=$showHideNotifications&page=$page&pageSize=$pageSize';

      final response = await httpClient.get(
        Uri.parse(url),
        headers: {
          "Content-Type": "application/json",
          "Authorization": "Bearer $tokenPerfilApps",
        },
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body)['retorno'];
        final collection = (data as List).map<OfficeNoticeModel>((e) => OfficeNoticeModel.fromJson(e)).toList();

        logger.d('getOfficeNotices success list ${collection.length}');
        return collection;
      } else {
        final data = jsonDecode(response.body);
        final message = data['message'] ?? 'Não foi possível no momento.';
        logger.e('getOfficeNotices statusCode : ${response.statusCode} ${response.body}');
        throw UnimedException('$message');
      }
    } on UnimedException {
      rethrow;
    } catch (ex) {
      logger.e('getOfficeNotices exception : $ex');
      throw UnimedException('Não foi possível no momento.');
    }
  }

  Future<void> readNotification({required String crm, required int codNotificacao}) async {
    try {
      final tokenPerfilApps = await Locator.instance!.get<AuthApi>().tokenPerfilApps();
      final url = '${FlavorConfig.instance!.values.profilePermissions.url}app/cooperado/office-notices/read';

      final response = await httpClient.post(
        Uri.parse(url),
        body: jsonEncode({
          "crm": crm,
          "codNotificacao": codNotificacao,
        }),
        headers: {
          "Content-Type": "application/json",
          "Authorization": "Bearer $tokenPerfilApps",
        },
      );

      if (response.statusCode != 200 && response.statusCode != 201) {
        final message = jsonDecode(response.body)['mensagem'] ?? 'Não foi possível no momento.';
        logger.e('readNotification statusCode : ${response.statusCode} ${response.body}');
        throw UnimedException('$message');
      }
    } on UnimedException catch (ex) {
      logger.e('readNotification UnimedException : $ex');
      throw UnimedException(ex.toString());
    } catch (ex) {
      logger.e('readNotification exception : $ex');
      throw UnimedException(ex.toString());
    }
  }

  Future<void> hideNotification({required String crm, required int codNotificacao}) async {
    try {
      final tokenPerfilApps = await Locator.instance!.get<AuthApi>().tokenPerfilApps();

      final url = '${FlavorConfig.instance!.values.profilePermissions.url}app/cooperado/office-notices/hide';

      final response = await httpClient.post(
        Uri.parse(url),
        body: jsonEncode({
          "crm": crm,
          "codNotificacao": codNotificacao,
        }),
        headers: {
          "Content-Type": "application/json",
          "Authorization": "Bearer $tokenPerfilApps",
        },
      );

      if (response.statusCode != 200 && response.statusCode != 201) {
        final message = jsonDecode(response.body)['mensagem'] ?? 'Não foi possível no momento.';
        logger.e('hideNotification statusCode : ${response.statusCode} ${response.body}');
        throw UnimedException('$message');
      }
    } on UnimedException catch (ex) {
      logger.e('hideNotification UnimedException : $ex');
      throw UnimedException(ex.toString());
    } catch (ex) {
      logger.e('hideNotification exception : $ex');
      throw UnimedException(ex.toString());
    }
  }
}
