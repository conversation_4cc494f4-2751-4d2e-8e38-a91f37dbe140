import 'package:cooperado_minha_unimed/colors.dart';
import 'package:flutter/material.dart';

class ElevatedButtonCustom extends StatelessWidget {
  const ElevatedButtonCustom({
    super.key,
    required this.title,
    required this.onPressed,
    this.color,
  });
  final String title;
  final Function()? onPressed;
  final Color? color;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0, horizontal: 8.0),
      child: ElevatedButton(
        style: ElevatedButton.styleFrom(
          backgroundColor: onPressed == null
              ? Colors.grey
              : (color ?? CooperadoColors.tealGreen),
          padding: const EdgeInsets.symmetric(vertical: 16.0),
        ),
        onPressed: onPressed,
        child: Text(
          title.toUpperCase(),
          style: TextStyle(
            color: onPressed == null ? Colors.white54 : Colors.white,
          ),
        ),
      ),
    );
  }
}
