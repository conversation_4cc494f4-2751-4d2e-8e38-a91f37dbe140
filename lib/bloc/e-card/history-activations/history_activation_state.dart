// ignore_for_file: must_be_immutable

import 'package:cooperado_minha_unimed/models/ecard-models/history_transaction_model.dart';
import 'package:equatable/equatable.dart';

abstract class HistoryEcardActivationState extends Equatable {
  const HistoryEcardActivationState();

  @override
  List<Object> get props => [];
}

class InitialHistoryActivationState extends HistoryEcardActivationState {}

class LoadingHistoryActivationState extends HistoryEcardActivationState {
  @override
  List<Object> get props => [];
}

class ErrorHistoryActivationState extends HistoryEcardActivationState {
  final String message;

  @override
  List<Object> get props => [message];

  const ErrorHistoryActivationState({required this.message});
}

class LoadedHistoryActivationState extends HistoryEcardActivationState {
  List<HistoryTransactionEcardModel> listHistoryActivarion = [];

  @override
  List<Object> get props => [listHistoryActivarion];

  LoadedHistoryActivationState({required this.listHistoryActivarion});
}

class NoDataHistoryActivationState extends HistoryEcardActivationState {}
