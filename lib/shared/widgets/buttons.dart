import 'package:cooperado_minha_unimed/colors.dart';
import 'package:flutter/material.dart';

class ButtonSeeMore extends StatelessWidget {
  final Function? onPress;
  const ButtonSeeMore({super.key, this.onPress});
  @override
  Widget build(BuildContext context) {
    return Align(
      child: InkWell(
        onTap: () {
          if (onPress != null) onPress!();
        },
        child: Container(
          padding: const EdgeInsets.only(top: 12.0, bottom: 12.0, left: 40, right: 40),
          decoration: const BoxDecoration(color: CooperadoColors.tealGreen, borderRadius: BorderRadius.only(topLeft: Radius.circular(20), topRight: Radius.circular(20))),
          child: const Text(
            "VER MAIS",
            style: TextStyle(
              //fontSize: 18,
              color: Colors.white,
            ),
          ),
        ),
      ),
    );
  }
}
