import 'package:cooperado_minha_unimed/bloc/fiscal-council/fiscal_council_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/fiscal-council/form-council/form_council_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/fiscal-council/form-council/send-form/send_form_cubit.dart';
import 'package:cooperado_minha_unimed/colors.dart';
import 'package:cooperado_minha_unimed/models/fiscal_council.model.dart';
import 'package:cooperado_minha_unimed/shared/widgets/alert.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:mask_text_input_formatter/mask_text_input_formatter.dart';

class FormCouncilWidget extends StatefulWidget {
  final CouncilTopics? councilTopics;
  final List<CouncilTopics>? list;
  const FormCouncilWidget({super.key, this.councilTopics, this.list});
  @override
  FormCouncilWidgetState createState() => FormCouncilWidgetState();
}

class FormCouncilWidgetState extends State<FormCouncilWidget> {
  final foneFormatter = MaskTextInputFormatter(
    mask: '(##) #####-####',
    filter: {"#": RegExp(r'[0-9]')},
  );
  final msgFormatter = MaskTextInputFormatter(
    mask:
        '############################################################################################################################################################',
    filter: {
      "#": RegExp(
          r"^[a-zA-Z0-9 A-Za-záàâãéèêíïóôõöúçñÁÀÂÃÉÈÍÏÓÔÕÖÚÇÑ,.;?!@:-_]"),
    },
  );
  final tecFone = TextEditingController();
  final tecMsg = TextEditingController();
  final _formKey = GlobalKey<FormState>();
  late bool _disableFields;
  bool anonymous = false;
  @override
  void initState() {
    _disableFields = false;
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      onPopInvokedWithResult: (value, result) async {
        context.read<FormCouncilCubit>().getTopicEvent();
        context.read<FiscalCouncilCubit>().getCouncil();
      },
      child: Scaffold(
        appBar: AppBar(
          title: const Text("Fale com o conselho"),
          backgroundColor: CooperadoColors.tealGreenDark,
        ),
        body: Card(
          child: Padding(
            padding: const EdgeInsets.all(8.0),
            child: Form(
              key: _formKey,
              child: IgnorePointer(
                ignoring: _disableFields,
                child: SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: <Widget>[
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          SizedBox(
                            width: MediaQuery.of(context).size.width / 1.4,
                            child: DropdownButtonFormField(
                                isExpanded: true,
                                style: const TextStyle(
                                    color: CooperadoColors.tealGreen),
                                disabledHint: Text(
                                  widget.councilTopics!.descricao!,
                                ),
                                decoration: const InputDecoration(
                                    enabledBorder: OutlineInputBorder(
                                      borderSide: BorderSide(
                                          color: CooperadoColors.grayLight),
                                    ),
                                    labelText: "Assunto"),
                                value: widget.councilTopics,
                                items: widget.list!
                                    .map(
                                      (p) => DropdownMenuItem<CouncilTopics>(
                                        value: p,
                                        child: FittedBox(
                                          fit: BoxFit.contain,
                                          child: SizedBox(
                                            width: MediaQuery.of(context)
                                                    .size
                                                    .width -
                                                50,
                                            child: Text(
                                              '${p.descricao}',
                                              style: const TextStyle(
                                                  fontSize: 20,
                                                  color:
                                                      CooperadoColors.grayDark),
                                            ),
                                          ),
                                        ),
                                      ),
                                    )
                                    .toList(),
                                onChanged: null),
                          ),
                          Padding(
                            padding: const EdgeInsets.symmetric(vertical: 5),
                            child: Column(
                              children: [
                                const Text(
                                  'Anônimo:',
                                  style: TextStyle(fontSize: 14),
                                ),
                                Transform.scale(
                                  scale: 1.2,
                                  child: Switch(
                                      activeColor: CooperadoColors.tealGreen,
                                      value: anonymous,
                                      onChanged: (value) {
                                        setState(() {
                                          anonymous = !anonymous;
                                        });
                                        if (anonymous) {
                                          tecFone.clear();
                                        }
                                      }),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                      Padding(
                          padding: const EdgeInsets.symmetric(vertical: 8.0),
                          child: TextFormField(
                            enabled: !anonymous,
                            controller: tecFone,
                            keyboardType: TextInputType.number,
                            style: const TextStyle(
                                color: CooperadoColors.tealGreen),
                            decoration: InputDecoration(
                                disabledBorder: _borderInput(),
                                enabledBorder: _borderInput(),
                                focusedBorder: _borderInput(),
                                errorBorder: _borderInput(),
                                focusedErrorBorder: _borderInput(),
                                labelText: 'Telefone',
                                labelStyle: const TextStyle(
                                    color: CooperadoColors.tealGreen)),
                            inputFormatters: [foneFormatter],
                            validator: (value) {
                              if (!anonymous) {
                                if (value!.isEmpty) {
                                  setState(() => _disableFields = false);
                                  return 'Necessário um telefone de contato';
                                }
                              }

                              return null;
                            },
                          )),
                      Padding(
                        padding: const EdgeInsets.symmetric(vertical: 8.0),
                        child: TextFormField(
                          inputFormatters: [msgFormatter],
                          controller: tecMsg,
                          maxLines: 5,
                          minLines: 5,
                          textInputAction: TextInputAction.send,
                          style:
                              const TextStyle(color: CooperadoColors.tealGreen),
                          decoration: InputDecoration(
                              enabledBorder: _borderInput(),
                              focusedBorder: _borderInput(),
                              errorBorder: _borderInput(),
                              focusedErrorBorder: _borderInput(),
                              labelText: 'Mensagem',
                              labelStyle: const TextStyle(
                                  color: CooperadoColors.tealGreen)),
                          validator: (value) {
                            if (value!.isEmpty) {
                              setState(() => _disableFields = false);
                              return 'Insira sua mensagem para o conselho';
                            }
                            return null;
                          },
                        ),
                      ),
                      _button()
                    ],
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  InputBorder _borderInput() {
    return const OutlineInputBorder(
      borderSide: BorderSide(color: CooperadoColors.grayLight),
    );
  }

  Widget _button() {
    return BlocConsumer<SendFormCubit, SendFormState>(
      listener: (context, state) {
        if (state is DoneSendMessage) {
          setState(() => _disableFields = false);
          _show(state.message);
        } else if (state is ErrorSendMessage) {
          setState(() => _disableFields = false);
          _show(state.message);
        }
      },
      builder: (context, state) {
        if (state is LoadingSendMessage) {
          return const SpinKitThreeBounce(
            color: CooperadoColors.tealGreen,
          );
        } else {
          return ElevatedButton(
              style: ElevatedButton.styleFrom(
                  backgroundColor: CooperadoColors.tealGreen),
              onPressed: _submit,
              child: const Text("Enviar"));
        }
      },
    );
  }

  void _show(String? message) {
    showDialog(
      context: context,
      builder: (context) => CooperadoAlertDialog(
        textWidget: Text(
          message!,
          textAlign: TextAlign.center,
        ),
        onPressed: () {
          Navigator.of(context).pop();
        },
      ),
    );
  }

  void _submit() {
    setState(() => _disableFields = true);
    if (_formKey.currentState!.validate()) {
      context.read<SendFormCubit>().sendForm(
          tecMsg.text, widget.councilTopics!, tecFone.text, anonymous);
    } else {
      setState(() => _disableFields = false);
    }
  }
}
