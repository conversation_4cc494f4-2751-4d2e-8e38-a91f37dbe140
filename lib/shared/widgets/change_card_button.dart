import 'package:cooperado_minha_unimed/bloc/beneficiary/beneficiary_cubit.dart';
import 'package:cooperado_minha_unimed/colors.dart';
import 'package:cooperado_minha_unimed/models/beneficiary_card.model.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';

class ChangeCardButton extends StatelessWidget {
  final Function(BeneficiaryCardModel)? onUpdate;
  const ChangeCardButton({
    super.key,
    this.onUpdate,
  });

  static Future<void> _openChangeProfile(
    BuildContext context, {
    Function(BeneficiaryCardModel)? onUpdate,
  }) async {
    return await openSelectCardAlert(
      context,
      onUpdate: onUpdate,
    );
  }

  static Icon get _icon {
    return const Icon(
      Icons.person_outline,
      color: Colors.white,
    );
  }

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<BeneficiaryCubit, BeneficiaryState>(
      listener: (context, state) {
        if (state is LoadedBeneficiaryState) {
          if (onUpdate != null) {
            onUpdate!(BlocProvider.of<BeneficiaryCubit>(context).selectedCard);
          }
        }
      },
      builder: (context, state) {
        String initials = '';
        bool showButton = true;
        if (state is LoadedBeneficiaryState) {
          initials = state.selectCard.iniciaisBeneficiario;
          showButton = state.cards.length > 1;
        }

        return showButton
            ? TextButton(
                child: Row(
                  children: [
                    _icon,
                    Text(initials, style: const TextStyle(color: Colors.white)),
                  ],
                ),
                onPressed: () {
                  _openChangeProfile(
                    context,
                    onUpdate: onUpdate,
                  );
                },
              )
            : Container();
      },
    );
  }

  static Future<void> openSelectCardAlert(
    BuildContext context, {
    Function(BeneficiaryCardModel)? onUpdate,
  }) async {
    showDialog<List<BeneficiaryCardModel>>(
      context: context,
      builder: (BuildContext context) {
        // Create line
        List<SimpleDialogOption> listItems = [
          const SimpleDialogOption(
            onPressed: null,
            child: Divider(),
          )
        ];

        final cards = BlocProvider.of<BeneficiaryCubit>(context).cards;
        // Create perfil options
        for (BeneficiaryCardModel card in cards) {
          final bool perfilSelected = card.carteira ==
              BlocProvider.of<BeneficiaryCubit>(context).selectedCard.carteira;
          debugPrint(MediaQuery.of(context).size.width.toString());
          debugPrint(MediaQuery.of(context).textScaler.toString());

          listItems.add(
            SimpleDialogOption(onPressed: () {
              BlocProvider.of<BeneficiaryCubit>(context).selectCard(card: card);
              if (onUpdate != null) {
                onUpdate(
                    BlocProvider.of<BeneficiaryCubit>(context).selectedCard);
              }

              Navigator.of(context).pop();
            }, child: BlocBuilder<BeneficiaryCubit, BeneficiaryState>(
              builder: (context, state) {
                return Container(
                  width: double.infinity,
                  decoration: BoxDecoration(
                    border: perfilSelected
                        ? Border.all(
                            style: BorderStyle.solid,
                            color: CooperadoColors.green,
                          )
                        : null,
                    color: perfilSelected
                        ? CooperadoColors.green.withAlpha(30)
                        : Colors.white,
                  ),
                  child: Padding(
                    padding: const EdgeInsets.all(5.0),
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: <Widget>[
                        Padding(
                          padding: const EdgeInsets.only(left: 5.0),
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.start,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: <Widget>[
                              FittedBox(
                                child: Text(
                                  card.carteira,
                                  textAlign: TextAlign.justify,
                                  style: const TextStyle(
                                    fontWeight: FontWeight.bold,
                                    fontSize: 16,
                                  ),
                                ),
                              ),
                              Text(
                                card.descPlano,
                                style: const TextStyle(
                                  fontSize: 14,
                                ),
                              )
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                );
              },
            )),
          );
        }

        return BlocBuilder<BeneficiaryCubit, BeneficiaryState>(
          builder: (context, state) {
            if (state is LoadingBeneficiaryState) {
              return const SizedBox(
                width: double.infinity,
                child: SimpleDialog(
                  children: [
                    Column(
                      mainAxisSize: MainAxisSize.min,
                      children: <Widget>[
                        SpinKitThreeBounce(
                          color: CooperadoColors.green,
                          size: 20,
                        ),
                        SizedBox(height: 15),
                        Text('home.loadingProfile.text')
                      ],
                    )
                  ],
                ),
              );
            } else {
              return SizedBox(
                width: double.infinity,
                child: SimpleDialog(
                    contentPadding: const EdgeInsets.only(left: 0, right: 0),
                    shape: const RoundedRectangleBorder(
                      borderRadius: BorderRadius.all(Radius.circular(10.0)),
                    ),
                    title: const FittedBox(
                      child: Text(
                        'Selecione a carteira desejada',
                        textAlign: TextAlign.center,
                        style: TextStyle(fontSize: 18),
                      ),
                    ),
                    children: listItems),
              );
            }
          },
        );
      },
    );
  }
}
