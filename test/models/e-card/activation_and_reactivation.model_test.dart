import 'package:cooperado_minha_unimed/models/e-card/activation_and_reactivation.model.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('EcardActiveAndReativeCardModel', () {
    test('fromJson should return a valid model', () {
      final json = {
        'data': {
          'eCardActiveCard': {
            'message': 'Test message',
          },
        },
      };

      final model = EcardActiveAndReativeCardModel.fromJson(json);

      expect(model.message, 'Test message');
    });

    test('toJson should return a valid json', () {
      final model = EcardActiveAndReativeCardModel(message: 'Test message');

      final json = model.toJson();

      expect(json, {
        'data': {
          'eCardActiveCard': {
            'message': 'Test message',
          },
        },
      });
    });
  });
}
