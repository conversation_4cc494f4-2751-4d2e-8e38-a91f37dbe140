import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:cooperado_minha_unimed/models/user.model.dart';
import 'package:cooperado_minha_unimed/shared/api/auth.api.dart';
import 'package:cooperado_minha_unimed/shared/exceptions.dart';
import 'package:cooperado_minha_unimed/shared/flavor_config.dart';
import 'package:http_client/http_client.dart';
import 'package:cooperado_minha_unimed/shared/locator.dart';
import 'package:cooperado_minha_unimed/shared/messages.exceptions.dart';
import 'package:cooperado_minha_unimed/shared/utils/logger_print.dart';
import 'package:cooperado_minha_unimed/shared/vo/transparencia/demostrativo_resultado.dart';

class NoticeApi {
  final UnimedHttpClient httpClient;

  NoticeApi(this.httpClient);

  final logger = UnimedLogger(className: 'NoticeApi');

  Future<List<Noticia>> getNoticeByType(
      {required List<String> categories, required int page}) async {
    UserCredentials? credentials =
        await Locator.instance!.get<AuthApi>().getCredentials();

    final token = credentials != null
        ? await User.createToken(credentials: credentials)
        : "";
    // List<String> categorias = List();
    // categorias.add(noticeType);

    Map<String, Object> body = {
      "paginaSeguinte": page,
      "categorias": categories,
    };

    try {
      final url =
          '${FlavorConfig.instance!.values.portal.url}noticias?token=$token';
      final response =
          await httpClient.post(Uri.parse(url), body: jsonEncode(body));
      final data = jsonDecode(utf8.decode(response.bodyBytes));
      if (response.statusCode == 200) {
        final collection = (data["retorno"]["noticias"] as List)
            .map<Noticia>((e) => Noticia.fromJson(e))
            .toList();
        logger.d('getNoticeByType success list : ${collection.length}');
        return collection;
      } else {
        final message = jsonDecode(response.body);
        logger.e(
            'getNoticeByType statusCode : ${response.statusCode} ${response.body}');
        throw NoticeException('$message');
      }
    } on TimeoutException catch (ex) {
      logger.e('getNoticeByType TimeoutException exceptionÇ ${ex.message}');
      throw NoticeException(MessageException.noInternet);
    } on SocketException catch (ex) {
      logger.e('getNoticeByType SocketException: ${ex.toString()}');
      throw NoticeException(MessageException.noInternet);
    } catch (ex) {
      logger.e('getNoticeByType NoticeException: ${ex.toString()}');
      throw NoticeException(MessageException.general);
    }
  }

  Future<String?> readNews(Noticia noticiasPortal) async {
    UserCredentials? credentials =
        await Locator.instance!.get<AuthApi>().getCredentials();

    final token = credentials != null
        ? await User.createToken(credentials: credentials)
        : "";

    Map<String, Object?> news = {
      "id": noticiasPortal.id,
    };

    final body = jsonEncode({"noticia": news});

    try {
      final url =
          '${FlavorConfig.instance!.values.portal.url}marcar-leitura?token=$token';
      final response = await httpClient.post(Uri.parse(url), body: body);
      final data = jsonDecode(utf8.decode(response.bodyBytes));
      if (response.statusCode == 200) {
        logger.d(
            'readNews success statusCode : ${response.statusCode} ${response.body}');
        return data["mensagem"];
      } else {
        final message = jsonDecode(response.body);
        logger.e('readNews statusCode : ${response.statusCode}');
        throw NoticeException('$message');
      }
    } catch (ex) {
      logger.e('readNews exception : $ex');
      throw NoticeException('$ex');
    }
  }
}
