import 'package:cooperado_minha_unimed/colors.dart';
import 'package:flutter/material.dart';

class CooperadoAlertNews extends StatelessWidget {
  const CooperadoAlertNews(
      {super.key,
      required this.onPressed,
      required this.textWidget,
      this.textTitle,
      this.iconData = Icons.info_outline,
      this.textButton = "Ok",
      this.colorIcon = CooperadoColors.tealGreen});

  final IconData iconData;
  final String? textTitle;
  final VoidCallback onPressed;
  final Widget textWidget;
  final String textButton;
  final Color colorIcon;

  @override
  Widget build(context) {
    return AlertDialog(
      shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.only(
              topLeft: Radius.circular(10),
              topRight: Radius.circular(10),
              bottomLeft: Radius.circular(10))),
      title: Center(
          child: Text(
        textTitle!,
        style: const TextStyle(fontSize: 18),
      )),
      content: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: <Widget>[
            textWidget,
            const Divider(
              height: 5.0,
              color: Colors.transparent,
            ),
            const SizedBox(height: 10.0),
          ]),
      backgroundColor: Colors.white,
      actions: <Widget>[
        ElevatedButton(
            style: ElevatedButton.styleFrom(
              shape: const RoundedRectangleBorder(
                borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(10),
                    topRight: Radius.circular(10),
                    bottomLeft: Radius.circular(10)),
              ),
              textStyle: const TextStyle(color: Colors.white),
              backgroundColor: CooperadoColors.tealGreen,
            ),
            onPressed: onPressed,
            child: Text(
              textButton,
              style: const TextStyle(fontSize: 12),
            )),
        ElevatedButton(
            style: ElevatedButton.styleFrom(
                shape: const RoundedRectangleBorder(
                  borderRadius: BorderRadius.only(
                      topLeft: Radius.circular(10),
                      topRight: Radius.circular(10),
                      bottomLeft: Radius.circular(10)),
                ),
                backgroundColor: CooperadoColors.grayDark2,
                textStyle: const TextStyle(
                  color: Colors.white,
                )),
            child: const Text(
              "Fechar",
              style: TextStyle(fontSize: 12),
            ),
            onPressed: () => Navigator.pop(context)),
      ],
    );
  }
}
