import 'package:bloc/bloc.dart';
import 'package:cooperado_minha_unimed/shared/locator.dart';
import 'package:equatable/equatable.dart';
import 'package:cooperado_minha_unimed/shared/api/score.api.dart';
import 'package:cooperado_minha_unimed/shared/vo/score_comparison.vo.dart';

part 'score_comparison_state.dart';

class ScoreComparisonCubit extends Cubit<ScoreComparisonState> {
  ScoreComparisonCubit() : super(ScoreComparisonInitial());

  getScoreComparisonEvent() async {
    emit(LoadingGetScoreComparisonState());
    try {
      final ResponseScore responseScore =
          await Locator.instance!<ScoreApi>().getScoreComparison();

      emit(DoneGetScoreComparisonState(responseScore: responseScore));
    } catch (ex) {
      emit(ErrorGetScoreComparisonState('$ex'));
    }
  }
}
