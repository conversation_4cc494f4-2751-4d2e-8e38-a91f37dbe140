import 'dart:async';
import 'dart:convert';

import 'package:cooperado_minha_unimed/models/indicadores.model.dart';
import 'package:cooperado_minha_unimed/models/service_historic.model.dart';
import 'package:cooperado_minha_unimed/models/user.model.dart';
import 'package:cooperado_minha_unimed/shared/api/auth.api.dart';
import 'package:cooperado_minha_unimed/shared/exceptions.dart';
import 'package:cooperado_minha_unimed/shared/flavor_config.dart';
import 'package:cooperado_minha_unimed/shared/locator.dart';
import 'package:cooperado_minha_unimed/shared/utils/logger_print.dart';
import 'package:cooperado_minha_unimed/shared/vo/indicadores/comparativo_custo.dart';
import 'package:cooperado_minha_unimed/shared/vo/indicadores/daily_attendance.dart';
import 'package:cooperado_minha_unimed/shared/vo/indicadores/production_history.dart';
import 'package:cooperado_minha_unimed/shared/vo/indicadores/service_historic.vo.dart';
import 'package:http_client/exceptions/exceptions.dart';
import 'package:http_client/http_client.dart';
import 'package:intl/intl.dart';

class IndicadoresApi {
  final UnimedHttpClient httpClient;

  IndicadoresApi(this.httpClient);

  final logger = UnimedLogger(className: 'IndicadoresApi');
  final Duration timeout = const Duration(milliseconds: 250);

  Future<RetornoComparativoAtendimento> getComparativoAtendimentos(
      {required String dataInicio, required String dataFim}) async {
    UserCredentials? credentials =
        await Locator.instance!.get<AuthApi>().getCredentials();

    final token = credentials != null
        ? await User.createToken(credentials: credentials)
        : "";

    Map<String, Object> body = {"dataInicio": dataInicio, "dataFim": dataFim};

    try {
      final url =
          '${FlavorConfig.instance!.values.portal.url}comparativo-atendimentos?token=$token';
      final response = await httpClient.post(Uri.parse(url),
          body: jsonEncode(body),
          headers: {"Content-Type": "application/json"});

      if (response.statusCode == 200) {
        final data = RetornoComparativoAtendimento.fromJson(
            jsonDecode(utf8.decode(response.bodyBytes))['retorno']);
        logger.d(
            "getComparativoAtendimentos success list prest ${data.atendimentosPrestador!.length} | list espec ${data.atendimentosEspecialidade!.length}");

        return data;
      } else {
        final message = jsonDecode(response.body)['mensagem'];

        logger.e(
            'getComparativoAtendimentos statusCode : ${response.statusCode} ${response.body}');
        throw IndicadoresException('$message}');
      }
    } catch (ex) {
      logger.e('getComparativoAtendimentos exception : $ex');
      throw IndicadoresException('$ex');
    }
  }

  Future<RetornoComparativoCustoVO> getComparativoCusto(
      {required DateTime dataInicio, required DateTime dataFim}) async {
    UserCredentials? credentials =
        await Locator.instance!.get<AuthApi>().getCredentials();

    final token = credentials != null
        ? await User.createToken(credentials: credentials)
        : "";

    try {
      final tokenPerfilApps =
          await Locator.instance!.get<AuthApi>().tokenPerfilApps();
      final startDate = DateFormat("dd-MM-yyyy").format(dataInicio);
      final endDate = DateFormat("dd-MM-yyyy").format(dataFim);

      final url =
          '${FlavorConfig.instance!.values.profilePermissions.url}app/cooperado/comparativo-custos-atendimento/inicio/$startDate/fim/$endDate?tokenPortal=$token&crm=${credentials?.crm}';

      final response = await httpClient.get(
        Uri.parse(url),
        headers: {
          "Content-Type": "application/json",
          "Authorization": "Bearer $tokenPerfilApps",
        },
      );

      final data = jsonDecode(utf8.decode(response.bodyBytes));
      if (response.statusCode == 200) {
        final result = RetornoComparativoCustoVO.fromJson(data['retorno']);
        logger.d(
            "getComparativoCusto success especialidade : ${result.getValorEspecialdade()} | valor prest ${result.getValorPrestador()}");
        return result;
      } else {
        final message = data['mensagem'] ??
            data['message'] ??
            'Não foi possível no momento.';
        logger.e(
            'getComparativoCusto statusCode : ${response.statusCode} ${response.body}');
        throw IndicadoresException('$message');
      }
    } on UnimedException catch (ex) {
      logger.e('getComparativoCusto ${ex.runtimeType}: $ex');
      throw TransparenciaException(ex.message);
    } catch (ex) {
      logger.e('getComparativoCusto exception : $ex');
      throw IndicadoresException('Não foi possível no momento.');
    }
  }

  Future<List<ResponseDailyAttendance>> getDailyAttendance(
      {required String dateBegin, required String dateEnd}) async {
    try {
      UserCredentials? credentials =
          await Locator.instance!.get<AuthApi>().getCredentials();
      final token = credentials != null
          ? await User.createToken(credentials: credentials)
          : "";
      final tokenPerfilApps =
          await Locator.instance!.get<AuthApi>().tokenPerfilApps();

      final url =
          '${FlavorConfig.instance!.values.profilePermissions.url}app/cooperado/atendimentos-diarios/inicio/$dateBegin/fim/$dateEnd?tokenPortal=$token&crm=${credentials?.crm}';
      final response = await httpClient.get(Uri.parse(url), headers: {
        "Content-Type": "application/json",
        "Authorization": "Bearer $tokenPerfilApps",
      });

      final data = jsonDecode(utf8.decode(response.bodyBytes));

      if (response.statusCode == 200) {
        final collection = (data["retorno"] as List)
            .map<ResponseDailyAttendance>(
                (e) => ResponseDailyAttendance.fromJson(e))
            .toList();
        logger.d('getDailyAttendance success list ${collection.length}');
        return collection;
      } else if (response.statusCode == 404) {
        logger.i(
            'getDailyAttendance statusCode : ${response.statusCode} - ${response.body}');
        return [];
      } else {
        final message = data['mensagem'] ??
            data['message'] ??
            'Não foi possível no momento.';
        logger.e(
            'getDailyAttendance statusCode : ${response.statusCode} ${response.body}');
        throw IndicadoresException('$message');
      }
    } on UnimedException catch (ex) {
      logger.e('getDailyAttendance ${ex.runtimeType}: $ex');
      throw IndicadoresException(ex.message);
    } catch (ex) {
      logger.e('getDailyAttendance exception : $ex');
      throw IndicadoresException('Não foi possível no momento.');
    }
  }

  Future<ResponseServiceHistoric> getServicesHistoric(
      DateTime lastProduction) async {
    try {
      final credentials =
          await Locator.instance!.get<AuthApi>().getCredentials();
      final tokenPerfilApps =
          await Locator.instance!.get<AuthApi>().tokenPerfilApps();

      DateTime dateInit = lastProduction.subtract(const Duration(days: 150));
      final mesInit = DateFormat("MM").format(dateInit);
      final anoInit = DateFormat("yyyy").format(dateInit);
      final mesFim = DateFormat("MM").format(lastProduction);
      final anoFim = DateFormat("yyyy").format(lastProduction);

      final url =
          '${FlavorConfig.instance!.values.profilePermissions.url}app/cooperado/historico-quantidade-servico/mes-ini/$mesInit/ano-ini/$anoInit/mes-fim/$mesFim/ano-fim/$anoFim/crm/${credentials!.crm}';

      final response = await httpClient.get(
        Uri.parse(url),
        headers: {
          "Content-Type": "application/json",
          "Authorization": "Bearer $tokenPerfilApps",
        },
      );

      final data = jsonDecode(utf8.decode(response.bodyBytes));

      if (response.statusCode == 200) {
        final attendanceComparativeVO =
            ResponseServiceHistoric.fromJson(data["retorno"]);

        logger.d(
            'getServicesHistoric success list espec ${attendanceComparativeVO.mediaAtendimentosEspecialidade!.length} | list prest ${attendanceComparativeVO.mediaAtendimentosPrestador!.length}');

        for (HistoricoServico element
            in attendanceComparativeVO.mediaAtendimentosPrestador!) {
          final ServiceHistoricVO serviceVO =
              ServiceHistoricVO(date: element.mesAno, procedures: []);
          if (serviceVO.date == element.mesAno) {
            serviceVO.procedures!.add(Procedure(
                qtd: double.parse(element.total!), type: element.tipo));
          }
        }

        return attendanceComparativeVO;
      } else {
        final message = data['mensagem'] ??
            data['message'] ??
            'Não foi possível no momento.';

        logger.e(
            'getServicesHistoric statusCode : ${response.statusCode} ${response.body}');
        throw IndicadoresException('$message');
      }
    } on ServiceTimeoutException catch (ex) {
      logger.e('getServicesHistoric exception ServiceTimeoutException: $ex');
      throw IndicadoresException(
          "Não foi possível atender sua solicitação no momento. Tente novamente mais tarde.");
    } on IndicadoresException catch (ex) {
      logger.e(
          'getServicesHistoric exception IndicadoresException: ${ex.message}');

      throw IndicadoresException(
          'Serviço indisponível no momento. Tente novamente mais tarde.');
    } on NoInternetException catch (ex) {
      logger.e('getServicesHistoric exception NoInternetException: $ex');
      throw IndicadoresException('Você está sem internet no momento.');
    } catch (ex) {
      logger.e('getServicesHistoric exception catch: $ex');
      throw IndicadoresException('Não foi possível no momento.');
    }
  }

  Future<List<ProductionHistoryVO>> getProductionHistory(
      DateTime lastProduction) async {
    UserCredentials? credentials =
        await Locator.instance!.get<AuthApi>().getCredentials();

    final token = credentials != null
        ? await User.createToken(credentials: credentials)
        : "";

    final tokenPerfilApps =
        await Locator.instance!.get<AuthApi>().tokenPerfilApps();

    try {
      //Para setar a dataInicial para aproxidamente 6 meses anterior a data da ultima producao
      DateTime dateInit = lastProduction.subtract(const Duration(days: 150));

      final url =
          '${FlavorConfig.instance!.values.profilePermissions.url}app/cooperado/relatorio-producao-periodo/inicio/${DateFormat("dd-MM-yyyy").format(dateInit)}/fim/${DateFormat("dd-MM-yyyy").format(lastProduction)}/tipo/P?tokenPortal=$token&crm=${credentials?.crm}';

      final response = await httpClient.get(
        Uri.parse(url),
        headers: {
          "Content-Type": "application/json",
          "Authorization": "Bearer $tokenPerfilApps",
        },
      );

      final data = jsonDecode(utf8.decode(response.bodyBytes));

      if (response.statusCode == 200) {
        final collection = (data["retorno"] as List)
            .map<ProductionHistoryVO>((e) => ProductionHistoryVO.fromJson(e))
            .toList();
        logger.d('getProductionHistory success list: ${collection.length}');
        return collection;
      } else {
        final message = data['mensagem'] ??
            data['message'] ??
            'Não foi possível no momento.';
        logger.e(
            'getProductionHistory statusCode : ${response.statusCode} - ${response.body}');

        throw IndicadoresException('$message');
      }
    } on ServiceTimeoutException catch (ex) {
      logger.e('getProductionHistory exception ServiceTimeoutException: $ex');
      throw IndicadoresException(
          "Não foi possível atender sua solicitação no momento. Tente novamente mais tarde.");
    } on IndicadoresException catch (ex) {
      logger.e(
          'getProductionHistory exception IndicadoresException: ${ex.message}');
      rethrow;
    } on NoInternetException catch (ex) {
      logger.e('getProductionHistory exception NoInternetException: $ex');
      throw IndicadoresException('Você está sem internet no momento.');
    } catch (ex) {
      logger.e('getProductionHistory catch exception : $ex');
      throw IndicadoresException('Não foi possível no momento.');
    }
  }

  Future<String?> getLastProduction() async {
    UserCredentials? credentials =
        await Locator.instance!.get<AuthApi>().getCredentials();

    final token = credentials != null
        ? await User.createToken(credentials: credentials)
        : "";
    final tokenPerfilApps =
        await Locator.instance!.get<AuthApi>().tokenPerfilApps();

    try {
      final url =
          '${FlavorConfig.instance!.values.profilePermissions.url}app/cooperado/producao-medica/ultima-data?tokenPortal=$token&crm=${credentials?.crm}';
      final response = await httpClient.get(
        Uri.parse(url),
        headers: {
          "Content-Type": "application/json",
          "Authorization": "Bearer $tokenPerfilApps",
        },
      );

      final data = jsonDecode(utf8.decode(response.bodyBytes));

      if (response.statusCode == 200) {
        logger.d('getLastProduction success body: ${response.body}');

        return data['retorno']['dataProducao'];
      } else {
        final message = data['mensagem'] ??
            data['message'] ??
            'Não foi possível no momento';
        logger.e(
            'getLastProduction statusCode : ${response.statusCode} - ${response.body}');
        throw IndicadoresException('$message');
      }
    } on UnimedException catch (e) {
      throw IndicadoresException(e.message);
    } catch (ex) {
      logger.e('getLastProduction exception : $ex');
      throw IndicadoresException(
          'Não foi possível atender sua solicitação no momento. Tente novamente mais tarde.');
    }
  }
}
