import 'package:auto_size_text/auto_size_text.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:cooperado_minha_unimed/bloc/fiscal-council/fiscal_council_cubit.dart';
import 'package:cooperado_minha_unimed/colors.dart';
import 'package:cooperado_minha_unimed/models/fiscal_council.model.dart';
import 'package:cooperado_minha_unimed/screens/fiscal-council/image_member.dart';
import 'package:cooperado_minha_unimed/shared/screen-transitions/fade.transition.dart';
import 'package:cooperado_minha_unimed/shared/widgets/error-banner/error_banner.component.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';

class CardMembers extends StatefulWidget {
  const CardMembers({super.key});

  @override
  CardMembersState createState() => CardMembersState();
}

class CardMembersState extends State<CardMembers> {
  @override
  void initState() {
    context.read<FiscalCouncilCubit>().getCouncil();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(top: 8.0),
      child: Card(
        child: Center(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: <Widget>[
              const Padding(
                padding: EdgeInsets.all(8.0),
                child: Text(
                  "Membros do conselho",
                  style:
                      TextStyle(color: CooperadoColors.blackText, fontSize: 16),
                ),
              ),
              Padding(
                padding: const EdgeInsets.all(8.0),
                child: _membersBloc(),
              )
            ],
          ),
        ),
      ),
    );
  }

  Widget _membersBloc() {
    return BlocBuilder<FiscalCouncilCubit, FiscalCouncilState>(
      builder: (context, state) {
        if (state is LoadingGetCouncil) {
          return const SpinKitCircle(
            color: CooperadoColors.tealGreen,
          );
        } else if (state is ErrorGetCouncil) {
          return ErrorBanner(message: state.message);
        } else if (state is DoneGetCouncil) {
          return _gridConselho(state.list!);
        } else {
          return Container();
        }
      },
    );
  }

  Widget _gridConselho(List<FiscalCouncil> list) {
    return Center(
        child: GridView.count(
      crossAxisCount: 2,
      controller: ScrollController(keepScrollOffset: false),
      shrinkWrap: true,
      scrollDirection: Axis.vertical,
      children: list.map((FiscalCouncil value) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: <Widget>[
            Flexible(
              flex: 10,
              child: value.imagem == ''
                  ? const Icon(
                      Icons.account_circle,
                      size: 70,
                      color: CooperadoColors.grayLight,
                    )
                  : _thumbImage(value),
            ),
            const SizedBox(height: 10),
            Flexible(
              flex: 3,
              child: AutoSizeText(
                value.nome,
                textAlign: TextAlign.center,
                style: const TextStyle(color: CooperadoColors.blackText),
                minFontSize: 8,
                maxFontSize: 14,
              ),
            ),
            Flexible(
              flex: 3,
              child: AutoSizeText(
                value.cargo,
                textAlign: TextAlign.center,
                style: const TextStyle(color: CooperadoColors.grayDark),
                minFontSize: 6,
                maxFontSize: 12,
                maxLines: 3,
                overflow: TextOverflow.ellipsis,
              ),
            ),
            Flexible(
              flex: 3,
              child: AutoSizeText(
                '${value.crm} - ${value.especialidade}',
                textAlign: TextAlign.center,
                style: const TextStyle(color: CooperadoColors.grayDark),
                minFontSize: 6,
                maxFontSize: 12,
                maxLines: 3,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        );
      }).toList(),
    ));
  }

  Widget _thumbImage(FiscalCouncil member) {
    return InkWell(
      child: CachedNetworkImage(
        imageUrl: member.thumb!,
        placeholder: (context, url) => const CircularProgressIndicator(),
      ),
      onTap: () {
        Navigator.push(
          context,
          FadeRoute(
            page: ImageMember(
              imageUrl: member.imagem ?? "",
              title: member.nome,
              subtitle: member.cargo,
            ),
          ),
        );
      },
    );
  }
}
