part of 'forgot_password_cubit.dart';

abstract class ForgotPasswordState extends Equatable {
  const ForgotPasswordState();

  @override
  List<Object?> get props => [];
}

class ForgotPasswordInitial extends ForgotPasswordState {}

class LoadingForgotPasswordState extends ForgotPasswordState {
  @override
  List<Object> get props => [];
}

class ErrorForgotPasswordState extends ForgotPasswordState {
  final String message;

  @override
  List<Object> get props => [message];

  const ErrorForgotPasswordState(this.message);
}

class SucessForgotPassworState extends ForgotPasswordState {
  final String message;

  @override
  List<Object> get props => [message];

  const SucessForgotPassworState(this.message);
}

class LoadingGetPasswordRulesState extends ForgotPasswordState {
  @override
  List<Object> get props => [];
}

class DoneGetPasswordRulesState extends ForgotPasswordState {
  final List<PasswordRulesModel> rules;

  @override
  List<Object> get props => [];

  const DoneGetPasswordRulesState({required this.rules});
}

class ErrorRulesState extends ForgotPasswordState {
  final String message;

  @override
  List<Object> get props => [message];

  const ErrorRulesState(this.message);
}
