// ignore_for_file: unnecessary_statements

import 'dart:async';

import 'package:flutter/material.dart';

class ShowUp extends StatefulWidget {
  final Widget child;
  final int? delay;
  final int? duration;
  final Function? onAnimationEnd;

  const ShowUp(
      {super.key,
      required this.child,
      this.delay,
      this.duration,
      this.onAnimationEnd});

  @override
  ShowUpState createState() => ShowUpState();
}

class ShowUpState extends State<ShowUp> with TickerProviderStateMixin {
  late AnimationController _animController;
  late Animation<Offset> _animOffset;
  bool _isDisposed = false;

  @override
  void initState() {
    super.initState();
    if (_isDisposed || !mounted) {
      return;
    } else {
      _animController = AnimationController(
          vsync: this,
          duration: Duration(milliseconds: widget.duration ?? 350));
      final curve =
          CurvedAnimation(curve: Curves.decelerate, parent: _animController);
      _animOffset =
          Tween<Offset>(begin: const Offset(0.0, 0.35), end: Offset.zero)
              .animate(curve);
      _animController.addStatusListener((status) {
        if (widget.onAnimationEnd != null &&
            status == AnimationStatus.completed) {
          widget.onAnimationEnd != null ? widget.onAnimationEnd!() : () => null;
        }
      });

      if (widget.delay == null) {
        _animController.forward();
      } else {
        try {
          Timer(Duration(milliseconds: widget.delay ?? 0), () {
            _animController.forward();
          });
        } catch (e) {
          debugPrint('Erro ao realizar animação: ${e.toString()}');
        }
      }
    }
  }

  @override
  void dispose() {
    _isDisposed = true;
    _animController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return FadeTransition(
      opacity: _animController,
      child: SlideTransition(
        position: _animOffset,
        child: widget.child,
      ),
    );
  }
}
