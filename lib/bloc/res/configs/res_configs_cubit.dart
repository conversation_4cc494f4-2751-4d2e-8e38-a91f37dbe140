import 'package:cooperado_minha_unimed/models/res/res_configs_model.dart';
import 'package:cooperado_minha_unimed/shared/api/graphql-res.api.dart';
import 'package:cooperado_minha_unimed/shared/locator.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

part 'res_configs_state.dart';

class ResConfigCubit extends Cubit<ResConfigsState> {
  ResConfigCubit() : super(InitialResConfigsState());

  late ResConfigModel _resConfigModel;
  ResConfigModel get resConfigModel => _resConfigModel;

  void getResConfigs({required String crm, required String cpf}) async {
    try {
      emit(LoadingResConfigsState());

      _resConfigModel = await Locator.instance!<ResGraphQlApi>().resConfigs(
        crm: crm,
        cpf: cpf,
      );

      emit(LoadedResConfigsState(resConfigModel: _resConfigModel));
    } catch (e) {
      emit(ErrorResConfigsState(message: e.toString()));
    }
  }
}
