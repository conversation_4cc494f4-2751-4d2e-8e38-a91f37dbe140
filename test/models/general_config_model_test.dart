import 'package:cooperado_minha_unimed/models/general_config_model.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  late GeneralConfigModel generalConfigModel;
  setUpAll(() => {
        generalConfigModel = GeneralConfigModel(
          home: Home(
            buttons: Buttons(
              benefits: true,
              onlineConsultation: false,
              myIndicators: false,
              medicalProduction: false,
              services: false,
              transparency: false,
              terms: false,
              guide: false,
              direction: false,
              fiscalCouncil: false,
              news: false,
              consultometer: false,
              personalAssistant: true,
              economicIndicators: true,
              financial: true,
              clinicalJourney: true,
              officeNotices: true,
              ecard: true,
              insurance: true,
              financialServiceUniverse: true,
              clubMaisVantagens: true,
              res: Res(
                resInternal: false,
                resExternal: ResExternal(
                  resExternal: false,
                  resExternalService: false,
                  resExternalAllergy: false,
                  resExternalProcedure: false,
                  resExternalDocument: false,
                  resExternalAlerts: false,
                  resExternalDiagnostic: false,
                  resExternalResultExam: false,
                ),
              ),
              supportCooperative: true,
            ),
            boxes: Boxes(
              cooperative: true,
              medicalProductionReport: true,
              news: true,
              productionComparison: true,
              unimedIndicators: true,
              feeMonitoring: true,
            ),
            profile: Profile(
              myData: true,
            ),
          ),
          messages: [
            Messages(id: "1", message: "message test"),
          ],
          links: Links(
            coopMais: "https://www.google.com",
            plataformaMaisUnimed: "https://www.bing.com/",
            clubeMaisVantagens: 'https://www.unimedfortaleza.com.br/mais',
          ),
        ),
      });
  group('Test GeneralConfigModel', () {
    test('Constructor must assign correct values ​​to properties', () {
      const String message = 'Message example';
      const String id = '123';
      final config = GeneralConfigModel(
          home: Home(
            buttons: Buttons(
              supportCooperative: true,
              benefits: true,
              onlineConsultation: true,
              myIndicators: true,
              medicalProduction: true,
              officeNotices: true,
              services: true,
              transparency: true,
              terms: true,
              guide: true,
              direction: true,
              fiscalCouncil: true,
              news: true,
              consultometer: true,
              personalAssistant: true,
              economicIndicators: true,
              financial: true,
              clinicalJourney: true,
              ecard: true,
              insurance: true,
              financialServiceUniverse: true,
              clubMaisVantagens: true,
              res: Res(
                resInternal: true,
                resExternal: ResExternal(
                  resExternal: true,
                  resExternalService: true,
                  resExternalAllergy: true,
                  resExternalProcedure: true,
                  resExternalDocument: true,
                  resExternalAlerts: true,
                  resExternalDiagnostic: true,
                  resExternalResultExam: true,
                ),
              ),
            ),
            boxes: Boxes(
              cooperative: true,
              medicalProductionReport: true,
              news: true,
              productionComparison: true,
              unimedIndicators: true,
              feeMonitoring: true,
            ),
            profile: Profile(
              myData: true,
            ),
          ),
          messages: [Messages(message: message, id: id)],
          links: Links(
            coopMais: "https://www.google.com",
            plataformaMaisUnimed: "https://www.bing.com/",
            clubeMaisVantagens: 'https://www.unimedfortaleza.com.br/mais',
          ));

      expect(config.messages![0].message, equals(message));
      expect(config.messages![0].id, equals(id));
      expect(config.links!.plataformaMaisUnimed, equals("https://www.bing.com/"));
      expect(config.links!.coopMais, equals("https://www.google.com"));
      expect(config.links!.clubeMaisVantagens, equals("https://www.unimedfortaleza.com.br/mais"));
    });
  });

  test("test return thereIsAnInvisibleButton = true when at least one button is visible ", () {
    final generalConfigModel = GeneralConfigModel(
      home: Home(
        buttons: Buttons(
          supportCooperative: true,
          benefits: false,
          onlineConsultation: false,
          myIndicators: false,
          medicalProduction: false,
          services: false,
          officeNotices: true,
          transparency: false,
          terms: false,
          guide: false,
          direction: false,
          fiscalCouncil: false,
          news: false,
          consultometer: false,
          personalAssistant: true,
          economicIndicators: false,
          financial: false,
          clinicalJourney: false,
          ecard: true,
          insurance: true,
          financialServiceUniverse: true,
          clubMaisVantagens: true,
          res: Res(
            resInternal: false,
            resExternal: ResExternal(
              resExternal: false,
              resExternalService: false,
              resExternalAllergy: false,
              resExternalProcedure: false,
              resExternalDocument: false,
              resExternalAlerts: false,
              resExternalDiagnostic: false,
              resExternalResultExam: false,
            ),
          ),
        ),
        boxes: Boxes(
          cooperative: true,
          medicalProductionReport: true,
          news: true,
          productionComparison: true,
          unimedIndicators: true,
          feeMonitoring: true,
        ),
        profile: Profile(
          myData: true,
        ),
      ),
    );

    expect(generalConfigModel.home.buttons.thereIsAnInvisibleButton(), true);
  });

  test("test return thereIsAnInvisibleButton = false when all buttons are set to false ", () {
    final generalConfigModel = GeneralConfigModel(
      home: Home(
        buttons: Buttons(
          supportCooperative: true,
          benefits: false,
          onlineConsultation: false,
          myIndicators: false,
          officeNotices: false,
          medicalProduction: false,
          services: false,
          transparency: false,
          terms: false,
          guide: false,
          direction: false,
          fiscalCouncil: false,
          news: false,
          consultometer: false,
          personalAssistant: false,
          economicIndicators: false,
          financial: true,
          clinicalJourney: true,
          ecard: true,
          insurance: true,
          financialServiceUniverse: true,
          clubMaisVantagens: true,
          res: Res(
            resInternal: false,
            resExternal: ResExternal(
              resExternal: false,
              resExternalService: false,
              resExternalAllergy: false,
              resExternalProcedure: false,
              resExternalDocument: false,
              resExternalAlerts: false,
              resExternalDiagnostic: false,
              resExternalResultExam: false,
            ),
          ),
        ),
        boxes: Boxes(
          cooperative: true,
          medicalProductionReport: true,
          news: true,
          productionComparison: true,
          unimedIndicators: true,
          feeMonitoring: true,
        ),
        profile: Profile(
          myData: true,
        ),
      ),
    );

    expect(generalConfigModel.home.buttons.thereIsAnInvisibleButton(), false);
  });

  test(
      "test return buttonListExpandedButtonVisibility = false if there are at least three visible buttons ",
      () {
    final generalConfigModel = GeneralConfigModel(
      home: Home(
        buttons: Buttons(
          supportCooperative: true,
          benefits: false,
          onlineConsultation: false,
          myIndicators: false,
          medicalProduction: false,
          services: false,
          transparency: false,
          officeNotices: true,
          terms: false,
          guide: false,
          direction: false,
          fiscalCouncil: false,
          news: false,
          consultometer: false,
          personalAssistant: true,
          economicIndicators: true,
          financial: true,
          clinicalJourney: false,
          ecard: true,
          insurance: true,
          financialServiceUniverse: true,
          clubMaisVantagens: true,
          res: Res(
            resInternal: false,
            resExternal: ResExternal(
              resExternal: false,
              resExternalService: false,
              resExternalAllergy: false,
              resExternalProcedure: false,
              resExternalDocument: false,
              resExternalAlerts: false,
              resExternalDiagnostic: false,
              resExternalResultExam: false,
            ),
          ),
        ),
        boxes: Boxes(
          cooperative: true,
          medicalProductionReport: true,
          news: true,
          productionComparison: true,
          unimedIndicators: true,
          feeMonitoring: true,
        ),
        profile: Profile(
          myData: true,
        ),
      ),
    );

    expect(generalConfigModel.home.buttons.buttonListExpandedButtonVisibility(), false);
  });

  test(
      "test return buttonListExpandedButtonVisibility = true if you have more than three buttons visible",
      () {
    final generalConfigModel = GeneralConfigModel(
      home: Home(
        buttons: Buttons(
          supportCooperative: true,
          benefits: false,
          officeNotices: true,
          onlineConsultation: false,
          myIndicators: false,
          medicalProduction: false,
          services: false,
          transparency: false,
          terms: false,
          guide: false,
          direction: false,
          fiscalCouncil: true,
          news: true,
          consultometer: true,
          personalAssistant: true,
          economicIndicators: true,
          financial: true,
          clinicalJourney: true,
          ecard: true,
          insurance: true,
          financialServiceUniverse: true,
          clubMaisVantagens: true,
          res: Res(
            resInternal: false,
            resExternal: ResExternal(
              resExternal: false,
              resExternalService: false,
              resExternalAllergy: false,
              resExternalProcedure: false,
              resExternalDocument: false,
              resExternalAlerts: false,
              resExternalDiagnostic: false,
              resExternalResultExam: false,
            ),
          ),
        ),
        boxes: Boxes(
          cooperative: true,
          medicalProductionReport: true,
          news: true,
          productionComparison: true,
          unimedIndicators: true,
          feeMonitoring: true,
        ),
        profile: Profile(
          myData: true,
        ),
      ),
    );

    expect(generalConfigModel.home.buttons.buttonListExpandedButtonVisibility(), true);
  });

  group("Json test", () {
    test("Should be return instance of retorn generalConfigModel to json", () {
      expect(generalConfigModel.toJson(), isInstanceOf<Map<String, dynamic>>());
    });
    test("Should be return instance of retorn generalConfigModel from json", () {
      final json = generalConfigModel.toJson();
      expect(GeneralConfigModel.fromJson(json), isInstanceOf<GeneralConfigModel>());
    });
  });
}
