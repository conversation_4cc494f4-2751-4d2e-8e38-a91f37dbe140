import 'package:cooperado_minha_unimed/models/glosa_resource/file-attach.model.dart';
import 'package:equatable/equatable.dart';

abstract class GlosaResourceAddFileState extends Equatable {}

class InitialState extends GlosaResourceAddFileState {
  @override
  List<Object> get props => [];
}

class LoadingState extends GlosaResourceAddFileState {
  @override
  List<Object> get props => [];
}

class LoadingDocumentState extends GlosaResourceAddFileState {
  final String message;
  final int index;

  @override
  List<Object> get props => [index, message];

  LoadingDocumentState({required this.index, required this.message});
}

class LoadingBase64State extends GlosaResourceAddFileState {
  final int index;

  @override
  List<Object> get props => [index];

  LoadingBase64State({required this.index});
}

class FileSendedState extends GlosaResourceAddFileState {
  final int index;

  @override
  List<Object> get props => [index];

  FileSendedState({required this.index});
}

class ErrorFileSendedState extends GlosaResourceAddFileState {
  final String message;

  @override
  List<Object> get props => [message];

  ErrorFileSendedState({required this.message});
}

class ErrorFileLoadingState extends GlosaResourceAddFileState {
  final String message;

  @override
  List<Object> get props => [message];

  ErrorFileLoadingState({required this.message});
}

class ErrorSendedState extends GlosaResourceAddFileState {
  final int index;

  @override
  List<Object> get props => [index];

  ErrorSendedState({required this.index});
}

class FinishSendAllFileState extends GlosaResourceAddFileState {
  final List<FileAttach?> attachments;

  @override
  List<Object> get props => [attachments];

  FinishSendAllFileState({required this.attachments});
}

class ErrorState extends GlosaResourceAddFileState {
  final String message;

  @override
  List<Object> get props => [message];

  ErrorState(this.message);
}

class DoneState extends GlosaResourceAddFileState {
  final List<FileAttach?> attachments;

  @override
  List<Object> get props => [attachments];

  DoneState({required this.attachments});
}

class GlosaResourceAttachmentErrorState extends GlosaResourceAddFileState {
  final String message;
  GlosaResourceAttachmentErrorState({required this.message});
  @override
  List<Object> get props => [message];
}
