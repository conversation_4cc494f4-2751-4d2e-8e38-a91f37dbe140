import 'package:cooperado_minha_unimed/shared/api/transparencia.api.dart';
import 'package:cooperado_minha_unimed/shared/locator.dart';
import 'package:cooperado_minha_unimed/shared/vo/transparencia/quantitativo_beneficiarios.vo.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

part 'beneficiaries_quantitative_state.dart';

class BeneficiariesQuantitativeCubit
    extends Cubit<BeneficiariesQuantitativeState> {
  BeneficiariesQuantitativeCubit()
      : super(InititalBeneficiariesQuantitativeState());

  getBeneficiariesQuantitative() async {
    try {
      emit(LoadingBeneficiariesQuantitativeState());
      emit(LoadedBeneficiariesQuantitativeState(await Locator.instance!
              <TransparenciaApi>()
          .getQuantitativoBeneficiarios()));
    } catch (e) {
      emit(ErrorBeneficiariesQuantitativeState(e.toString()));
    }
  }
}
