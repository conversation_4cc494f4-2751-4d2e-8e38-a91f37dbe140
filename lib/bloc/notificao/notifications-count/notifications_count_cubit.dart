import 'package:cooperado_minha_unimed/bloc/notificao/notifications-count/notifications_count_state.dart';
import 'package:cooperado_minha_unimed/shared/api/notificacao-graphql.api.dart';
import 'package:cooperado_minha_unimed/shared/locator.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class NotificationsCountCubit extends Cubit<NotificationsCountState> {
  NotificationsCountCubit() : super(InitialNotificationsCountState());
  int? _notificationsCount;

  void getNotificationsCount({
    required String codPrestador,
    int? page,
    int? perPage,
    DateTime? startDateTime,
    DateTime? endDateTime,
  }) async {
    emit(NotificationsCountStateLoading());
    try {
      final response = await Locator.instance!<GraphQlApiNotificacao>().getListNotificacoes(
        codPrestador: codPrestador,
        page: page,
        perPage: perPage,
        startDateTime: startDateTime,
        endDateTime: endDateTime,
      );

      _notificationsCount = (response.notifications ?? []).where((element) => element.readAt == null).length;

      emit(NotificationsCountStateLoaded(
        notificationsCount: _notificationsCount ?? 0,
      ));
    } catch (e) {
      emit(NotificationsCountStateError(message: e.toString()));
    }
  }

  void updateNotificationCount() async {
    emit(NotificationsCountStateLoading());
    await Future.delayed(const Duration(milliseconds: 300));
    _notificationsCount = _notificationsCount! - 1;
    emit(NotificationsCountStateLoaded(
      notificationsCount: _notificationsCount ?? 0,
    ));
  }
}
