import 'package:cooperado_minha_unimed/bloc/sensitive-data/sensitive_data_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/sensitive-data/sensitive_data_state.dart';
import 'package:cooperado_minha_unimed/colors.dart';
import 'package:cooperado_minha_unimed/shared/vo/transparencia/producao_medica_resumida.vo.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class DetailsScreen extends StatefulWidget {
  final RetornoProducaoMedica? retornoProducaoMedica;
  const DetailsScreen({super.key, required this.retornoProducaoMedica});
  @override
  DetailsScreenState createState() => DetailsScreenState();
}

class DetailsScreenState extends State<DetailsScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text("Detalhes"),
        backgroundColor: CooperadoColors.tealGreenDark,
        actions: [
           Padding(
             padding: const EdgeInsets.only(right: 15.0),
             child: IconButton(
                       icon: BlocBuilder<SensitiveDataCubit, SensitiveDataState>(
              builder: (context, state) {
                return Icon(
                  state.isSensitiveDataVisible ? Icons.visibility : Icons.visibility_off,
                );
              },
                       ),
                       onPressed: () {
              context.read<SensitiveDataCubit>().toggleSensitiveDataVisibility();
                       },
                     ),
           ),
        ],
      ),
      body: SafeArea(
        child: SingleChildScrollView(
          child: Card(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: <Widget>[
                const Row(),
                const Padding(
                  padding: EdgeInsets.only(top: 8.0, left: 8.0),
                  child: Text(
                    "Total",
                    style: TextStyle(
                      color: CooperadoColors.grayDark,
                      fontSize: 18,
                    ),
                  ),
                ),
                BlocBuilder<SensitiveDataCubit, SensitiveDataState>(
  builder: (context, sensitiveState) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 8.0),
      child: Text(
        sensitiveState.isSensitiveDataVisible
            ? widget.retornoProducaoMedica?.meses?.first.totalFormatted ?? ''
            : '*****',
        style: const TextStyle(
          color: CooperadoColors.limaColor,
          fontWeight: FontWeight.bold,
          fontSize: 20,
        ),
      ),
    );
  },
),
                _table()
              ],
            ),
          ),
        ),
      ),
    );
  }

 Widget _table() {
  return BlocBuilder<SensitiveDataCubit, SensitiveDataState>(
    builder: (context, sensitiveState) {
      return SizedBox(
        width: MediaQuery.of(context).size.width,
        child: DataTable(
          columns: const <DataColumn>[
            DataColumn(
              label: Text(
                'Descrição',
                style: TextStyle(
                  color: CooperadoColors.blackText,
                  fontStyle: FontStyle.italic,
                ),
              ),
            ),
            DataColumn(
              label: Text(
                'Valor',
                style: TextStyle(
                  color: CooperadoColors.blackText,
                  fontStyle: FontStyle.italic,
                ),
              ),
            ),
          ],
          rows: (widget.retornoProducaoMedica?.meses?.first.producoes ?? [])
              .map(
                (prod) => DataRow(
                  cells: [
                    DataCell(
                      Text(
                        prod.descricao ?? '',
                        softWrap: true,
                        style: const TextStyle(color: CooperadoColors.grayDark),
                      ),
                    ),
                    DataCell(
                      Text(
                        sensitiveState.isSensitiveDataVisible
                            ? prod.valorFormatted
                            : '*****',
                        style: const TextStyle(
                          color: CooperadoColors.grayDark,
                          fontWeight: FontWeight.bold,
                        ),
                        maxLines: 1,
                      ),
                    )
                  ],
                ),
              )
              .toList(),
        ),
      );
    },
  );
}
}
