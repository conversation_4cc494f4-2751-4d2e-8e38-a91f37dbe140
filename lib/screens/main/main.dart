// ignore_for_file: use_build_context_synchronously

import 'dart:async';
import 'dart:io';

import 'package:cooperado_minha_unimed/bloc/auth/agree_terms/agree_terms_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/auth/auth_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/honorarios-quimioterapia/honorarios_quimioterapia_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/indicators/last_production/last_production_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/news/council/council_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/notificao/notifications-count/notifications_count_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/profile/profile_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/update-version/update_version_cubit.dart';
import 'package:cooperado_minha_unimed/colors.dart';
import 'package:cooperado_minha_unimed/models/general_config_model.dart';
import 'package:cooperado_minha_unimed/screens/login.dart';
import 'package:cooperado_minha_unimed/screens/main/header.dart';
import 'package:cooperado_minha_unimed/screens/main/list_buttons.dart';
import 'package:cooperado_minha_unimed/screens/main/list_news_home.dart';
import 'package:cooperado_minha_unimed/screens/main/score-comparison/main.dart';
import 'package:cooperado_minha_unimed/screens/news/main.dart';
import 'package:cooperado_minha_unimed/screens/servicos/acompanhamento-honorarios-quimioterapia/card_acompanhamento_honorarios_quimioterapia.dart';
import 'package:cooperado_minha_unimed/screens/servicos/card_list_medicines.dart';
import 'package:cooperado_minha_unimed/screens/servicos/relatorio-producao-medica/card_relatorio_producao_medica.dart';
import 'package:cooperado_minha_unimed/screens/transparencia/card_indexes_unimed.dart';
import 'package:cooperado_minha_unimed/shared/screen-transitions/fade.transition.dart';
import 'package:cooperado_minha_unimed/shared/utils/router_observer.dart';
import 'package:cooperado_minha_unimed/shared/vo/transparencia/demostrativo_resultado.dart';
import 'package:cooperado_minha_unimed/shared/widgets/alert.dart';
import 'package:cooperado_minha_unimed/shared/widgets/alert_agree.dart';
import 'package:cooperado_minha_unimed/shared/widgets/alert_news.dart';
import 'package:cooperado_minha_unimed/shared/widgets/comparative-production/view/card_comparative_production.dart';
import 'package:cooperado_minha_unimed/shared/widgets/flavor_banner.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:url_launcher/url_launcher.dart';

class HomeScreen extends StatefulWidget {
  final Widget? child;
  final bool expand;
  final FirebaseAnalytics analytics;
  final FirebaseAnalyticsObserver observer;

  const HomeScreen({
    super.key,
    this.expand = false,
    this.child,
    required this.analytics,
    required this.observer,
  });

  @override
  HomeScreenState createState() => HomeScreenState();
}

late ScrollController scrollController;
const double expandedHight = 150.0;

class HomeScreenState extends State<HomeScreen>
    with RouteAware, SingleTickerProviderStateMixin {
  final bool _permitExit = false;
  late Boxes _configHomeBoxes;
  bool exibeCard = false;
  late ScrollController _scrollController;

  @override
  initState() {
    _checkReportHonorarioVisibility();
    _configHomeBoxes =
        context.read<AuthCubit>().modelGeneralConfigModel.home.boxes;
    scrollController = ScrollController();
    context.read<AgreeTermsCubit>().verifyAgree();
    scrollController.addListener(() => setState(() {}));
    _scrollController = ScrollController();
    _scrollController.addListener(() => setState(() {}));

    super.initState();

    widget.analytics.logScreenView(
      screenName: 'Home',
      screenClass: 'HomeScreen',
    );

    if (_configHomeBoxes.productionComparison == true) {
      context.read<LastProductionCubit>().listLastProduction();
    }

    FirebaseMessaging.onMessage.listen((RemoteMessage message) async {
      debugPrint('Handling a foreground message ${message.messageId}');
      if (message.notification != null && Platform.isAndroid) {
        debugPrint('Create Android Notification ${message.messageId}');
        const platform = MethodChannel('cooperado/sendNotification');

        Map<String, dynamic> notificationData = {
          "title": message.notification!.title,
          "content": message.notification!.body,
        };

        await platform.invokeMethod('sendNotification', notificationData);

        context.read<NotificationsCountCubit>().getNotificationsCount(
              codPrestador:
                  context.read<ProfileCubit>().user.codPrestador.toString(),
            );
      }
    });
  }

  void _checkReportHonorarioVisibility() async {
    bool? verification = await context
        .read<HonorarioCubit>()
        .getReportHonorarioCodeVisibility(
            context.read<ProfileCubit>().user.especialidades!);
    if (verification != null && verification) {
      setState(() {
        exibeCard = true;
      });
    } else {
      setState(() {
        exibeCard = false;
      });
    }

    debugPrint('Exibe HonorariosHomeButtom?: $exibeCard');
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    routeObserver.subscribe(this, ModalRoute.of(context) as PageRoute<dynamic>);
  }

  @override
  void dispose() {
    routeObserver.unsubscribe(this);
    scrollController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Future<void> didPopNext() async {
    debugPrint("========= voltei home");

    await context
        .read<CouncilCubit>()
        .getFiscalCouncil(categories: ["noticias-conselho-fiscal"], page: 1);
    await context.read<AgreeTermsCubit>().verifyAgree();
  }

  @override
  Widget build(BuildContext context) {
    return MultiBlocListener(
      listeners: [
        BlocListener<UpdateVersionCubit, UpdateVersionState>(
          listener: (context, state) {
            if (state is OutOfDateState) {
              Alert.open(
                context,
                title: 'Alerta de Versão ',
                text:
                    'Nova versão disponível : ${state.remoteVersion}\nSua Versão atual:  ${state.localVersion}',
                textButtonClose: state.forceUpdate ? 'Sair' : 'Fechar',
                callbackClose: () {
                  if (state.forceUpdate) _logout();
                },
                barrierDismissible: false,
                actions: <Widget>[
                  ElevatedButton(
                    style: ElevatedButton.styleFrom(
                      shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(5.0)),
                      backgroundColor: unimedOrange,
                      textStyle: const TextStyle(
                        color: Colors.white,
                      ),
                    ),
                    child: const Text('Atualizar'),
                    onPressed: () => _openAppStore(),
                  ),
                ],
              );
            }
          },
        ),
        BlocListener<AgreeTermsCubit, AgreeTermsState>(
            listener: (context, state) async {
          if (state is ErrorState) {
            Alert.open(context,
                title: "Não foi possivel verificar o aceite de termos",
                text: "Verifique sua conexão com a internet e tente novamente",
                callbackClose: context.read<AgreeTermsCubit>().popTerms());
          } else if (state is PopAgreeTermsState) {
          } else if (state is AlertState) {
            for (int i = 0; i < state.agreeTermsModel.length; i++) {
              int indexReverse = state.agreeTermsModel.length - i - 1;
              Future<void> displayTerm() async {
                await AlertAgree().open(context,
                    agreeTerm: ValueNotifier(false),
                    title: state.agreeTermsModel[indexReverse].title,
                    pdf: state.agreeTermsModel[indexReverse].pdf,
                    index: state.termsQuantity,
                    code: state.agreeTermsModel[indexReverse].code,
                    subtitle: state.agreeTermsModel[indexReverse].subtitle,
                    agreeTermsModel: state.agreeTermsModel[indexReverse],
                    primaryButton: () async {
                  await context.read<AgreeTermsCubit>().popTerms();
                  Navigator.pop(context);
                });
              }

              await Future.delayed(Duration(milliseconds: 300 * i));
              await displayTerm();
            }
          }
          //TODO codigo comentado para caso precise voltar a versao anterior
          // List.generate(
          //   state.agreeTermsModel.length,
          //   (index) {
          //     int indexReverse = state.agreeTermsModel.length - index - 1;
          //     return AlertAgree().open(
          //       context,
          //       agreeTerm: ValueNotifier(false),
          //       title: state.agreeTermsModel[indexReverse].title,
          //       pdf: state.agreeTermsModel[indexReverse].pdf,
          //       index: state.termsQuantity,
          //       code: state.agreeTermsModel[indexReverse].code,
          //       subtitle: state.agreeTermsModel[indexReverse].subtitle,
          //       primaryButton: () async {
          //         await context.read<AgreeTermsCubit>().popTerms();
          //         Navigator.pop(context);
          //       },
          //     );
          //   },
          // );
        }),
      ],
      child: _home(),
    );
  }

  void _logout() async {
    await context.read<AuthCubit>().signout();
    await Navigator.pushAndRemoveUntil(
        context,
        FadeRoute(
          page: LoginScreen(
            openBiometryOnInit: false,
            analytics: widget.analytics,
            observer: widget.observer,
          ),
        ),
        (route) => route.isFirst);
  }

  double get top {
    double res = expandedHight;
    if (scrollController.hasClients) {
      double offset = scrollController.offset;
      if (offset < (res - kToolbarHeight)) {
        res -= offset;
      } else {
        res = kToolbarHeight;
      }
    }
    return res;
  }

  Widget _home() {
    return FlavorBanner(
      child: PopScope(
        canPop: _permitExit,
        child: AnnotatedRegion<SystemUiOverlayStyle>(
          value: SystemUiOverlayStyle.light,
          child: Scaffold(
            backgroundColor: CooperadoColors.backgroundColor,
            body: SafeArea(
              top: false,
              child: CustomScrollView(
                controller: _scrollController,
                physics: const ClampingScrollPhysics(),
                slivers: [
                  //Header is a SliverAppBar
                  HeaderCooperado(
                    analytics: widget.analytics,
                    observer: widget.observer,
                    scrollController: _scrollController,
                  ),
                  SliverToBoxAdapter(
                      child: Column(
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: <Widget>[
                      Container(
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(20.0),
                          color: CooperadoColors.backgroundColor,
                          boxShadow: const [
                            BoxShadow(
                              color: Colors.grey,
                              offset: Offset(0.0, 1.0), //(x,y)
                              blurRadius: 6.0,
                            ),
                          ],
                        ),
                        child: Column(
                          children: <Widget>[
                            Stack(
                              clipBehavior: Clip.none,
                              alignment: Alignment.topCenter,
                              children: <Widget>[
                                Container(
                                  height: 50,
                                  decoration: const BoxDecoration(
                                      color: CooperadoColors.tealGreenDark,
                                      borderRadius: BorderRadius.only(
                                        bottomLeft: Radius.circular(40.0),
                                        bottomRight: Radius.circular(40.0),
                                      )),
                                ),
                                Padding(
                                  padding: const EdgeInsets.only(top: 0),
                                  child: ListButtons(
                                    analytics: widget.analytics,
                                    observer: widget.observer,
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                      Visibility(
                        visible: _configHomeBoxes.news,
                        child: const ListNewsHome(),
                      ),
                      Visibility(
                        visible: _configHomeBoxes.medicalProductionReport,
                        child: const Padding(
                          padding: EdgeInsets.all(8.0),
                          child: CardReportProduction(),
                        ),
                      ),
                      const Visibility(
                        visible: true,
                        child: Padding(
                          padding: EdgeInsets.all(8.0),
                          child: CardListMendicines(),
                        ),
                      ),
                      Visibility(
                        visible: exibeCard && _configHomeBoxes.feeMonitoring,
                        child: const Padding(
                            padding: EdgeInsets.all(8.0),
                            child: CardHonorariosQuimioterapia()),
                      ),
                      _cardComparativoProducao(),
                      Visibility(
                        visible: _configHomeBoxes.cooperative,
                        child: const ScoreComparisonScreen(),
                      ),
                      _load(),
                      Visibility(
                        visible: _configHomeBoxes.unimedIndicators,
                        child: const CardIndexesUnimed(),
                      ),
                    ],
                  )),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _cardComparativoProducao() {
    return BlocBuilder<LastProductionCubit, LastProductionState>(
      builder: (context, state) {
        if (state is LoadingLastProductionState) {
          return const Card(
            child: Column(
              children: [
                Align(
                  alignment: Alignment.topLeft,
                  child: Padding(
                    padding: EdgeInsets.all(8.0),
                    child: Text(
                      "Comparativo de produção",
                      style: TextStyle(
                        color: CooperadoColors.blackText,
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),
                SpinKitCircle(
                  color: CooperadoColors.tealGreen,
                ),
              ],
            ),
          );
        } else {
          return CardComparativoProducao(
            lastDate: context.read<LastProductionCubit>().lastDate ??
                DateTime(
                  DateTime.now().year,
                  DateTime.now().month - 1,
                  DateTime.now().day,
                ),
            isVisible: _configHomeBoxes.productionComparison,
          );
        }
      },
    );
  }

  Widget _load() {
    return BlocConsumer<CouncilCubit, CouncilState>(
      listener: (context, state) {
        if (state is DoneFiscalCouncilNewsState) {
          _showDialog(state.list, context);
        }
      },
      builder: (context, state) {
        return Container();
      },
    );
  }

  void _showDialog(List<Noticia> list, contextReceived) {
    final List<Noticia> filter =
        list.where((element) => !element.lido!).toList();
    if (filter.isNotEmpty) {
      showDialog(
        context: contextReceived,
        builder: (context) => CooperadoAlertNews(
          textTitle: 'Notícias do Conselho Fiscal',
          textButton: "Ir para notícias",
          textWidget: Text('Você tem ${filter.length} notícias não lidas'),
          onPressed: () async {
            // context.read<ReadCubit>().readNewsEvent(e);
            Navigator.of(context).pop();
            await Navigator.push(
              context,
              FadeRoute(
                page: NewsScreen(
                  analytics: widget.analytics,
                  observer: widget.observer,
                ),
              ),
            );
          },
        ),
      );
    }
  }

  _openAppStore() async {
    late String url;
    if (Platform.isAndroid) {
      url =
          'https://play.google.com/store/apps/details?id=br.com.unimedfortaleza.minhaunimed';
    } else if (Platform.isIOS) {
      url = 'https://apps-enterprise.unimedfortaleza.com.br';
    }
    if (!await launchUrl(Uri.parse(url))) {
      throw Exception('Could not launch $url');
    }
  }
}
