
import 'package:cooperado_minha_unimed/models/economic_indicators.model.dart';
import 'package:cooperado_minha_unimed/shared/api/economic_indicators.api.dart';
import 'package:cooperado_minha_unimed/shared/locator.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

part 'economic_indicators_state.dart';

class EconomicIndicatorsCubit extends Cubit<EconomicIndicatorsState> {
  EconomicIndicatorsCubit() : super(InitialEconomicIndicatorsState());
  

  getEconomicIndicators(String ano) async {
    try {
      emit(LoadingEconomicIndicatorsState());
      final economicIndicators = await Locator.instance!<EconomicIndicatorsApi>().getEconomicIndicators(ano);
     
     

      emit(LoadedEconomicIndicatorsState(economicIndicators: economicIndicators,));
    } catch (e) {
      emit(ErrorEconomicIndicatorsState(e.toString()));
    }
  }
}



