import 'package:cooperado_minha_unimed/bloc/auth/auth_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/res/diagnostico/res_brasil_diagnostico_result_cubit.state.dart';
import 'package:cooperado_minha_unimed/bloc/res/diagnostico/res_brazil_diagnostico_result_cubit.dart';
import 'package:cooperado_minha_unimed/colors.dart';
import 'package:cooperado_minha_unimed/models/res/res_attendance_model.dart';
import 'package:cooperado_minha_unimed/models/res/res_brasil_list_diagnostico_model.dart';
import 'package:cooperado_minha_unimed/screens/res/diagnostic-screen/res_diagnostic_detail.dart';
import 'package:cooperado_minha_unimed/screens/res/widget/app_bar_res.dart';
import 'package:cooperado_minha_unimed/screens/res/widget/custom_button_card.dart';
import 'package:cooperado_minha_unimed/screens/res/widget/empty_list.dart';
import 'package:cooperado_minha_unimed/screens/res/widget/erro_service.dart';
import 'package:cooperado_minha_unimed/screens/res/widget/expandable_card.dart';
import 'package:cooperado_minha_unimed/screens/res/widget/filters_widget.dart';
import 'package:cooperado_minha_unimed/shared/utils/router_observer.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:intl/intl.dart';

class ResBrazilDiagnosticoScreen extends StatefulWidget {
  final String nameBeneficiary;
  final String crm;
  final String card;
  final List<ResBrazilListDiagnosticoModel> listDiagnosticoResult;

  const ResBrazilDiagnosticoScreen({
    super.key,
    required this.nameBeneficiary,
    required this.crm,
    required this.card,
    required this.listDiagnosticoResult,
  });

  @override
  State<ResBrazilDiagnosticoScreen> createState() =>
      _ResBrazilDiagnosticoscreenState();
}

class _ResBrazilDiagnosticoscreenState extends State<ResBrazilDiagnosticoScreen>
    with RouteAware {
  FocusNode? focusTextFieldSearch;
  final bool _isLoading = false;
  int? _filterMonthSelected;
  DateTimeRange? _dateRangeToFilter;
  final List<int> _lastMonthsToFilter = [3, 6];

  List<ExpansionTileController> controllers = [];
  int? previusSelectedIndex;

  void _carregarDiagnosticos() {
    setState(() {
      controllers = [];
      previusSelectedIndex = null;
    });
    context
        .read<ResBrazilDiagnosticoResultCubit>()
        .listResBrazilDiagnosticoResult(
            crm: widget.crm, card: widget.card, dataRange: _dateRangeToFilter);
  }

  @override
  void initState() {
    super.initState();
    // Inicializa a busca dos diagnósticos
    _carregarDiagnosticos();
    focusTextFieldSearch = FocusNode();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    routeObserver.subscribe(this, ModalRoute.of(context) as PageRoute);
  }

  @override
  void dispose() {
    focusTextFieldSearch!.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return MultiBlocListener(
        listeners: [
          BlocListener<AuthCubit, AuthState>(listener: (context, state) {
            if (state is LoadedAuthState) {
              _carregarDiagnosticos();
            }
          }),
        ],
        child: Scaffold(
          appBar: AppBarRes(
            title: 'Diagnósticos',
            nameBeneficiary: widget.nameBeneficiary,
          ),
          backgroundColor: CooperadoColors.backgroundWhiteColor,
          body: Padding(
            padding: const EdgeInsets.only(left: 20, right: 20, top: 24),
            child: Column(
              children: [
                FiltersWidget(
                  isLoading: _isLoading,
                  lastMonthsToFilter: _lastMonthsToFilter,
                  filterMonthSelected: _filterMonthSelected,
                  dateRangeToFilter: _dateRangeToFilter,
                  onMonthFilterChanged: (filterSelected) {
                    setState(() {
                      _filterMonthSelected = filterSelected;
                      debugPrint('filterSelected: $filterSelected');
                      filterSelected == null
                          ? _dateRangeToFilter = null
                          : _dateRangeToFilter = DateTimeRange(
                              start: _selectedDataStart(filterSelected),
                              end: DateTime.now(),
                            );
                    });
                    _carregarDiagnosticos();
                  },
                  onClearDateRange: () {
                    setState(() {
                      _dateRangeToFilter = null;
                      _filterMonthSelected = null;
                    });
                    _carregarDiagnosticos();
                  },
                  selectDateToFilter: _selectDateToFilter,
                  onDateRangeSelected: (dateRange) {
                    setState(() {
                      _filterMonthSelected = null;
                      _dateRangeToFilter = dateRange;
                    });
                    _carregarDiagnosticos();
                  },
                ),
                BlocBuilder<ResBrazilDiagnosticoResultCubit,
                    ResBrazilDiagnosticoResultCubitState>(
                  builder: (context, state) {
                    if (state is LoadedResBrazilListDiagnosticoResultState) {
                      if (state.listAllDiagnostico.isEmpty &&
                          (_filterMonthSelected != null ||
                              _dateRangeToFilter != null)) {
                        return EmptyList(
                          pathIcon: 'assets/svg/icon_file.svg',
                          message: _dateRangeToFilter != null
                              ? 'Não foi encontrado nenhum diagnóstico para o período de ${DateFormat("dd/MM/yyyy").format(_dateRangeToFilter!.start)} até ${DateFormat("dd/MM/yyyy").format(_dateRangeToFilter!.end)}'
                              : 'Não foi encontrado nenhum diagnóstico.',
                        );
                      }
                      return Expanded(
                        child: RefreshIndicator(
                          onRefresh: () async {
                            _carregarDiagnosticos();
                          },
                          child: ListView.builder(
                            physics: const ClampingScrollPhysics(
                                parent: AlwaysScrollableScrollPhysics()),
                            itemCount: state.listAllDiagnostico.length,
                            itemBuilder: (context, index) {
                              ExpansionTileController controller =
                                  ExpansionTileController();

                              controllers.add(controller);
                              final diagnostico =
                                  state.listAllDiagnostico[index];
                              return Padding(
                                padding: const EdgeInsets.all(8.0),
                                child: ExpandableCard(
                                  pathIcon: 'assets/svg/icon_diagnostico.svg',
                                  title: 'Diagnósticos',
                                  controller: controller,
                                  subtitle: diagnostico.dataEntrada != null
                                      ? DateFormat('dd/MM/yyyy - HH:mm').format(
                                          DateTime.parse(
                                              diagnostico.dataEntrada!))
                                      : '',
                                  additionalInfo: [
                                    {
                                      'title': 'Tipo',
                                      'description': diagnostico.tipo ?? ''
                                    },
                                    {
                                      'title': 'Local',
                                      'description': diagnostico.nomeLocal ?? ''
                                    },
                                  ],
                                  onExpansionChanged: (value) {
                                    setState(() {
                                      if (value &&
                                          previusSelectedIndex != index) {
                                        debugPrint(
                                            'previusSelectedIndex: $previusSelectedIndex');
                                        if (previusSelectedIndex != null) {
                                          controllers[previusSelectedIndex!]
                                              .collapse();
                                        }
                                        previusSelectedIndex = index;
                                      }
                                    });
                                  },
                                  buttons: _buildButtons(diagnostico),
                                ),
                              );
                            },
                          ),
                        ),
                      );
                    } else if (state is ErroResBrazilDiagnosticoResultState) {
                      return Expanded(
                        child: Center(
                          child: ErroService(
                            message: state.message,
                            onPressed: () {
                              _carregarDiagnosticos();
                            },
                          ),
                        ),
                      );
                    } else if (state is NoDataResBrazilDiagnosticoResultState) {
                      return Expanded(
                        child: Padding(
                          padding: const EdgeInsets.only(top: 32.0),
                          child: Center(
                            child: EmptyList(
                              pathIcon: 'assets/svg/icon_file.svg',
                              message: _dateRangeToFilter != null
                                  ? 'Sem dados de diagnóstico para o período de ${DateFormat("dd/MM/yyyy").format(_dateRangeToFilter!.start)} até  ${DateFormat("dd/MM/yyyy").format(_dateRangeToFilter!.end)}'
                                  : 'Sem dados de diagnóstico.',
                            ),
                          ),
                        ),
                      );
                    } else if (state
                        is LoadingResBrazilDiagnosticoResultState) {
                      return const Expanded(
                        child: Center(
                          child:
                              SpinKitCircle(color: CooperadoColors.tealGreen),
                        ),
                      );
                    }
                    return Container();
                  },
                ),
              ],
            ),
          ),
        ));
  }

  DateTime _selectedDataStart(int meses) {
    DateTime dataAtual = DateTime.now();
    DateTime novaData = DateTime(
      dataAtual.year,
      dataAtual.month - meses,
      dataAtual.day,
      dataAtual.hour,
      dataAtual.minute,
      dataAtual.second,
      dataAtual.millisecond,
      dataAtual.microsecond,
    );

    return novaData;
  }

  List<CustomButtonCard> _buildButtons(ResAttendanceModel diagnostico) {
    List<CustomButtonCard> buttons = [];

    if (diagnostico.itensAtendimento != null &&
        diagnostico.itensAtendimento!.isNotEmpty) {
      buttons.add(
        CustomButtonCard(
          text: 'Ver detalhes',
          onPressed: () {
            _showDetailsBottomSheet(context, diagnostico, 'Diagnóstico');
          },
        ),
      );
    }

    return buttons;
  }

  void _showDetailsBottomSheet(
      BuildContext context, ResAttendanceModel atendimento, String title) {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.vertical(top: Radius.circular(15))),
      builder: (BuildContext context) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            const SizedBox(height: 16),
            detailDiagnostico(atendimento.codigo.toString()),
          ],
        );
      },
    );
  }

  Widget detailDiagnostico(String code) {
    return BlocProvider(
      create: (context) => ResBrazilDiagnosticoResultCubit()
        ..listResBrazilDiagnosticoDetailResult(
          crm: widget.crm, // Passa o CRM recebido via construtor
          card: widget.card, // Passa o cartão recebido via construtor
          code: code, // Passa o código recebido via construtor
        ),
      child: BlocBuilder<ResBrazilDiagnosticoResultCubit,
          ResBrazilDiagnosticoResultCubitState>(
        builder: (context, state) {
          if (state is LoadingResBrazilDiagnosticoResultState) {
            return Column(
              children: [
                Center(
                  child: Container(
                    height: 4,
                    width: 100,
                    margin: const EdgeInsets.symmetric(
                      vertical: 10,
                    ),
                    decoration: BoxDecoration(
                      color: CooperadoColors.grayLight3,
                      borderRadius: BorderRadius.circular(2),
                    ),
                  ),
                ),
                const Center(
                  child: SpinKitCircle(color: CooperadoColors.tealGreen),
                ),
                const SizedBox(height: 16),
              ],
            );
          } else if (state is LoadedResBrazilDiagnosticoResultState) {
            return Expanded(
                child:
                    DiagnosticDetail(listDiagnostico: state.listDiagnostico));
          } else if (state is ErroResBrazilDiagnosticoResultState) {
            return Padding(
              padding: const EdgeInsets.all(12.0),
              child: Center(
                  child: ErroService(
                message: state.message,
                onPressed: () {
                  context
                      .read<ResBrazilDiagnosticoResultCubit>()
                      .listResBrazilDiagnosticoDetailResult(
                        crm: widget.crm,
                        card: widget.card,
                        code: code,
                      );
                },
              )),
            );
          } else if (state is NoDataResBrazilDiagnosticoResultState) {
            return Expanded(
              child: Padding(
                padding: const EdgeInsets.only(top: 32.0),
                child: Center(
                  child: EmptyList(
                    pathIcon: 'assets/svg/icon_file.svg',
                    message: _dateRangeToFilter != null
                        ? 'Sem dados de diagnóstico para o período de ${DateFormat("dd/MM/yyyy").format(_dateRangeToFilter!.start)} até  ${DateFormat("dd/MM/yyyy").format(_dateRangeToFilter!.end)}'
                        : 'Sem dados de diagnóstico.',
                  ),
                ),
              ),
            );
          } else {
            return Container();
          }
        },
      ),
    );
  }

  DateTime _getFirstDate() {
    DateTime now = DateTime.now();
    int year = now.year;
    bool isLeapYear = (year % 4 == 0 && year % 100 != 0) || (year % 400 == 0);
    return now.subtract(Duration(days: isLeapYear ? 366 : 365));
  }

  Future<DateTime?> _selectDateToFilter(BuildContext context, String helpText,
      {DateTime? firstDate}) async {
    return await showDatePicker(
      context: context,
      helpText: helpText,
      initialDate: DateTime.now(),
      firstDate: firstDate ?? _getFirstDate(),
      lastDate: DateTime.now(),
      keyboardType: TextInputType.text,
      builder: (BuildContext context, Widget? child) {
        return Theme(
          data: ThemeData.light().copyWith(
            colorScheme: const ColorScheme.light(
              primary: CooperadoColors.tealGreen,
              onPrimary: Colors.white,
              onSurface: Colors.black,
            ),
          ),
          child: child!,
        );
      },
    );
  }
}
