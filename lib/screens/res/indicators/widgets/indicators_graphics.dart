import 'package:cooperado_minha_unimed/bloc/res/configs/res_configs_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/res/indicators/res_indicator_data_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/res/indicators/res_indicator_data_state.dart';
import 'package:cooperado_minha_unimed/colors.dart';
import 'package:cooperado_minha_unimed/screens/res/indicators/widgets/card_indicator_data.dart';
import 'package:cooperado_minha_unimed/screens/res/widget/empty_list.dart';
import 'package:cooperado_minha_unimed/screens/res/widget/erro_service.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';

class IndicatorGraphics extends StatefulWidget {
  const IndicatorGraphics(
      {super.key,
      required this.cpfBeneficiary,
      required this.dateRangeToFilter,
      required this.selectedIndicators});

  final String cpfBeneficiary;
  final DateTimeRange dateRangeToFilter;
  final List<String> selectedIndicators;

  @override
  // ignore: library_private_types_in_public_api
  _IndicatorGraphicsState createState() => _IndicatorGraphicsState();
}

class _IndicatorGraphicsState extends State<IndicatorGraphics> {
  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ResIndsicatorsDataCubit, ResIndicatorsDataState>(
      builder: (context, state) {
        if (state is LoadingResIndicatorsDataState) {
          return const Center(
            child: SpinKitThreeBounce(
              color: CooperadoColors.green,
              size: 30.0,
            ),
          );
        } else if (state is ErrorResIndicatorDataState) {
          return SizedBox(
            height: MediaQuery.of(context).size.height * 0.5,
            child: ErroService(
                message: state.message,
                onPressed: () {
                  context
                      .read<ResIndsicatorsDataCubit>()
                      .loadResBrazilBeneficiaryIndicatorsData(
                        cpf: widget.cpfBeneficiary,
                        indicatorsId: widget.selectedIndicators,
                        dateRange: widget.dateRangeToFilter,
                      );
                }),
          );
        } else if (state is NoDataResIndicatorDataState) {
          return const EmptyList(
            pathIcon: 'assets/svg/icon_file.svg',
            message: 'Sem dados de indicadores.',
          );
        } else if (state is LoadedResIndicatorDataState) {
          return ListView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: state.resIndicatorsData.length,
            itemBuilder: (context, index) {
              final indicatorData = state.resIndicatorsData.elementAt(index);

              return CardIndicatorData(
                  indicatorData: indicatorData,
                  description: context
                      .read<ResConfigCubit>()
                      .resConfigModel
                      .indicators
                      .firstWhere((element) =>
                          element.id.toString() == indicatorData.id.toString())
                      .description);
            },
          );
        }

        return Container();
      },
    );
  }
}
