import 'package:cooperado_minha_unimed/bloc/indicators/cost_comparative/cost_comparative_cubit.dart';
import 'package:cooperado_minha_unimed/screens/indicadores/graphics/bargraph_cost.dart';
import 'package:cooperado_minha_unimed/shared/utils/graph_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class CustoChart extends StatelessWidget {
  const CustoChart({super.key});

  @override
  Widget build(BuildContext context) {
    List<double> allValues = [];
    List<CustoSerie> seriesCost = context.read<CostComparativeCubit>().costList;
    for (CustoSerie serie in seriesCost) {
      if (serie.valor != null) {
        if (serie.valor! > 0.0) {
          allValues.add(serie.valor!);
        }
      }
    }
    double maxY = GraphUtils.findMax(allValues);
    return CostBarGraph(maxY: maxY, data: seriesCost);
  }
}

class CustoSerie {
  final double? valor;
  final String title;
  final Color barColor;

  CustoSerie(
      {required this.valor, required this.title, required this.barColor});
}
