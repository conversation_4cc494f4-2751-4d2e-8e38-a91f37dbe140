

import 'package:equatable/equatable.dart';

abstract class EcardActivationState extends Equatable {
  const EcardActivationState();

  @override
  List<Object> get props => [];
}

class InitialEcardActivationState extends EcardActivationState {}

class LoadingEcardActivationState extends EcardActivationState {
  @override
  List<Object> get props => [];
}

class ErrorEcardActivationState extends EcardActivationState {
  final String message;

  @override
  List<Object> get props => [message];

  const ErrorEcardActivationState({required this.message});
}

class LoadedEcardActivationState extends EcardActivationState {
  final String message;

  @override
  List<Object> get props => [message];

  const LoadedEcardActivationState({required this.message});
}

