import 'package:cooperado_minha_unimed/bloc/res/atendimento/res_brasil_atendimento_result_cubit.state.dart';
import 'package:cooperado_minha_unimed/models/res/res_attendance_model.dart';
import 'package:cooperado_minha_unimed/shared/api/graphql-res.api.dart';
import 'package:cooperado_minha_unimed/shared/locator.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class ResBrasilAtendimentoResultCubit
    extends Cubit<ResBrasilAtendimentoResultState> {
  ResBrasilAtendimentoResultCubit()
      : super(InitialResBrasilAtendimentoResultState());

  List<ResAttendanceModel> _listAtendimentoResult = List.empty(growable: true);
  List<ResAttendanceModel> get listAtendimentoResult => _listAtendimentoResult;

  void listResAtendimentoResult({
    required String crm,
    required String card,
    DateTime? startDateTime,
    DateTime? endDateTime,
  }) async {
    try {
      emit(LoadingResBrasilAtendimentoResultState());

      _listAtendimentoResult =
          await Locator.instance!<ResGraphQlApi>().resBrasilAttendanceByCard(
        crm: crm,
        card: card,
        startDateTime: startDateTime,
        endDateTime: endDateTime,
      );

      if (_listAtendimentoResult.isEmpty) {
        emit(const NoDataResBrasilAtendimentoResultState(
            message: 'Sem dados de atendimentos.'));
      } else {
        emit(LoadedResBrasilAtendimentoResultState(
            listAtendimentoResult: _listAtendimentoResult));
      }
    } catch (e) {
      emit(ErrorResBrasilAtendimentoResultState(message: e.toString()));
    }
  }
}
