// ignore_for_file: use_build_context_synchronously

import 'dart:io';

import 'package:cooperado_minha_unimed/bloc/auth/agree_terms/agree_terms_cubit.dart';
import 'package:cooperado_minha_unimed/colors.dart';
import 'package:cooperado_minha_unimed/models/agree_terms.model.dart';
import 'package:cooperado_minha_unimed/shared/locator.dart';
import 'package:cooperado_minha_unimed/shared/widgets/alert.dart';
import 'package:cooperado_minha_unimed/shared/widgets/pdf_view/pdf_view_platform.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:flutter_svg/svg.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:remote_log_elastic/remote_log_elastic.dart';
import 'package:url_launcher/url_launcher.dart';

const String terms = "Termos";
const String of = "de";
const String agreePart1 = "Li e estou ciente dos termos do(a)";
const String agreePart2 = " ";
const String agreePart3 = " Unimed Fortaleza.";
const String seeLater = "Ver depois";
const String errorPermission = "Permissão para salvar o arquivo não concedida.";
const String openFile = "Seu documento foi baixado, deseja abri-lo?";
const String yes = "Sim";
const String no = "Não";
const String click = "Clique";
const String here = " aqui ";
const String toDownloadAndRead = "para baixar e ler o Código de Conduta.";
const String requireAgree = "Você precisa aceitar os termos para continuar.";
const String confirm = "Confirmar";
const String successMessage = "Termos aceitos com sucesso!";
const String fileName = ' ';

class AlertAgree {
  Future<void> open(
    BuildContext context, {
    required ValueNotifier<bool> agreeTerm,
    required VoidCallback primaryButton,
    required String title,
    required String subtitle,
    required String pdf,
    required int code,
    required int index,
    required AgreeTermsModel agreeTermsModel,
    String? imagePath,
  }) async {
    return showDialog(
      context: context,
      builder: ((context) => Dialog(
            insetPadding: const EdgeInsets.all(20),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(20),
            ),
            child: Padding(
              padding: const EdgeInsets.all(20),
              child: SingleChildScrollView(
                child: BlocConsumer<AgreeTermsCubit, AgreeTermsState>(
                  listener: (context, state) {
                    if (state is ErrorPermissionState) {
                      Alert.open(context, text: errorPermission);
                    } else if (state is PopAgreeStoragePermissionState) {
                      _alertPermissionDataAcces(state.status, context, state.pdf, state.title);
                    } else if (state is SuccessDownloadState) {
                      showDialog(
                        context: context,
                        builder: (BuildContext context) {
                          return AlertDialog(
                            title: Text(title),
                            content: const Text(openFile),
                            actions: <Widget>[
                              TextButton(
                                child: const Text(yes),
                                onPressed: () async {
                                  Navigator.of(context).pop();
                                  Navigator.push(
                                    context,
                                    MaterialPageRoute(
                                      builder: (context) => PDFViewPlatform(
                                        pdf,
                                        share: true,
                                        filename: _getFilenameFromTitle(title),
                                      ),
                                    ),
                                  );
                                },
                              ),
                              TextButton(
                                child: const Text(no, style: TextStyle(color: Colors.black)),
                                onPressed: () {
                                  Navigator.of(context).pop();
                                },
                              ),
                            ],
                          );
                        },
                      );
                    }
                  },
                  builder: (context, state) {
                    if (state is LoadingState) {
                      return const SpinKitThreeBounce(
                        color: CooperadoColors.green,
                        size: 20,
                      );
                    }
                    return Column(
                      crossAxisAlignment: CrossAxisAlignment.stretch,
                      mainAxisAlignment: MainAxisAlignment.start,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        //devido ao fato de nas novas expressões nao existir eva sorrindo e o fato de serem png, modifiquei para caso nao seja
                        //informado o caminho da imagem, ele utilize a imagem padrão, que possui eva sorrindo porem em sgv

                        imagePath != null
                            ? Padding(
                                padding: const EdgeInsets.symmetric(horizontal: 60),
                                child: ClipOval(
                                  child: AspectRatio(
                                    aspectRatio: 1,
                                    child: Image.asset(
                                      imagePath,
                                      width: 80,
                                      height: 80,
                                      fit: BoxFit.cover,
                                    ),
                                  ),
                                ),
                              )
                            : SvgPicture.asset('assets/icon/eva-assistentevirtual.svg', width: 150),
                        Padding(
                          padding: const EdgeInsets.only(bottom: 10.0),
                          child: Text(title,
                              textAlign: TextAlign.center,
                              style: const TextStyle(
                                fontSize: 20,
                                fontWeight: FontWeight.bold,
                                color: CooperadoColors.green,
                              )),
                        ),
                        Padding(
                            padding: const EdgeInsets.only(bottom: 20.0),
                            child: SingleChildScrollView(
                              child: Html(
                                data: subtitle,
                                onLinkTap: (url, attributes, element) {
                                  if (url != null) _onTapLink(url);
                                },
                              ),
                            )),
                        Padding(
                          padding: const EdgeInsets.only(bottom: 20.0),
                          child: Row(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              InkWell(
                                onTap: () => context.read<AgreeTermsCubit>().setBool(),
                                child: Padding(
                                  padding: const EdgeInsets.only(right: 10, top: 3),
                                  child: Container(
                                    width: 20,
                                    height: 20,
                                    decoration: BoxDecoration(
                                      color: Colors.white,
                                      borderRadius: BorderRadius.circular(2),
                                      border: Border.all(
                                        color: CooperadoColors.grayDark2,
                                        width: 1,
                                      ),
                                    ),
                                    child: state.agreeTerm
                                        ? const Icon(
                                            Icons.check,
                                            color: Colors.green,
                                            size: 15,
                                          )
                                        : Container(),
                                  ),
                                ),
                              ),
                              Expanded(
                                child: RichText(
                                  textAlign: TextAlign.left,
                                  text: TextSpan(
                                    style: const TextStyle(
                                      color: Colors.black,
                                      fontSize: 14,
                                    ),
                                    children: <TextSpan>[
                                      const TextSpan(
                                        text: agreePart1,
                                      ),
                                      if (agreeTermsModel.pdf.isNotEmpty &&
                                          agreeTermsModel.pdf != ' ')
                                        TextSpan(
                                          recognizer: TapGestureRecognizer()
                                            ..onTap = () {
                                              Navigator.push(
                                                context,
                                                MaterialPageRoute(
                                                  builder: (context) => PDFViewPlatform(
                                                    pdf,
                                                    share: true,
                                                    filename: fileName,
                                                  ),
                                                ),
                                              );
                                            },
                                          text: agreeTermsModel.pdf.isNotEmpty
                                              ? agreeTermsModel.title
                                              : 'Li e estou de acordo com o texto acima',
                                          style: const TextStyle(
                                            color: CooperadoColors.green,
                                            fontWeight: FontWeight.bold,
                                          ),
                                        )
                                      else
                                        TextSpan(
                                          text: agreeTermsModel.title,
                                          style: const TextStyle(
                                            fontWeight: FontWeight.bold,
                                          ),
                                        ),
                                      const TextSpan(
                                        text: agreePart3,
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                        Row(
                          children: [
                            Expanded(
                              child: Padding(
                                padding: const EdgeInsets.only(right: 10.0),
                                child: InkWell(
                                  onTap: primaryButton,
                                  child: Container(
                                    padding: const EdgeInsets.only(
                                        top: 12.0, bottom: 12.0, left: 20, right: 20),
                                    decoration: const BoxDecoration(
                                        color: CooperadoColors.gray,
                                        borderRadius: BorderRadius.all(
                                          Radius.circular(20),
                                        )),
                                    child: const Text(
                                      seeLater,
                                      textAlign: TextAlign.center,
                                    ),
                                  ),
                                ),
                              ),
                            ),
                            _btnConfirmar(context, code)
                          ],
                        ),
                      ],
                    );
                  },
                ),
              ),
            ),
          )),
    );
  }

  _onTapLink(String url) async {
    await _launchURL(url);
  }

  Future _launchURL(String url) async {
    if (await canLaunchUrl(Uri.parse(url))) {
      await launchUrl(Uri.parse(url), mode: LaunchMode.externalApplication);
    } else {
      throw 'Unable to open url : $url';
    }
  }

  _alertPermissionDataAcces(
      PermissionStatus? dataAccesStatus, BuildContext context, String pdf, String title) {
    bool canClick = true;
    Alert.open(
      context,
      title: 'Acesso aos dados',
      text: 'Precisamos acessar seus arquivos para salvar o documento',
      textButtonClose: 'Fechar',
      callbackClose: () {},
      actions: <Widget>[
        ElevatedButton(
          style: ElevatedButton.styleFrom(
            foregroundColor: Colors.white,
            backgroundColor: CooperadoColors.orange,
            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(5.0)),
          ),
          child: const Text('Autorizar'),
          onPressed: () async {
            if (canClick) {
              canClick = false;

              if (dataAccesStatus == PermissionStatus.denied) {
                final infoJson = await Locator.instance!<RemoteLog>().deviceInfo?.toJson();
                final androidVersion = int.parse(infoJson?['version.release'] ?? '0');

                Platform.isAndroid
                    ? androidVersion < 13
                        ? Permission.storage.request().then((_) {
                            if (context.mounted) {
                              Navigator.of(context).pop();
                            }
                          })
                        : context.mounted
                            ? Navigator.of(context).pop()
                            : {}
                    : Permission.photos.request().then((_) {
                        if (context.mounted) {
                          Navigator.of(context).pop();
                        }
                      });
              } else {
                Navigator.of(context).pop();
                openAppSettings();
              }
            }
          },
        ),
      ],
    );
  }

  String _getFilenameFromTitle(String title) {
    // Basic sanitization: Remove special characters and spaces
    String filename = title.replaceAll(RegExp(r'[^a-zA-Z0-9]+'), '_').toLowerCase();
    return '$filename.pdf';
  }

  // Widget _btnDownload(context, pdf, title) {
  //   return BlocBuilder<AgreeTermsCubit, AgreeTermsState>(
  //     builder: (context, state) {
  //       if (state is LoadingDownloadState) {
  //         return const SpinKitThreeBounce(
  //           color: CooperadoColors.green,
  //           size: 20,
  //         );
  //       }
  //       return InkWell(
  //         onTap: () async {
  //           await context.read<AgreeTermsCubit>().downloadPdf(pdf, title);
  //         },
  //         child: Padding(
  //           padding: const EdgeInsets.only(bottom: 20.0),
  //           child: RichText(
  //             textAlign: TextAlign.justify,
  //             text: const TextSpan(
  //               style: TextStyle(
  //                 color: Colors.black,
  //                 fontSize: 14,
  //               ),
  //               children: <TextSpan>[
  //                 TextSpan(
  //                   text: click,
  //                 ),
  //                 TextSpan(
  //                   text: here,
  //                   style: TextStyle(color: CooperadoColors.green),
  //                 ),
  //                 TextSpan(
  //                   text: toDownloadAndRead,
  //                 ),
  //               ],
  //             ),
  //           ),
  //         ),
  //       );
  //     },
  //   );
  // }

  Widget _btnConfirmar(context, code) {
    return BlocBuilder<AgreeTermsCubit, AgreeTermsState>(
      builder: (context, state) {
        if (state is LoadingState) {
          return const SpinKitThreeBounce(
            color: CooperadoColors.green,
            size: 20,
          );
        }
        return Expanded(
          child: InkWell(
            onTap: () async {
              if (state.agreeTerm) {
                if (await context.read<AgreeTermsCubit>().acceptTerms(code)) {
                  if (context.mounted) {
                    Alert.open(context, title: successMessage, callbackClose: () {
                      Navigator.pop(context);
                    });
                  }
                }
              } else {
                Alert.open(context, title: requireAgree);
              }
            },
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
              decoration: const BoxDecoration(
                  color: CooperadoColors.green,
                  borderRadius: BorderRadius.all(
                    Radius.circular(20),
                  )),
              child: const Text(
                confirm,
                textAlign: TextAlign.center,
                style: TextStyle(color: Colors.white),
              ),
            ),
          ),
        );
      },
    );
  }
}
