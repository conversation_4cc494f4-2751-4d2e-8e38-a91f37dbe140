class BeneficiaryCardModel {
  late int codUnimed;
  late int codBeneficiario;
  late String nomeBeneficiario;
  late String iniciaisBeneficiario;
  String? sexo;
  int? cpf;
  late int unimedCarteira;
  late int codCarteira;
  late String dvCarteira;
  late String dataEmissao;
  late String descPlano;
  String? nomeSocial;
  late String carteira;

  BeneficiaryCardModel(
      {required this.codUnimed,
      required this.codBeneficiario,
      required this.nomeBeneficiario,
      required this.iniciaisBeneficiario,
      this.sexo,
      this.cpf,
      required this.unimedCarteira,
      required this.codCarteira,
      required this.dvCarteira,
      required this.dataEmissao,
      required this.descPlano,
      this.nomeSocial,
      required this.carteira});

  BeneficiaryCardModel.fromJson(Map<String, dynamic> json) {
    codUnimed = json['cod_unimed'];
    codBeneficiario = json['cod_beneficiario'];
    nomeBeneficiario = json['nome_beneficiario'];
    iniciaisBeneficiario = json['iniciais_beneficiario'];
    sexo = json['sexo'];
    cpf = json['cpf'];
    unimedCarteira = json['unimed_carteira'];
    codCarteira = json['cod_carteira'];
    dvCarteira = json['dv_carteira'];
    dataEmissao = json['data_emissao'];
    descPlano = json['desc_plano'];
    nomeSocial = json['nome_social'];
    carteira = json['carteira'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['cod_unimed'] = codUnimed;
    data['cod_beneficiario'] = codBeneficiario;
    data['nome_beneficiario'] = nomeBeneficiario;
    data['iniciais_beneficiario'] = iniciaisBeneficiario;
    data['sexo'] = sexo;
    data['cpf'] = cpf;
    data['unimed_carteira'] = unimedCarteira;
    data['cod_carteira'] = codCarteira;
    data['dv_carteira'] = dvCarteira;
    data['data_emissao'] = dataEmissao;
    data['desc_plano'] = descPlano;
    data['nome_social'] = nomeSocial;
    data['carteira'] = carteira;
    return data;
  }
}
