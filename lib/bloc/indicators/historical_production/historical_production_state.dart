part of 'historical_production_cubit.dart';

abstract class HistoricalProductionState extends Equatable {
  const HistoricalProductionState();

  @override
  List<Object?> get props => [];
}

class InitialHistoricalProductionState extends HistoricalProductionState {}

class LoadingHistoricalProductionState extends HistoricalProductionState {
  @override
  List<Object> get props => [];
}

class ErrorHistoricalProductionState extends HistoricalProductionState {
  final String? message;
  @override
  List<Object?> get props => [message];
  const ErrorHistoricalProductionState(this.message);
}

class LoadedHistoricalProductionState extends HistoricalProductionState {
  final List<ProductionHistoryVO> list;
  @override
  List<Object> get props => [list];

  const LoadedHistoricalProductionState(this.list);
}
