import 'dart:async';
import 'dart:convert';

import 'package:cooperado_minha_unimed/models/user.model.dart';
import 'package:cooperado_minha_unimed/shared/api/auth.api.dart';
import 'package:cooperado_minha_unimed/shared/exceptions.dart';
import 'package:cooperado_minha_unimed/shared/flavor_config.dart';
import 'package:cooperado_minha_unimed/shared/locator.dart';
import 'package:cooperado_minha_unimed/shared/utils/logger_print.dart';
import 'package:http_client/http_client.dart';

class TermsApi {
  final UnimedHttpClient httpClient;

  final logger = UnimedLogger(className: 'TermsApi');

  TermsApi(this.httpClient);

  Future verifyAgreeTerms() async {
    try {
      UserCredentials? credentials = await Locator.instance!.get<AuthApi>().getCredentials();
      final token = credentials != null ? await User.createToken(credentials: credentials) : "";
      final tokenPerfilApps = await Locator.instance!.get<AuthApi>().tokenPerfilApps();
      final url = '${FlavorConfig.instance!.values.profilePermissions.url}app/cooperado/terms?token=$token';

      final response = await httpClient.get(
        Uri.parse(url),
        headers: {
          'Content-Type': 'application/json',
          "Authorization": "Bearer $tokenPerfilApps",
        },
      );

      if (response.statusCode == 200) {
        logger.d('success | response ${response.statusCode}');

        return response.body;
      } else {
        logger.e('statusCode termos: ${response.statusCode} ${response.body}');
        throw PdfException('Serviço indisponível no momento.');
      }
    } catch (ex) {
      logger.e('statusCode termos: $ex');
      throw PdfException('Não foi possível no momento.');
    }
  }

  Future acceptTerms(index) async {
    try {
      UserCredentials? credentials = await Locator.instance!.get<AuthApi>().getCredentials();
      final token = credentials != null ? await User.createToken(credentials: credentials) : "";
      final tokenPerfilApps = await Locator.instance!.get<AuthApi>().tokenPerfilApps();
      final url = "${FlavorConfig.instance!.values.profilePermissions.url}app/cooperado/terms/accept?token=$token";

      final response = await httpClient.post(
        Uri.parse(url),
        body: json.encode({"termCode": index, "app": "APP"}),
        headers: {
          'Content-Type': 'application/json',
          "Authorization": "Bearer $tokenPerfilApps",
        },
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        logger.d('success | response ${response.statusCode}');

        return response.body;
      } else {
        logger.e('createPDFFileFromUrl - statusCode : ${response.statusCode} ${response.body}');
        throw PdfException('Serviço indisponível no momento.');
      }
    } on UnimedException catch (ex) {
      throw PdfException(ex.message);
    } catch (ex) {
      // logger.e('_getFileFromUrl catch exception - $ex - url: $url');
      throw PdfException('Não foi possível no momento.');
    }
  }
}
