// ignore_for_file: public_member_api_docs, sort_constructors_first

import 'package:cooperado_minha_unimed/bloc/sensitive-data/sensitive_data_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/sensitive-data/sensitive_data_state.dart';
import 'package:cooperado_minha_unimed/colors.dart';
import 'package:cooperado_minha_unimed/screens/transparencia/beneficiary/beneficiary-graphs/beneficiary_piegraph.dart';
import 'package:cooperado_minha_unimed/shared/utils/string_utils.dart';
import 'package:cooperado_minha_unimed/shared/widgets/graphics/indicator.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class AgePieGraphWidget extends StatelessWidget {
  const AgePieGraphWidget({
    super.key,
    required this.listBenefic,
  });
  final List<BeneficiaryData> listBenefic;
  // sunAmount

  List<PieChartSectionData> showingSections() {
    const double radius = 60.0;
    const shadows = [Shadow(color: Colors.black, blurRadius: 2)];

    final data = listBenefic
        .asMap()
        .map<int, PieChartSectionData>((index, value) {
          final sectionData = PieChartSectionData(
            color: CooperadoColors.chartColors[index],
            value: double.parse(value.quantity.toString()),
            title: '',
            radius: radius,
            titleStyle: const TextStyle(
              fontWeight: FontWeight.bold,
              color: Colors.white,
              shadows: shadows,
            ),
          );
          return MapEntry(index, sectionData);
        })
        .values
        .toList();

    return data;
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 8.0),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          Expanded(
            child: BlocBuilder<SensitiveDataCubit, SensitiveDataState>(
  builder: (context, sensitiveState) {
    bool isSensitiveDataVisible = ! sensitiveState.isSensitiveDataVisible;
    return PieChart(
      PieChartData(
        borderData: FlBorderData(
          show: false,
        ),
        sectionsSpace: 2,
        startDegreeOffset: 270,
        centerSpaceRadius: 0,
        sections: isSensitiveDataVisible
            ? [
                PieChartSectionData(
                  color: Colors.grey,
                  value: 100,
                  showTitle: false,
                ),
              ]
            : showingSections(),
      ),
    );
  },
),
          ),
          BlocBuilder<SensitiveDataCubit, SensitiveDataState>(
  builder: (context, sensitiveState) {
    bool isSensitiveDataVisible = !sensitiveState.isSensitiveDataVisible;

    return Column(
      children: listBenefic
          .asMap()
          .map((index, value) {
            String displayQuantity = isSensitiveDataVisible
                ? '*****'
                : StringUtils.formatThousands(listBenefic[index].quantity?.toInt());

            return MapEntry(
              index,
              Indicator(
                color: CooperadoColors.chartColors[index],
                text: "${listBenefic[index].description} ($displayQuantity)",
                isSquare: false,
              ),
            );
          })
          .values
          .toList(),
    );
  },
),
          const SizedBox(
            height: 6,
          ),
        ],
      ),
    );
  }
}
