import 'package:cooperado_minha_unimed/bloc/sensitive-data/sensitive_data_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/sensitive-data/sensitive_data_state.dart';
import 'package:cooperado_minha_unimed/colors.dart';
import 'package:cooperado_minha_unimed/screens/main/score-comparison/body_card.dart';
import 'package:cooperado_minha_unimed/shared/vo/score_comparison.vo.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class ScoreHistory extends StatefulWidget {
  final ResponseScore? responseScore;
  const ScoreHistory({super.key, this.responseScore});
  @override
  ScoreHistoryState createState() => ScoreHistoryState();
}

class ScoreHistoryState extends State<ScoreHistory> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        centerTitle: true,
        title: const Text("Histórico de pontuação"),
         actions: [
             Padding(
             padding: const EdgeInsets.only(right: 15.0),
             child: IconButton(
                       icon: BlocBuilder<SensitiveDataCubit, SensitiveDataState>(
              builder: (context, state) {
                return Icon(
                  state.isSensitiveDataVisible ? Icons.visibility : Icons.visibility_off,
                );
              },
                       ),
                       onPressed: () {
              context.read<SensitiveDataCubit>().toggleSensitiveDataVisibility();
                       },
                     ),
           ),
        ],

      ),
      backgroundColor: CooperadoColors.grayLight,
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(8.0),
          child: SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: <Widget>[
                _cardHeader(),
                const Padding(
                  padding: EdgeInsets.all(8.0),
                  child: Text(
                    "Histórico de sua pontuação",
                    style: TextStyle(
                        color: CooperadoColors.blackText,
                        fontSize: 16,
                        fontWeight: FontWeight.bold),
                  ),
                ),
                _listScore()
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _cardHeader() {
    return Card(
        child: Padding(
      padding: const EdgeInsets.all(8.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          const Text("Cooperativo",
              style: TextStyle(
                  color: CooperadoColors.blackText,
                  fontWeight: FontWeight.bold)),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: <Widget>[
              const Text(
                "Sua pontuação geral",
                style: TextStyle(color: CooperadoColors.grayLight2),
              ),
            BlocBuilder<SensitiveDataCubit, SensitiveDataState>(
  builder: (context, state) {
    bool isSensitiveDataVisible = state.isSensitiveDataVisible;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.end,
      children: <Widget>[
        Text(
          isSensitiveDataVisible ? widget.responseScore!.scoreFormatted : '******',
          style: const TextStyle(
              color: CooperadoColors.limaColor, fontSize: 30),
        ),
        const Text(
          "PONTOS",
          style: TextStyle(fontSize: 12),
        )
      ],
    );
  },
)
            ],
          ),
          BodyCard(
            list: widget.responseScore!.categorias,
          ),
        ],
      ),
    ));
  }

  Widget _listScore() {
    return ListView.builder(
        physics: const PageScrollPhysics(),
        shrinkWrap: true,
        itemCount: widget.responseScore!.historicoPontuacao!.length,
        itemBuilder: (context, index) {
          final item =
              widget.responseScore!.historicoPontuacao!.elementAt(index);
          return _itemCard(item);
        });
  }

  Widget _itemCard(HistoricoPontuacao historicoPontuacao) {
    return Padding(
      padding: const EdgeInsets.all(8.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: <Widget>[
          Padding(
            padding: const EdgeInsets.all(8.0),
            child: Text(
              historicoPontuacao.dataPontuacao!,
              textAlign: TextAlign.center,
            ),
          ),
          Row(
            children: [
              Categorias.howIcon(
                historicoPontuacao.categoriaEvento!.codigo,
              ),
              const SizedBox(width: 20),
              Expanded(
                child: Container(
                  padding: const EdgeInsets.all(12.0),
                  decoration: const BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.all(Radius.circular(8.0)),
                  ),
                  child: Column(
                    children: <Widget>[
                      Padding(
                        padding: const EdgeInsets.symmetric(
                            vertical: 12, horizontal: 35),
                        child: Text(
                          historicoPontuacao.categoriaEvento!.nome!,
                          textAlign: TextAlign.center,
                          style: const TextStyle(fontSize: 12),
                        ),
                      ),
                      Container(
                        width: MediaQuery.of(context).size.width,
                        padding: const EdgeInsets.symmetric(vertical: 12),
                        decoration: const BoxDecoration(
                          color: CooperadoColors.lightOrange,
                          borderRadius: BorderRadius.all(Radius.circular(8.0)),
                        ),
                        child: Column(
                          children: [
                            const Text(
                              'Parabéns! Você recebeu',
                              style: TextStyle(
                                color: CooperadoColors.lightOrangeText,
                                fontSize: 18,
                              ),
                              textAlign: TextAlign.center,
                            ),
                           BlocBuilder<SensitiveDataCubit, SensitiveDataState>(
  builder: (context, sensitiveState) {
    bool isSensitiveDataVisible = !sensitiveState.isSensitiveDataVisible;

    String displayValue = isSensitiveDataVisible
        ? '*****'
        : '${historicoPontuacao.valor} pontos';

    return Text(
      displayValue,
      style: const TextStyle(
        color: CooperadoColors.darkOrangeText,
        fontSize: 30,
      ),
    );
  },
)
                          ],
                        ),
                        // child: Html(
                        //   data: historicoPontuacao.descricao,
                        // ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          )
        ],
      ),
    );
  }
}
