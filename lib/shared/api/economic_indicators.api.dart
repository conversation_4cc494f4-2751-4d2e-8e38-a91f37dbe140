import 'dart:convert';

import 'package:cooperado_minha_unimed/models/economic_indicators.model.dart';
import 'package:cooperado_minha_unimed/shared/api/auth.api.dart';
import 'package:cooperado_minha_unimed/shared/exceptions.dart';
import 'package:cooperado_minha_unimed/shared/flavor_config.dart';
import 'package:http_client/http_client.dart';
import 'package:cooperado_minha_unimed/shared/locator.dart';
import 'package:cooperado_minha_unimed/shared/messages.exceptions.dart';
import 'package:cooperado_minha_unimed/shared/utils/logger_print.dart';

class EconomicIndicatorsApi {
  final UnimedHttpClient httpClient;

  EconomicIndicatorsApi(this.httpClient);

  final logger = UnimedLogger(className: 'DiretoriaApi');

  Future<List<EconomicIndicatorsModel>> getEconomicIndicators(
      String ano) async {
    final tokenPerfilApps =
        await Locator.instance!.get<AuthApi>().tokenPerfilApps();
    try {
      final url =
          '${FlavorConfig.instance!.values.profilePermissions.url}app/cooperado/indicadores-financeiros?ano=$ano';

      final response = await httpClient.get(
        Uri.parse(url),
        headers: {
          "Content-Type": "application/json",
          "Authorization": "Bearer $tokenPerfilApps",
        },
      );
      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);

        logger.e('getEconomicIndicators data: ${data.length}');

        final collection = (data as List)
            .map<EconomicIndicatorsModel>(
                (e) => EconomicIndicatorsModel.fromJson(e))
            .toList();

        return collection;
      } else if (response.statusCode == 500) {
        final message = jsonDecode(response.body);
        if (message['message'] == null) {
          throw EconomicIndicatorsException(MessageException.general);
        } else {
          logger.e('getEconomicIndicators exception: ${message['message']}');
          throw EconomicIndicatorsException("${message['message']}");
        }
      } else {
        final message = jsonDecode(response.body);
        if (message['message'] == null) {
          logger.e(
              'getEconomicIndicators statusCode : ${response.statusCode} ${response.body}');
          throw EconomicIndicatorsException(MessageException.general);
        } else {
          logger.e(
              'getEconomicIndicators statusCode : ${response.statusCode} ${response.body}');
          throw EconomicIndicatorsException("${message['message']}");
        }
      }
    } on UnimedException catch (ex) {
      logger.e('getTopics ${ex.runtimeType}: $ex');
      throw EconomicIndicatorsException(ex.message);
    } catch (ex) {
      logger.e('getEconomicIndicators exception: $ex');
      throw EconomicIndicatorsException(MessageException.general);
    }
  }

  Map<String, dynamic> converterMap(Map<String, Object?> originalMap) {
    Map<String, dynamic> novoMapa = {};

    originalMap.forEach((chave, valor) {
      if (valor is Map<String, Object?>) {
      } else if (valor is List<Object?>) {
        valor = valor.map((elemento) {
          if (elemento is Map<String, Object?>) {
            return converterMap(elemento);
          } else {
            return elemento;
          }
        }).toList();
      }
      novoMapa[chave] = valor;
    });

    return novoMapa;
  }
}
/* 
final  jsonExample = [
    {
        "jul": "-0,84%",
        "jun": "-0,66%",
        "set": "-0,89%",
        "ago": "-1,38%",
        "out": "-0,57%",
        "objetivo": 1,
        "abr": "0,69%",
        "mai": "-0,24%",
        "nov": "4,2%",
        "jan": "-3,64%",
        "fev": "2,53%",
        "nu_ano": "2023",
        "dez": "4,2%",
        "formula": 1,
        "ds_indicador": "Margem de Lucro Líquido",
        "mar": "0,24%"
    },
    {
        "jul": "-2,64%",
        "jun": "-1,75%",
        "set": "-3,67%",
        "ago": "-5,12%",
        "out": "-2,65%",
        "objetivo": 2,
        "abr": "1,18%",
        "mai": "-0,53%",
        "nov": "4,2%",
        "jan": "-1,57%",
        "fev": "2,08%",
        "nu_ano": "2023",
        "dez": "4,2%",
        "formula": 2,
        "ds_indicador": "Taxa de Retorno do Resultado sobre o Patrimônio Líquido",
        "mar": "0,31%"
    },
    {
        "jul": "90,58%",
        "jun": "90,31%",
        "set": "90,70%",
        "ago": "91,35%",
        "out": "89,91%",
        "objetivo": 3,
        "abr": "88,37%",
        "mai": "89,22%",
        "nov": "4,2%",
        "jan": "93,61%",
        "fev": "86,68%",
        "nu_ano": "2023",
        "dez": "4,2%",
        "formula": 3,
        "ds_indicador": "Percentual de Despesas Assistenciais em relação as Receitas de Contraprestações (Sinistralidade)",
        "mar": "88,90%"
    },
    {
        "jul": "8,35%",
        "jun": "8,41%",
        "set": "8,32%",
        "ago": "8,32%",
        "out": "8,62%",
        "objetivo": 4,
        "abr": "8,68%",
        "mai": "8,50%",
        "nov": "4,2%",
        "jan": "10,17%",
        "fev": "9,18%",
        "nu_ano": "2023",
        "dez": "4,2%",
        "formula": 4,
        "ds_indicador": "Percentual de Despesas Administrativas em relação as Receitas de Contraprestações (Desp Adm)",
        "mar": "8,76%"
    },
    {
        "jul": "0,49%",
        "jun": "0,50%",
        "set": "0,50%",
        "ago": "0,49%",
        "out": "0,51%",
        "objetivo": 5,
        "abr": "0,49%",
        "mai": "0,51%",
        "nov": "4,2%",
        "jan": "0,62%",
        "fev": "0,49%",
        "nu_ano": "2023",
        "dez": "4,2%",
        "formula": 5,
        "ds_indicador": "Percentual de Despesas de Comercialização em relação as Receitas de Contraprestações (Desp Com)",
        "mar": "0,54%"
    },
    {
        "jul": "94,18%",
        "jun": "93,86%",
        "set": "94,34%",
        "ago": "94,97%",
        "out": "93,62%",
        "objetivo": 6,
        "abr": "91,77%",
        "mai": "93,17%",
        "nov": "4,2%",
        "jan": "95,06%",
        "fev": "88,89%",
        "nu_ano": "2023",
        "dez": "4,2%",
        "formula": 6,
        "ds_indicador": "Percentual de Despesas Operacionais em relação às Receitas Operacionais (Desp Oper)",
        "mar": "92,08%"
    },
    {
        "jul": "2,03%",
        "jun": "1,99%",
        "set": "2,12%",
        "ago": "2,10%",
        "out": "2,12%",
        "objetivo": 7,
        "abr": "1,76%",
        "mai": "1,92%",
        "nov": null,
        "jan": "1,60%",
        "fev": "1,38%",
        "nu_ano": "2023",
        "dez": "4,2%",
        "formula": 7,
        "ds_indicador": "Índice de Resultado Financeiro (IRF)",
        "mar": "1,63%"
    },
    {
        "jul": "0,98",
        "jun": "1,00",
        "set": "0,97",
        "ago": "0,96",
        "out": "0,95",
        "objetivo": 8,
        "abr": "0,98",
        "mai": "0,98",
        "nov": "4,2%",
        "jan": "1,01",
        "fev": "1,01",
        "nu_ano": "2023",
        "dez": "4,2%",
        "formula": 8,
        "ds_indicador": "Índice de Liquidez Corrente",
        "mar": "0,98"
    },
    {
        "jul": "193,72%",
        "jun": "189,93%",
        "set": "196,14%",
        "ago": "197,89%",
        "out": "201,94%",
        "objetivo": 9,
        "abr": "184,33%",
        "mai": "189,31%",
        "nov": "4,2%",
        "jan": "188,51%",
        "fev": "179,18%",
        "nu_ano": "2023",
        "dez": "4,2%",
        "formula": 9,
        "ds_indicador": "Capital de Terceiros s/ Capital Proprio",
        "mar": "186,04%"
    },
    {
        "jul": "12",
        "jun": "12",
        "set": "13",
        "ago": "12",
        "out": "11",
        "objetivo": 10,
        "abr": "13",
        "mai": "12",
        "nov": "4,2%",
        "jan": "9",
        "fev": "12",
        "nu_ano": "2023",
        "dez": "4,2%",
        "formula": 10,
        "ds_indicador": "Prazo Médio de Recebimento de Contraprestações",
        "mar": "12"
    },
    {
        "jul": "39",
        "jun": "37",
        "set": "37",
        "ago": "39",
        "out": "37",
        "objetivo": 11,
        "abr": "37",
        "mai": "37",
        "nov": "4,2%",
        "jan": "34",
        "fev": "36",
        "nu_ano": "2023",
        "dez": "4,2%",
        "formula": 11,
        "ds_indicador": "Prazo Médio de Pagamento de Eventos",
        "mar": "37"
    },
    {
        "jul": "14,51%",
        "jun": "13,99%",
        "set": "14,81%",
        "ago": "15,62%",
        "out": "14,28%",
        "objetivo": 12,
        "abr": "14,52%",
        "mai": "13,61%",
        "nov": "4,2%",
        "jan": "25,35%",
        "fev": "16,35%",
        "nu_ano": "2023",
        "dez": "4,2%",
        "formula": 12,
        "ds_indicador": "Índice de Variação de Custos Acumulado (VC Exc Atual/Anter)",
        "mar": "16,25%"
    },

    //----------------------------------------------------------------------------------------,,
    
    {
    "jul": "-0,45%",
    "jun": "-0,75%",
    "set": "-0,92%",
    "ago": "-1,20%",
    "out": "-0,81%",
    "objetivo": 1,
    "abr": "0,55%",
    "mai": "-0,12%",
    "nov": "3,8%",
    "jan": "-3,21%",
    "fev": "2,19%",
    "nu_ano": "2022",
    "dez": "3,9%",
    "formula": 1,
    "ds_indicador": "Margem de Lucro Líquido",
    "mar": "0,36%"
},
{
    "jul": "-2,15%",
    "jun": "-1,89%",
    "set": "-3,43%",
    "ago": "-4,85%",
    "out": "-2,33%",
    "objetivo": 2,
    "abr": "0,95%",
    "mai": "-0,46%",
    "nov": "3,9%",
    "jan": "-1,45%",
    "fev": "2,30%",
    "nu_ano": "2022",
    "dez": "3,7%",
    "formula": 2,
    "ds_indicador": "Taxa de Retorno do Resultado sobre o Patrimônio Líquido",
    "mar": "0,28%"
},
{
    "jul": "91,02%",
    "jun": "90,47%",
    "set": "90,94%",
    "ago": "91,15%",
    "out": "90,25%",
    "objetivo": 3,
    "abr": "88,91%",
    "mai": "89,58%",
    "nov": "3,9%",
    "jan": "93,15%",
    "fev": "87,11%",
    "nu_ano": "2022",
    "dez": "3,8%",
    "formula": 3,
    "ds_indicador": "Percentual de Despesas Assistenciais em relação as Receitas de Contraprestações (Sinistralidade)",
    "mar": "89,18%"
},
{
    "jul": "8,22%",
    "jun": "8,56%",
    "set": "8,15%",
    "ago": "8,47%",
    "out": "8,74%",
    "objetivo": 4,
    "abr": "8,60%",
    "mai": "8,32%",
    "nov": "3,9%",
    "jan": "9,98%",
    "fev": "9,09%",
    "nu_ano": "2022",
    "dez": "3,7%",
    "formula": 4,
    "ds_indicador": "Percentual de Despesas Administrativas em relação as Receitas de Contraprestações (Desp Adm)",
    "mar": "8,85%"
},
{
    "jul": "0,47%",
    "jun": "0,53%",
    "set": "0,48%",
    "ago": "0,52%",
    "out": "0,50%",
    "objetivo": 5,
    "abr": "0,46%",
    "mai": "0,53%",
    "nov": "3,9%",
    "jan": "0,61%",
    "fev": "0,52%",
    "nu_ano": "2022",
    "dez": "3,8%",
    "formula": 5,
    "ds_indicador": "Percentual de Despesas de Comercialização em relação as Receitas de Contraprestações (Desp Com)",
    "mar": "0,49%"
},
{
    "jul": "93,78%",
    "jun": "92,94%",
    "set": "94,05%",
    "ago": "94,62%",
    "out": "93,91%",
    "objetivo": 6,
    "abr": "91,45%",
    "mai": "93,52%",
    "nov": "3,9%",
    "jan": "94,85%",
    "fev": "89,05%",
    "nu_ano": "2022",
    "dez": "3,8%",
    "formula": 6,
    "ds_indicador": "Percentual de Despesas Operacionais em relação às Receitas Operacionais (Desp Oper)",
    "mar": "92,51%"
},
{
    "jul": "2,05%",
    "jun": "1,42%",
    "set": "2,18%",
    "ago": "2,03%",
    "out": "1,88%",
    "objetivo": 7,
    "abr": "1,95%",
    "mai": "1,78%",
    "nov": null,
    "jan": "1,59%",
    "fev": "1,44%",
    "nu_ano": "2022",
    "dez": "3,7%",
    "formula": 7,
    "ds_indicador": "Índice de Resultado Financeiro (IRF)",
    "mar": "1,60%"
},
{
    "jul": "0,99",
    "jun": "0,97",
    "set": "1,00",
    "ago": "1,02",
    "out": "0,96",
    "objetivo": 8,
    "abr": "0,99",
    "mai": "1,01",
    "nov": "3,9%",
    "jan": "1,00",
    "fev": "1,02",
    "nu_ano": "2022",
    "dez": "3,8%",
    "formula": 8,
    "ds_indicador": "Índice de Liquidez Corrente",
    "mar": "1,00"
},
{
    "jul": "194,87%",
    "jun": "192,45%",
    "set": "194,73%",
    "ago": "196,32%",
    "out": "199,12%",
    "objetivo": 9,
    "abr": "184,91%",
    "mai": "189,93%",
    "nov": "3,9%",
    "jan": "188,16%",
    "fev": "179,85%",
    "nu_ano": "2022",
    "dez": "3,8%",
    "formula": 9,
    "ds_indicador": "Capital de Terceiros s/ Capital Proprio",
    "mar": "186,75%"
},
{
    "jul": "11",
    "jun": "12",
    "set": "12",
    "ago": "13",
    "out": "12",
    "objetivo": 10,
    "abr": "13",
    "mai": "12",
    "nov": "3,9%",
    "jan": "10",
    "fev": "11",
    "nu_ano": "2022",
    "dez": "3,8%",
    "formula": 10,
    "ds_indicador": "Prazo Médio de Recebimento de Contraprestações",
    "mar": "12"
},
{
    "jul": "38",
    "jun": "36",
    "set": "38",
    "ago": "40",
    "out": "37",
    "objetivo": 11,
    "abr": "38",
    "mai": "36",
    "nov": "3,9%",
    "jan": "35",
    "fev": "37",
    "nu_ano": "2022",
    "dez": "3,8%",
    "formula": 11,
    "ds_indicador": "Prazo Médio de Pagamento de Eventos",
    "mar": "36"
},
{
    "jul": "15,25%",
    "jun": "14,80%",
    "set": "15,45%",
    "ago": "15,72%",
    "out": "14,10%",
    "objetivo": 12,
    "abr": "14,65%",
    "mai": "13,25%",
    "nov": "3,9%",
    "jan": "25,10%",
    "fev": "16,15%",
    "nu_ano": "2022",
    "dez": "3,8%",
    "formula": 12,
    "ds_indicador": "Índice de Variação de Custos Acumulado (VC Exc Atual/Anter)",
    "mar": "16,40%"
}
]
;


 */