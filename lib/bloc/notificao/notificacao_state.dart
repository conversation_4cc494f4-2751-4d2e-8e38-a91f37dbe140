import 'package:cooperado_minha_unimed/models/notificacao/notification.model.dart';
import 'package:equatable/equatable.dart';

abstract class NotificacaoState extends Equatable {}

class InitialNotificaoState extends NotificacaoState {
  @override
  List<Object> get props => [];
}

class NotificacaoStateLoading extends NotificacaoState {
  @override
  List<Object> get props => [];
}

class DoneDeleteNotificacaoState extends NotificacaoState {
  @override
  List<Object> get props => [];
}

class NotificacaoStateLoaded extends NotificacaoState {
  final int? totalPages;
  final List<NotificationModel> notificacoes;

  NotificacaoStateLoaded({
    required this.notificacoes,
    required this.totalPages,
  });

  @override
  List<Object> get props => [notificacoes];
}

class NotificacaoStateError extends NotificacaoState {
  final String message;

  NotificacaoStateError({required this.message});

  @override
  List<Object> get props => [message];
}
