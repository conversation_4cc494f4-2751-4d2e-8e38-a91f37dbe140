import 'package:cooperado_minha_unimed/bloc/servicos/consultar-chbpm/consult_chbpm_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/servicos/consultar-chbpm/consult_chbpm_state.dart';
import 'package:cooperado_minha_unimed/colors.dart';
import 'package:cooperado_minha_unimed/shared/vo/servicos/procedure_list.vo.dart';
import 'package:cooperado_minha_unimed/shared/widgets/error-banner/error_banner.component.dart';
import 'package:cooperado_minha_unimed/shared/widgets/snack.dart';
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';

class ProcedureListScreen extends StatefulWidget {
  const ProcedureListScreen({super.key});

  @override
  ProcedureListScreenState createState() => ProcedureListScreenState();
}

class ProcedureListScreenState extends State<ProcedureListScreen> {
  FocusNode? focusTextFieldSearch;
  final TextEditingController _tecTexto = TextEditingController();
  ScrollController? scrollController;
  int currentPage = 1;
  int maxPages = 1;
  ProcedureList procedureList = ProcedureList();
  String? lastKeySearch;
  bool isEnableSearch = false;

  @override
  void initState() {
    super.initState();

    procedureList = ProcedureList();
    lastKeySearch = "";

    scrollController = ScrollController();

    context.read<ConsultChbpmCubit>().setInitialState();

    scrollController!.addListener(() {
      if (scrollController!.position.atEdge) {
        final value = _tecTexto.text.trim();
        if (currentPage <= maxPages && value.isNotEmpty) {
          context
              .read<ConsultChbpmCubit>()
              .getConsultChbpmEvent(value, currentPage);
          currentPage++;
        }
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text("Consulta CBHPM / TUSS"),
        backgroundColor: CooperadoColors.tealGreenDark,
      ),
      body: SafeArea(
        child: SingleChildScrollView(
          controller: scrollController,
          child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: <Widget>[
                Container(
                  color: CooperadoColors.tealGreen,
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 5),
                    child: _textField(context, _tecTexto),
                  ),
                ),
                const SizedBox(height: 10),
                BlocBuilder<ConsultChbpmCubit, ConsultChbpmState>(
                    builder: (context, state) {
                  if (state is DoneGetConsultChbpmState) {
                    maxPages = state.procedureList.totalPaginas!;

                    if (procedureList.listaProcedimentos == null) {
                      procedureList = state.procedureList;
                    } else {
                      procedureList.listaProcedimentos!
                          .addAll(state.procedureList.listaProcedimentos!);
                    }

                    return _listSolicitations(procedureList);
                  } else if (state is LoadingGetConsultChbpmState) {
                    return Column(
                      children: <Widget>[
                        _listSolicitations(procedureList),
                        const SpinKitCircle(
                          color: CooperadoColors.tealGreen,
                        )
                      ],
                    );
                  }
                  if (state is ErrorGetConsultChbpmState) {
                    return Center(child: ErrorBanner(message: state.message));
                  }
                  return Container(
                      alignment: Alignment.center,
                      child: const Text(
                          "O termo pesquisado deve ter no mínimo 3 caracteres",
                          textAlign: TextAlign.center,
                          style: TextStyle(color: CooperadoColors.grayDark)));
                })
              ]),
        ),
      ),
    );
  }

  Widget _listSolicitations(ProcedureList list) {
    List<Widget> widgets = [];

    if (list.listaProcedimentos == null || list.listaProcedimentos!.isEmpty) {
      return Container();
    }

    for (ListaProcedimentos e in list.listaProcedimentos!) {
      widgets.add(Padding(
          padding: const EdgeInsets.only(left: 10),
          child: Column(
            children: <Widget>[
              Text("${e.codigo} - ${e.tipo!.descricao!}"),
              const SizedBox(
                height: 10,
              )
            ],
          )));
      widgets.add(const Divider(color: CooperadoColors.grayDark));
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: widgets,
    );
  }

  // TODO verificar a possibilidade componentizar esse search para usar nas outras consultas da tela de serviços
  Widget _textField(BuildContext context, TextEditingController controller) {
    return Stack(
      children: <Widget>[
        Padding(
          padding: const EdgeInsets.only(top: 4.0, right: 52.0, bottom: 4.0),
          child: Container(
            decoration: const BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(10.0),
                  bottomLeft: Radius.circular(10.0)),
            ),
            child: TextFormField(
              enableInteractiveSelection: false,
              autofocus: false,
              controller: controller,
              textInputAction: TextInputAction.search,
              style: const TextStyle(color: unimedGreen),
              onChanged: (term) {
                setState(() => isEnableSearch = term.length > 2 ? true : false);
              },
              onFieldSubmitted: (term) {
                if (term.trim().length > 2) _search(context);
              },
              maxLength: 27,
              maxLines: 1,
              decoration: const InputDecoration(
                counter: Offstage(),
                hintText: 'Consulta CBHPM / TUSS',
                hintMaxLines: null,
                contentPadding: EdgeInsets.only(
                  top: 12,
                  left: 10,
                  right: 10,
                  bottom: 6,
                ),
                border: InputBorder.none,
              ),
            ),
          ),
        ),
        Align(
          alignment: Alignment.centerRight,
          child: Padding(
              padding: const EdgeInsets.only(top: 4, bottom: 10),
              child: Container(
                  decoration: const BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.only(
                        topRight: Radius.circular(20.0),
                      )),
                  child: Padding(
                    padding: const EdgeInsets.only(top: 2, right: 2, bottom: 2),
                    child: InkWell(
                      splashColor: unimedGreen.shade400, // inkwell color
                      child: Container(
                        decoration: BoxDecoration(
                            borderRadius: const BorderRadius.only(
                                topLeft: Radius.circular(10.0),
                                topRight: Radius.circular(20.0),
                                bottomLeft: Radius.circular(20.0)),
                            color: isEnableSearch
                                ? unimedGreen
                                : CooperadoColors.grayLight2.withAlpha(100)),
                        width: 52,
                        height: 44,
                        child: const Icon(
                          Icons.send,
                          color: Colors.white,
                        ),
                      ),
                      onTap: () {
                        if (isEnableSearch) _search(context);
                      },
                    ),
                  ))),
        ),
      ],
    );
  }

  void _search(BuildContext context) {
    final value = _tecTexto.text.trim();
    final ConsultChbpmState currentState =
        context.read<ConsultChbpmCubit>().state;
    if (value != lastKeySearch || currentState is ErrorGetConsultChbpmState) {
      currentPage = 1;
      maxPages = 1;
      procedureList.listaProcedimentos = [];
    }

    if (value.isEmpty || value.length < 3) {
      SchedulerBinding.instance.addPostFrameCallback((_) {
        ScaffoldMessenger.of(context).showSnackBar(
          Snack.warning(
            'Digite o que deseja procurar',
            duration: const Duration(seconds: 1),
          ),
        );
      });
    } else {
      SchedulerBinding.instance.addPostFrameCallback((_) {
        if (currentPage <= maxPages) {
          context
              .read<ConsultChbpmCubit>()
              .getConsultChbpmEvent(value, currentPage);

          currentPage++;
          lastKeySearch = value;
        }
      });
    }
  }
}
