part of 'splash_screen_cubit.dart';

abstract class SplashScreenState extends Equatable {
  const SplashScreenState();

  @override
  List<Object?> get props => [];
}

class InitialSplashScreenState extends SplashScreenState {}

class LoadingSplashScreenState extends SplashScreenState {
  @override
  List<Object> get props => [];
}

class LoadedSplashScreenState extends SplashScreenState {
  final bool isFirstTime;

  @override
  List<Object?> get props => [isFirstTime];

  const LoadedSplashScreenState({required this.isFirstTime});
}
