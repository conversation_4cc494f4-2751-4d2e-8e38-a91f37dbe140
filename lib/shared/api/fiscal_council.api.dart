import 'dart:convert';

import 'package:cooperado_minha_unimed/models/fiscal_council.model.dart';
import 'package:cooperado_minha_unimed/models/user.model.dart';
import 'package:cooperado_minha_unimed/shared/api/auth.api.dart';
import 'package:cooperado_minha_unimed/shared/exceptions.dart';
import 'package:cooperado_minha_unimed/shared/flavor_config.dart';
import 'package:http_client/exceptions/exceptions.dart';
import 'package:http_client/http_client.dart';
import 'package:cooperado_minha_unimed/shared/locator.dart';
import 'package:cooperado_minha_unimed/shared/messages.exceptions.dart';
import 'package:cooperado_minha_unimed/shared/utils/logger_print.dart';

class FiscalCouncilApi {
  final UnimedHttpClient httpClient;

  FiscalCouncilApi(this.httpClient);

  final logger = UnimedLogger(className: 'FiscalCouncilApi');

  Future<List<FiscalCouncil>> getFiscalCouncil() async {
    UserCredentials? credentials =
        await Locator.instance!.get<AuthApi>().getCredentials();

    final token = credentials != null
        ? await User.createToken(credentials: credentials)
        : "";

    try {
      final url =
          '${FlavorConfig.instance!.values.portal.url}conselho-fiscal?token=$token';
      final response = await httpClient.post(Uri.parse(url));
      final data = jsonDecode(utf8.decode(response.bodyBytes));
      if (response.statusCode == 200) {
        final collection = (data["retorno"]["conselheiros"] as List)
            .map<FiscalCouncil>((e) => FiscalCouncil.fromJson(e))
            .toList();
        logger.d('getFiscalCouncil success | list ${collection.length}');
        return collection;
      } else {
        final message = jsonDecode(response.body);
        logger.e(
            'getFiscalCouncil statusCode : ${response.statusCode} ${response.body}');
        throw FiscalCouncilException('$message');
      }
    } on UnimedException catch (ex) {
      logger.e('getFiscalCouncil ${ex.runtimeType}: $ex');
      throw FiscalCouncilException(ex.message);
    } catch (ex) {
      logger.e('getFiscalCouncil exception: $ex');
      throw FiscalCouncilException(MessageException.general);
    }
  }

  Future<List<CouncilTopics>> getTopics() async {
    UserCredentials? credentials =
        await Locator.instance!.get<AuthApi>().getCredentials();
    final token = credentials != null
        ? await User.createToken(credentials: credentials)
        : "";

    try {
      final url =
          '${FlavorConfig.instance!.values.portal.url}conselho-fiscal/fale-conosco/assuntos?token=$token';
      final response = await httpClient.post(Uri.parse(url));
      final data = jsonDecode(utf8.decode(response.bodyBytes));
      if (response.statusCode == 200) {
        final collection = (data["retorno"] as List)
            .map<CouncilTopics>((e) => CouncilTopics.fromJson(e))
            .toList();
        logger.d('getTopics success | list : ${collection.length}');
        return collection;
      } else if (response.statusCode == 500) {
        logger.e('getTopics status code 500');
        throw FiscalCouncilException(MessageException.general);
      } else {
        final message = jsonDecode(response.body);
        logger.e(
            'getTopics statusCode : ${response.statusCode} ${response.body}');
        throw FiscalCouncilException('$message');
      }
    } on UnimedException catch (ex) {
      logger.e('getTopics ${ex.runtimeType}: $ex');
      throw FiscalCouncilException(ex.message);
    } catch (ex) {
      logger.e('getTopics exception: $ex');
      throw FiscalCouncilException(MessageException.general);
    }
  }

  Future<String> sendMessageCouncil(
      {required String message,
      required String fone,
      required CouncilTopics councilTopics,
      required bool anonymous}) async {
    UserCredentials? credentials =
        await Locator.instance!.get<AuthApi>().getCredentials();
    final token = credentials != null
        ? await User.createToken(credentials: credentials)
        : "";
    final url =
        '${FlavorConfig.instance!.values.portal.url}conselho-fiscal/fale-conosco/enviar?token=$token';
    Map<String, Object?> assunto = {"codigo": councilTopics.codigo};
    Map<String, Object> resposta = {"assunto": assunto};
    Map<String, Object> faleConosco = {
      "telefone": fone,
      "mensagem": message,
      "resposta": resposta,
      "anonimo": anonymous
    };

    final body = jsonEncode({"faleConosco": faleConosco});

    try {
      final response = await httpClient.post(Uri.parse(url), body: body);

      final data = jsonDecode(response.body);
      if (response.statusCode == 200) {
        logger.d("sendMessageCouncil success => ${response.body}");
        //mensagem vindo da api apenas: 'sucesso'. foi pedido melhoria na mensagem de sucesso
        // return data['mensagem'];
        //TODO falar com equipe portal para melhorar a mensagem de sucesso
        return "Sua mensagem foi enviada com sucesso!";
      } else {
        logger.e(
            'sendMessageCouncil ${response.statusCode} found ${response.body}');
        throw FiscalCouncilException(data['mensagem']);
      }
    } on ServiceTimeoutException catch (e) {
      logger.e('sendMessageCouncil ServiceTimeoutException ${e.message}');
      throw FiscalCouncilException(e.message);
    } on UnimedException catch (ex) {
      logger.e('sendMessageCouncil ${ex.runtimeType}: $ex');
      throw FiscalCouncilException(ex.message);
    } catch (ex) {
      logger.e('sendMessageCouncil exception: $ex');
      throw FiscalCouncilException(MessageException.general);
    }
  }
}
