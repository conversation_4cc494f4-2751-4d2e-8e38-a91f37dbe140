import 'package:cooperado_minha_unimed/bloc/sensitive-data/sensitive_data_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/sensitive-data/sensitive_data_state.dart';
import 'package:cooperado_minha_unimed/bloc/transparency/beneficiaries_quantitative/beneficiaries_quantitative_cubit.dart';
import 'package:cooperado_minha_unimed/colors.dart';
import 'package:cooperado_minha_unimed/screens/transparencia/beneficiary/beneficiary-graphs/age_beneficiary_piegraph.dart';
import 'package:cooperado_minha_unimed/screens/transparencia/beneficiary/beneficiary-graphs/beneficiary_piegraph.dart';
import 'package:cooperado_minha_unimed/screens/transparencia/beneficiary/beneficiary-graphs/gender_donut_chart.dart';
import 'package:cooperado_minha_unimed/shared/utils/string_utils.dart';
import 'package:cooperado_minha_unimed/shared/vo/transparencia/quantitativo_beneficiarios.vo.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:personal_assistente_digital/screens/personal-assistant/widget/error-banner.component.dart';

// ignore: must_be_immutable
class BeneficiaryDetail extends StatefulWidget {
  QuantitativoBeneficiariosVO quantitativoBeneficiariosVO;
  BeneficiaryDetail({super.key, required this.quantitativoBeneficiariosVO});
  @override
  BeneficiaryDetailState createState() => BeneficiaryDetailState();
}

class BeneficiaryDetailState extends State<BeneficiaryDetail> {
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text("Beneficiários"),
        backgroundColor: CooperadoColors.tealGreenDark,
        actions: [
             Padding(
             padding: const EdgeInsets.only(right: 15.0),
             child: IconButton(
                       icon: BlocBuilder<SensitiveDataCubit, SensitiveDataState>(
              builder: (context, state) {
                return Icon(
                  state.isSensitiveDataVisible ? Icons.visibility : Icons.visibility_off,
                );
              },
                       ),
                       onPressed: () {
              context.read<SensitiveDataCubit>().toggleSensitiveDataVisibility();
                       },
                     ),
           ),
        ],
      ),
      body: SafeArea(
        child: Container(
          padding: const EdgeInsets.all(10.0),
          child: ListView(
            physics: const ClampingScrollPhysics(
                parent: AlwaysScrollableScrollPhysics()),
            children: <Widget>[
              _cardBeneficiaryQuantidy(),
              const SizedBox(
                height: 10,
              ),
              _cardQuantative(
                "Quantitativo Beneficiários por plano",
                "Plano",
                "Quantidade",
                widget.quantitativoBeneficiariosVO.quantitativoPlanos,
              ),
              const SizedBox(
                height: 10,
              ),
              _cardQuantative("Beneficiários por Sexo", "Sexo", "Quantidade",
                  widget.quantitativoBeneficiariosVO.quantitativoSexos),
              const SizedBox(
                height: 10,
              ),
              _cardQuantative(
                  "Beneficiários por faixa etária",
                  "Faixa Etária",
                  "Quantidade",
                  widget.quantitativoBeneficiariosVO.quantitativoFaixasEtarias),
              const SizedBox(height: 10),
            ],
          ),
        ),
      ),
    );
  }

  Widget _cardTittle(String tittle, {bool isRefresh = true}) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 20),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: <Widget>[
          Text(
            tittle,
            style:
                const TextStyle(color: CooperadoColors.tealGreen, fontSize: 16),
          ),
          Visibility(
            visible: isRefresh,
            child: InkWell(
              child: const Icon(Icons.refresh),
              onTap: () {
                context
                    .read<BeneficiariesQuantitativeCubit>()
                    .getBeneficiariesQuantitative();
              },
            ),
          )
        ],
      ),
    );
  }

  Widget _cardBeneficiaryQuantidy() {
    return Card(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          _cardTittle("Quantitativo de Beneficiários", isRefresh: false),
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 10),
            child: Row(
              children: <Widget>[
                const Expanded(
                  flex: 2,
                  child: Text(
                    'Beneficiários Ativos',
                    textAlign: TextAlign.center,
                    style: TextStyle(fontSize: 12),
                  ),
                ),
               Expanded(
  flex: 3,
  child: BlocBuilder<SensitiveDataCubit, SensitiveDataState>(
    builder: (context, sensitiveState) {
      bool isSensitiveDataVisible = !sensitiveState.isSensitiveDataVisible;
      String displayValue = isSensitiveDataVisible
          ? '*****'
          : StringUtils.formatThousands(widget.quantitativoBeneficiariosVO.quantidadeBeneficiarioAtivos);

      return Container(
        decoration: const BoxDecoration(
          border: Border(
            left: BorderSide(width: 1.0, color: CooperadoColors.greenLight3),
          ),
        ),
        child: Text(
          displayValue,
          textAlign: TextAlign.center,
          style: const TextStyle(
            fontSize: 38,
            color: CooperadoColors.indicatorsText,
          ),
        ),
      );
    },
  ),
)
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _cardQuantative(
      title, columnLabel1, columnLabel2, List<dynamic> quantitativo) {
    Widget cardwidget = Container();
    switch (title) {
      case "Quantitativo Beneficiários por plano":
        cardwidget = LimitedBox(
          maxHeight: MediaQuery.of(context).size.height * 0.6,
          child: BeneficiaryPieGraph(data: quantitativo),
        );
        break;

      case "Beneficiários por Sexo":
        cardwidget = LimitedBox(
          maxHeight: MediaQuery.of(context).size.height * 0.6,
          child: GenderDonutChart(
            animate: true,
            custos: quantitativo,
          ) /* BeneficiaryPieGraph(data: quantitativo, animate: true) */,
        );
        break;
      case "Beneficiários por faixa etária":
        cardwidget = LimitedBox(
          maxHeight: MediaQuery.of(context).size.height * 0.6,
          child: AgeBeneficiaryPieGraph(data: quantitativo),
        );
        break;
    }

    return Card(
      child: Builder(
        builder: (context) {
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: <Widget>[
              _cardTittle(title),
              BlocConsumer<BeneficiariesQuantitativeCubit,
                  BeneficiariesQuantitativeState>(
                listener: (context, state) {
                  if (state is LoadedBeneficiariesQuantitativeState) {
                    widget.quantitativoBeneficiariosVO =
                        state.quantitativoBeneficiarios;
                  }
                },
                builder: (context, state) {
                  if (state is ErrorBeneficiariesQuantitativeState) {
                    return const Center(
                      child: Column(
                        children: [
                          ErrorBanner(message: "Não foi possível no momento"),
                        ],
                      ),
                    );
                  } else if (state is LoadingBeneficiariesQuantitativeState) {
                    return const SpinKitCircle(
                        color: CooperadoColors.tealGreen);
                  }
                  return Column(
                    children: [
                      Visibility(
                        visible: quantitativo.isNotEmpty,
                        child: cardwidget,
                      ),
                      Visibility(
                        visible:
                            state is LoadedBeneficiariesQuantitativeState &&
                                quantitativo.isEmpty,
                        child: const Center(
                          child: Column(
                            children: [
                              ErrorBanner(
                                  message: "Não foi possível no momento"),
                            ],
                          ),
                        ),
                      ),
                    ],
                  );
                },
              ),
              const SizedBox(height: 8),
            ],
          );
        },
      ),
    );
  }
}
