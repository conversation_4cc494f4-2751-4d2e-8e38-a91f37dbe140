import 'package:cooperado_minha_unimed/shared/api/transparencia.api.dart';
import 'package:cooperado_minha_unimed/shared/locator.dart';
import 'package:cooperado_minha_unimed/shared/messages.exceptions.dart';
import 'package:cooperado_minha_unimed/shared/vo/transparencia/custos_assistenciais.vo.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';

part 'assistance_costs_state.dart';

class AssistanceCostsCubit extends Cubit<AssistanceCostsState> {
  AssistanceCostsCubit() : super(InitialAssistanceCostsState());

  DateTime _lastDate = DateTime.now().subtract(const Duration(days: 30));
  DateTime get lastDate => _lastDate;

  getAssistanceCosts({DateTime? dateReport}) async {
    if (dateReport != null) _lastDate = dateReport;

    final lastDateFormated = DateFormat("MM-yyyy").format(_lastDate);
    try {
      emit(LoadingAssistanceCostsState());

      final CustosAssistenciaisVO assistanceCosts = await Locator.instance!
              <TransparenciaApi>()
          .getCustosAssistenciais(dataRelatorio: lastDateFormated);

      DateTime? data;
      if (assistanceCosts.custos != null &&
          assistanceCosts.custos!.isNotEmpty) {
        data = DateTime.parse(
            "${assistanceCosts.custos![0].ano}${(assistanceCosts.custos![0].mes).toString().padLeft(2, '0')}01");
      }

      if (assistanceCosts.total == 0) {
        emit(const ErrorAssistanceCostsState(MessageException.genericNoData));
      } else {
        emit(LoadedAssistanceCostsState(assistanceCosts, data));
      }
    } catch (e) {
      emit(ErrorAssistanceCostsState(e.toString()));
    }
  }
}
