import 'package:flutter/material.dart';

class ErroService extends StatefulWidget {
  final String message;
  final Function() onPressed;
  const ErroService(
      {super.key, required this.message, required this.onPressed});

  @override
  State<ErroService> createState() => _ErroServiceState();
}

class _ErroServiceState extends State<ErroService> {
  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Center(
          child: Text(
            textAlign: TextAlign.center,
            widget.message,
          ),
        ),
        Padding(
          padding: const EdgeInsets.only(top: 12),
          child: ElevatedButton(
            onPressed: () {
              widget.onPressed();
            },
            child: const Text('Tentar novamente'),
          ),
        )
      ],
    );
  }
}
