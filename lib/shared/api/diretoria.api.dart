import 'dart:convert';

import 'package:cooperado_minha_unimed/models/diretoria.model.dart';
import 'package:cooperado_minha_unimed/models/user.model.dart';
import 'package:cooperado_minha_unimed/shared/api/auth.api.dart';
import 'package:cooperado_minha_unimed/shared/exceptions.dart';
import 'package:cooperado_minha_unimed/shared/flavor_config.dart';
import 'package:http_client/http_client.dart';
import 'package:cooperado_minha_unimed/shared/locator.dart';
import 'package:cooperado_minha_unimed/shared/utils/logger_print.dart';

class DiretoriaApi {
  final UnimedHttpClient httpClient;

  DiretoriaApi(this.httpClient);

  final logger = UnimedLogger(className: 'DiretoriaApi');

  Future<RetornoDiretoria> getDiretoria() async {
    UserCredentials? credentials =
        await Locator.instance!.get<AuthApi>().getCredentials();
    final token = credentials != null
        ? await User.createToken(credentials: credentials)
        : "";

    try {
      final url =
          '${FlavorConfig.instance!.values.portal.url}diretoria?token=$token';
      final response = await httpClient.post(Uri.parse(url));
      if (response.statusCode == 200) {
        final result =
            RetornoDiretoria.fromJson(jsonDecode(response.body)['retorno']);
        logger.d(
            'getDiretoria success | list diretors ${result.diretores} list council ${result.conselhos}');
        return result;
      } else {
        final message = jsonDecode(response.body);
        logger.e(
            'getDiretoria statusCode : ${response.statusCode} ${response.body}');
        throw DiretoriaException('$message');
      }
    } on UnimedException catch (ex) {
      logger.e('getDiretoria ${ex.runtimeType} : $ex');
      throw DiretoriaException(ex.message);
    } catch (ex) {
      logger.e('getDiretoria exception : $ex');
      throw DiretoriaException('Não foi possível no momento.');
    }
  }
}
