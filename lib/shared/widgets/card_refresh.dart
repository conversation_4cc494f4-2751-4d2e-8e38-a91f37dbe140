import 'package:cooperado_minha_unimed/colors.dart';
import 'package:flutter/material.dart';

class CardRefresh extends StatelessWidget {
  final Widget? child;
  final Widget title;
  final Widget? refresh;
  const CardRefresh(
      {super.key,
      this.child,
      this.title = const Text('Tí<PERSON><PERSON>',
          style: TextStyle(
            color: CooperadoColors.blackText,
            fontSize: 16,
            fontWeight: FontWeight.bold,
          )),
      this.refresh});
  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 4,
      margin: const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 20),
        child: Column(
          children: [
            Row(
              children: [
                Expanded(
                  child: title,
                ),
                refresh ?? Container()
              ],
            ),
            const SizedBox(height: 15),
            child ?? Container(),
          ],
        ),
      ),
    );
  }
}
