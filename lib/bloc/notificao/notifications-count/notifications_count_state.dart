import 'package:equatable/equatable.dart';

abstract class NotificationsCountState extends Equatable {}

class InitialNotificationsCountState extends NotificationsCountState {
  @override
  List<Object> get props => [];
}

class NotificationsCountStateLoading extends NotificationsCountState {
  @override
  List<Object> get props => [];
}

class NotificationsCountStateLoaded extends NotificationsCountState {
  final int notificationsCount;

  NotificationsCountStateLoaded({required this.notificationsCount});

  @override
  List<Object> get props => [];
}

class NotificationsCountStateError extends NotificationsCountState {
  final String message;

  NotificationsCountStateError({required this.message});

  @override
  List<Object> get props => [message];
}
