import 'package:cooperado_minha_unimed/models/res/res_attendance_model.dart';
import 'package:cooperado_minha_unimed/shared/api/graphql-res.api.dart';
import 'package:cooperado_minha_unimed/shared/locator.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

part 'res_documents_state.dart';

class ResDocumentsCubit extends Cubit<ResDocumentsState> {
  ResDocumentsCubit() : super(InitialResDocumentsState());

  List<ResAttendanceModel> _listDocuments = List.empty(growable: true);
  List<ResAttendanceModel> get listDocuments => _listDocuments;

  void listResDocuments(
      {required String crm,
      required String card,
      DateTimeRange? dataRange}) async {
    try {
      emit(LoadingResDocumentsState());

      _listDocuments = await Locator.instance!<ResGraphQlApi>()
          .resGetAttendanceByType(
              crm: crm, card: card, type: "documentos", dataRange: dataRange);

      _listDocuments.isEmpty
          ? emit(const NoDataResDocumentsState())
          : emit(LoadedResDocumentsState(listResDocuments: _listDocuments));
    } catch (e) {
      emit(ErrorResDocumentsState(message: e.toString()));
    }
  }
}
