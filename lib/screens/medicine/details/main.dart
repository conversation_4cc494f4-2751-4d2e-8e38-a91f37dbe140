import 'package:cooperado_minha_unimed/colors.dart';
import 'package:cooperado_minha_unimed/models/medicine.model.dart';
import 'package:flutter/material.dart';

class MedicineDetailsScreen extends StatefulWidget {
  const MedicineDetailsScreen({super.key, required this.medicine});
  final MedicineModel medicine;

  @override
  State<MedicineDetailsScreen> createState() => _MedicineDetailsScreenState();
}

class _MedicineDetailsScreenState extends State<MedicineDetailsScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        centerTitle: true,
        title: const Text("Detalhes do Medicamento"),
        backgroundColor: CooperadoColors.tealGreenDark,
      ),
      body: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16),
        child: Hero(
          tag: widget.medicine.codProduto ?? '',
          child: Card(
            margin: const EdgeInsets.symmetric(vertical: 16),
            child: SizedBox(
              width: MediaQuery.of(context).size.width,
              child: SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildItemText(
                      title: 'Código do produto',
                      value: widget.medicine.codProduto,
                    ),
                    _buildItemText(
                      title: 'Código da unidade',
                      value: widget.medicine.codigoUnidade,
                    ),
                    _buildItemText(
                      title: 'Valor da última compra',
                      value: widget.medicine.valorUltimaEntradaFormatado,
                    ),
                    _buildItemText(
                      title: 'Produto',
                      value: widget.medicine.descricaoProduto,
                    ),
                    _buildItemText(
                      title: 'Unidade',
                      value: widget.medicine.descricaoUnidade,
                    ),
                    _buildItemText(
                      title: 'Princípio ativo',
                      value: widget.medicine.descricaoSubstancia,
                    ),
                    _buildItemText(
                      title: 'Classe',
                      value: widget.medicine.descricaoClasse,
                    ),
                    _buildItemText(
                      title: 'Data da última compra',
                      value: widget.medicine.dataUltimaEntradaFormatada,
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildItemText({required title, required String? value}) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: const TextStyle(
              color: CooperadoColors.blackText,
              fontSize: 14,
            ),
          ),
          Text(
            value ?? 'Não informado',
            style: const TextStyle(
              color: CooperadoColors.blackText,
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }
}
