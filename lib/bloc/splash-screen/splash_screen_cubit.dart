import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shared_preferences/shared_preferences.dart';

part 'splash_screen_state.dart';

class SplashScreenCubit extends Cubit<SplashScreenState> {
  SplashScreenCubit() : super(InitialSplashScreenState());

  String firsTime = "firstTime";

  Future<void> checkFirstTime() async {
    try {
      SharedPreferences prefs = await SharedPreferences.getInstance();
      final isFirstTime = prefs.getBool(firsTime);
      if (isFirstTime != null && !isFirstTime) {
        prefs.setBool(firsTime, false);

        emit(const LoadedSplashScreenState(isFirstTime: false));
      } else {
        prefs.setBool(firsTime, false);
        emit(const LoadedSplashScreenState(isFirstTime: true));
      }
    } catch (e) {
      emit(const LoadedSplashScreenState(isFirstTime: true));
    }
  }
}
