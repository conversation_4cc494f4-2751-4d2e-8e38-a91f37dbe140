import 'package:cooperado_minha_unimed/models/res/res_brasil_beneficiary_model.dart';
import 'package:cooperado_minha_unimed/shared/api/graphql-res.api.dart';
import 'package:cooperado_minha_unimed/shared/exceptions.dart';
import 'package:cooperado_minha_unimed/shared/locator.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

part 'res_brasil_beneficiary_state.dart';

class ResBrazilBeneficiaryCubit extends Cubit<ResBrasilBeneficiaryState> {
  ResBrazilBeneficiaryCubit() : super(InitialResBrasilBeneficiaryState());

  void resBrazilBeneficiaryByCpf(
      {required String cpf, required String crm}) async {
    try {
      emit(LoadingResBrasilBeneficiaryState());

      final ResBrazilBeneficiaryModel resBrazilBeneficiary = await Locator
          .instance!<ResGraphQlApi>()
          .resBrazilBeneficiaryByCpf(cpf: cpf, crm: crm);

      emit(
        LoadedResBrasilBeneficiaryState(
            resBrazilBeneficiaryModel: resBrazilBeneficiary),
      );
    } on NotFoundException {
      emit(ErrorResBrasilBeneficiaryNotFoundState());
    } catch (e) {
      emit(ErrorResBrasilBeneficiaryState(message: e.toString()));
    }
  }

  void resetState() {
    emit(InitialResBrasilBeneficiaryState());
  }
}
