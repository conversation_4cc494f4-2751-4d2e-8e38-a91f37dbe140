class ProductionHistoryVO {
  int? ano;
  List<ProductionReport>? meses;

  ProductionHistoryVO({this.ano, this.meses});

  String get ano2Digitos {
    final stringAno = '$ano'.padLeft(4, '0');
    return stringAno.substring(2, 4).toString();
  }

  ProductionHistoryVO.fromJson(Map<String, dynamic> json) {
    ano = json['ano'];
    if (json['meses'] != null) {
      meses = [];
      json['meses'].forEach((v) {
        meses!.add(ProductionReport.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['ano'] = ano;
    if (meses != null) {
      data['meses'] = meses!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class ProductionReport {
  int? mes;
  List<Producoes>? producoes;
  dynamic total;

  ProductionReport({this.mes, this.producoes, this.total});

  ProductionReport.fromJson(Map<String, dynamic> json) {
    mes = json['mes'];
    if (json['producoes'] != null) {
      producoes = [];
      json['producoes'].forEach((v) {
        producoes!.add(Producoes.fromJson(v));
      });
    }
    total = json['total'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['mes'] = mes;
    if (producoes != null) {
      data['producoes'] = producoes!.map((v) => v.toJson()).toList();
    }
    data['total'] = total;
    return data;
  }
}

class Producoes {
  String? descricao;
  String? tipoProducao;
  String? dataProducao;
  String? dataProducaoFim;
  String? dataAtendimento;
  dynamic valor;

  Producoes(
      {this.descricao,
      this.tipoProducao,
      this.dataProducao,
      this.dataProducaoFim,
      this.dataAtendimento,
      this.valor});

  Producoes.fromJson(Map<String, dynamic> json) {
    descricao = json['descricao'];
    tipoProducao = json['tipoProducao'];
    dataProducao = json['dataProducao'];
    dataProducaoFim = json['dataProducaoFim'];
    dataAtendimento = json['dataAtendimento'];
    valor = json['valor'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['descricao'] = descricao;
    data['tipoProducao'] = tipoProducao;
    data['dataProducao'] = dataProducao;
    data['dataProducaoFim'] = dataProducaoFim;
    data['dataAtendimento'] = dataAtendimento;
    data['valor'] = valor;
    return data;
  }
}
