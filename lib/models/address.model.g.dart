// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'address.model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

AddressModel _$AddressModelFromJson(Map json) => AddressModel(
      code: json['codigo'] as int?,
      complement: json['complemento'] as String?,
      contacts: (json['contatos'] as List<dynamic>?)
          ?.map((e) => ContactModel.fromJson(e as Map))
          .toList(),
      correspondence: json['correspondencia'] as String?,
      number: json['numero'] as String?,
      type: json['tipo'] as String?,
      zipcode: json['cep'] == null
          ? null
          : ZipCodeModel.fromJson(json['cep'] as Map),
    );

Map<String, dynamic> _$AddressModelToJson(AddressModel instance) =>
    <String, dynamic>{
      'codigo': instance.code,
      'tipo': instance.type,
      'numero': instance.number,
      'complemento': instance.complement,
      'correspondencia': instance.correspondence,
      'cep': instance.zipcode,
      'contatos': instance.contacts,
    };

ContactModel _$ContactModelFromJson(Map json) => ContactModel(
      contact: json['contato'] as String?,
      type: json['tipo'] == null
          ? null
          : TypeContactModel.fromJson(json['tipo'] as Map),
    );

Map<String, dynamic> _$ContactModelToJson(ContactModel instance) =>
    <String, dynamic>{
      'contato': instance.contact,
      'tipo': instance.type,
    };

TypeContactModel _$TypeContactModelFromJson(Map json) => TypeContactModel(
      code: json['codigo'] as int?,
      name: json['nome'] as String?,
    );

Map<String, dynamic> _$TypeContactModelToJson(TypeContactModel instance) =>
    <String, dynamic>{
      'codigo': instance.code,
      'nome': instance.name,
    };
