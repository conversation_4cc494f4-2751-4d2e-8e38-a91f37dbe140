import 'package:cooperado_minha_unimed/colors.dart';
import 'package:cooperado_minha_unimed/shared/utils/uicons.dart';
import 'package:flutter/material.dart';

class TransparencyCollapsibleHead extends StatefulWidget {
  final String? title;
  final String? dividend;
  final String? divider;
  final String? quotient;
  final String? quotientSub;

  const TransparencyCollapsibleHead(
      {super.key,
      this.title,
      this.dividend,
      this.divider,
      this.quotient,
      this.quotientSub});

  @override
  CardPortalTransparencia createState() => CardPortalTransparencia();
}

class CardPortalTransparencia extends State<TransparencyCollapsibleHead> {
  double total = 0.0;
  DateTime? selectedDateTime;
  bool _isExpanded = false;

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final Size size = MediaQuery.of(context).size;
    return Card(
      margin: const EdgeInsets.only(left: 0, right: 0, bottom: 10.0),
      child: Container(
        decoration: BoxDecoration(
            color: _isExpanded
                ? CooperadoColors.aliceBlue
                : CooperadoColors.tealGreen,
            borderRadius: const BorderRadius.only(
                bottomLeft: Radius.circular(10.0),
                topLeft: Radius.circular(10.0),
                topRight: Radius.circular(10.0))),
        child: Theme(
            data: ThemeWidgets.expansionTile(
              context,
              colorArrow:
                  _isExpanded ? CooperadoColors.tealGreen : Colors.black,
            ),
            child: ListTileTheme(
                contentPadding: const EdgeInsets.symmetric(horizontal: 10),
                child: ExpansionTile(
                    collapsedIconColor: Colors.white,
                    onExpansionChanged: (value) {
                      setState(() {
                        _isExpanded = value;
                      });
                    },
                    title: Text(
                      widget.title!,
                      style: TextStyle(
                        color: _isExpanded
                            ? CooperadoColors.blackText
                            : Colors.white,
                      ),
                    ),
                    children: <Widget>[
                      Center(
                        child: Padding(
                          padding: const EdgeInsets.only(
                              left: 8.0, top: 10.0, bottom: 10.0, right: 8.0),
                          child: Stack(
                            children: <Widget>[
                              Column(
                                crossAxisAlignment: CrossAxisAlignment.center,
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: <Widget>[
                                  _card(widget.dividend!,
                                      CooperadoColors.limaColor, size),
                                  _card(widget.divider!,
                                      CooperadoColors.tealGreenDark, size),
                                  _card(widget.quotient!,
                                      CooperadoColors.orange, size,
                                      subtitle: widget.quotientSub!),
                                ],
                              ),
                              Positioned(
                                top: 39,
                                child: SizedBox(
                                  width: size.width - (size.width * 0.24),
                                  child: Column(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    crossAxisAlignment:
                                        CrossAxisAlignment.center,
                                    children: <Widget>[
                                      Container(
                                        height: 30,
                                        width: 30,
                                        decoration: BoxDecoration(
                                          color: Colors.white,
                                          borderRadius:
                                              BorderRadius.circular(30),
                                        ),
                                        child: const Icon(
                                          UIcons.division,
                                          size: 14,
                                        ),
                                      ),
                                      const SizedBox(height: 35),
                                      Container(
                                        height: 30,
                                        width: 30,
                                        decoration: BoxDecoration(
                                          color: Colors.white,
                                          borderRadius:
                                              BorderRadius.circular(30),
                                        ),
                                        child: const Icon(
                                          UIcons.equals,
                                          size: 14,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              )
                            ],
                          ),
                        ),
                      ),

                      //_headerCard(context),
                    ]))),
      ),
    );
  }

  Widget _card(String text, Color color, Size size, {String subtitle = ''}) {
    return Container(
      width: size.width - (size.width * 0.24),
      padding: const EdgeInsets.symmetric(vertical: 15.0),
      margin: const EdgeInsets.only(bottom: 7.0),
      decoration:
          BoxDecoration(color: color, borderRadius: BorderRadius.circular(8.0)),
      child: Column(
        children: <Widget>[
          Text(
            text,
            style: const TextStyle(
              color: Colors.white,
              fontSize: 18,
            ),
            textAlign: TextAlign.center,
          ),
          if (subtitle.isNotEmpty)
            Text(
              subtitle,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 14,
              ),
              textAlign: TextAlign.center,
            )
        ],
      ),
    );
  }
}
