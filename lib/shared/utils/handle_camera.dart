// ignore_for_file: use_build_context_synchronously

import 'package:cooperado_minha_unimed/colors.dart';
import 'package:cooperado_minha_unimed/shared/utils/logger_print.dart';
import 'package:cooperado_minha_unimed/shared/widgets/alert.dart';
import 'package:cooperado_minha_unimed/shared/widgets/camera_screen.dart';
import 'package:flutter/material.dart';
import 'package:permission_handler/permission_handler.dart';

final logger = UnimedLogger(className: 'HandleCamera');

class HandleCamera {
  static requestCameraPermission(BuildContext context) async {
    final cameraStatus = await Permission.camera.status;
    logger.i('Permissão da Câmera => $cameraStatus');

    if (cameraStatus != PermissionStatus.granted) {
      Alert.open(
        context,
        title: 'Acesso a câmera',
        text: 'Precisamos acessar sua câmera para anexar documentos',
        textButtonClose: 'Fechar',
        // callbackClose: () {},
        actions: <Widget>[
          ElevatedButton(
            style: ElevatedButton.styleFrom(
              foregroundColor: Colors.white,
              backgroundColor: CooperadoColors.tealGreen,
              shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(5.0)),
            ),
            child: const Text('Autorizar'),
            onPressed: () {
              if (cameraStatus == PermissionStatus.permanentlyDenied) {
                Navigator.of(context).pop();
                openAppSettings();
              } else {
                Permission.camera.request().then((_) {
                  Navigator.of(context).pop();
                });
              }
            },
          ),
        ],
      );
    }
  }

  static openCameraScreen(BuildContext context, Function onTakePhoto) {
    Navigator.push(
      context,
      MaterialPageRoute(
          builder: (context) => CameraScreen(
                onTakePhoto: onTakePhoto,
              )),
    );
  }
}
