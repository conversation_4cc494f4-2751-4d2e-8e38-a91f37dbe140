import 'package:cooperado_minha_unimed/colors.dart';
import 'package:cooperado_minha_unimed/shared/utils/string_utils.dart';
import 'package:cooperado_minha_unimed/shared/utils/uicons.dart';
import 'package:flutter/material.dart';

class ResponseScore {
  int? pontuacaoTotal;
  List<HistoricoPontuacao>? historicoPontuacao;
  List<Categorias>? categorias;

  ResponseScore(
      {this.pontuacaoTotal, this.historicoPontuacao, this.categorias});

  String get scoreFormatted => StringUtils.pontuationSeparator(pontuacaoTotal);

  ResponseScore.fromJson(Map<String, dynamic> json) {
    pontuacaoTotal = json['pontuacaoTotal'];
    if (json['historicoPontuacao'] != null) {
      historicoPontuacao = [];
      json['historicoPontuacao'].forEach((v) {
        historicoPontuacao!.add(HistoricoPontuacao.fromJson(v));
      });
    }
    if (json['categorias'] != null) {
      categorias = [];
      json['categorias'].forEach((v) {
        categorias!.add(Categorias.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['pontuacaoTotal'] = pontuacaoTotal;
    if (historicoPontuacao != null) {
      data['historicoPontuacao'] =
          historicoPontuacao!.map((v) => v.toJson()).toList();
    }
    if (categorias != null) {
      data['categorias'] = categorias!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class HistoricoPontuacao {
  int? codigo;
  String? codigoCategoriaPontuacao;
  String? codigoEventoPontuacao;
  String? nome;
  String? descricao;
  int? valor;
  String? dataPontuacao;
  Categorias? categoriaEvento;

  HistoricoPontuacao(
      {this.codigo,
      this.codigoCategoriaPontuacao,
      this.codigoEventoPontuacao,
      this.nome,
      this.descricao,
      this.valor,
      this.dataPontuacao,
      this.categoriaEvento});

  String get scoreFormatted => StringUtils.pontuationSeparator(valor);
  HistoricoPontuacao.fromJson(Map<String, dynamic> json) {
    codigo = json['codigo'];
    codigoCategoriaPontuacao = json['codigoCategoriaPontuacao'];
    codigoEventoPontuacao = json['codigoEventoPontuacao'];
    nome = json['nome'];
    descricao = json['descricao'];
    valor = json['valor'];
    dataPontuacao = json['dataPontuacao'];
    categoriaEvento = json['categoriaEvento'] != null
        ? Categorias.fromJson(json['categoriaEvento'])
        : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['codigo'] = codigo;
    data['codigoCategoriaPontuacao'] = codigoCategoriaPontuacao;
    data['codigoEventoPontuacao'] = codigoEventoPontuacao;
    data['nome'] = nome;
    data['descricao'] = descricao;
    data['valor'] = valor;
    data['dataPontuacao'] = dataPontuacao;
    if (categoriaEvento != null) {
      data['categoriaEvento'] = categoriaEvento!.toJson();
    }
    return data;
  }
}

class Categorias {
  int? codigo;
  String? nome;
  int? pontuacaoTotalCategoria;

  Categorias({this.codigo, this.nome, this.pontuacaoTotalCategoria});

  String get scoreFormatted =>
      StringUtils.pontuationSeparator(pontuacaoTotalCategoria);

  Categorias.fromJson(Map<String, dynamic> json) {
    codigo = json['codigo'];
    nome = json['nome'];
    pontuacaoTotalCategoria = json['pontuacaoTotalCategoria'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['codigo'] = codigo;
    data['nome'] = nome;
    data['pontuacaoTotalCategoria'] = pontuacaoTotalCategoria;
    return data;
  }

  static Widget howIcon(int? codigo) {
    if (codigo == 1) {
      return Container(
          padding: const EdgeInsets.all(8.0),
          decoration: const BoxDecoration(
              color: CooperadoColors.limaColor,
              borderRadius: BorderRadius.all(Radius.elliptical(50, 50))),
          child: const Icon(
            UIcons.group,
            color: Colors.white,
          ));
    } else if (codigo == 3) {
      return Container(
          padding: const EdgeInsets.all(8.0),
          decoration: const BoxDecoration(
              color: CooperadoColors.limaColor,
              borderRadius: BorderRadius.all(Radius.elliptical(50, 50))),
          child: const Icon(
            Icons.check_box,
            color: Colors.white,
          ));
    } else {
      return Container(
          padding: const EdgeInsets.all(8.0),
          decoration: const BoxDecoration(
              color: CooperadoColors.limaColor,
              borderRadius: BorderRadius.all(Radius.elliptical(50, 50))),
          child: const Icon(
            Icons.school,
            color: Colors.white,
          ));
    }
  }
}
