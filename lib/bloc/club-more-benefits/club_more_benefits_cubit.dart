import 'package:cooperado_minha_unimed/bloc/club-more-benefits/club_more_benefits_state.dart';
import 'package:cooperado_minha_unimed/shared/api/club_more_benefits_graphql.api.dart';
import 'package:cooperado_minha_unimed/shared/locator.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class ClubMoreBenefitsCubit extends Cubit<ClubMoreBenefitsState> {
  ClubMoreBenefitsCubit() : super(ClubMoreBenefitsInitialState());

  void checkBenefitsClubUserRegistration() async {
    try {
      emit(LoadingClubMoreBenefitsState());

      final clubMoreBenefits = await Locator.instance!<ClubMoreBenefitsGraphQlApi>().checkBenefitsClubUserRegistration();

      emit(DoneCheckUserRegistrationState(clubMaisVantagens: clubMoreBenefits));
    } catch (e) {
      emit(ErrorClubMoreBenefitsState(message: e.toString()));
    }
  }

  void registerBenefitsClubUser() async {
    try {
      emit(LoadingClubMoreBenefitsState());

      await Locator.instance!<ClubMoreBenefitsGraphQlApi>().registerBenefitsClubUser();

      emit(DoneRegisterUserClubeMaisVantagens());
    } catch (e) {
      emit(ErrorClubMoreBenefitsState(message: e.toString()));
    }
  }
}
