import 'package:cooperado_minha_unimed/bloc/indicators/last_production/last_production_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/sensitive-data/sensitive_data_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/sensitive-data/sensitive_data_state.dart';
import 'package:cooperado_minha_unimed/colors.dart';
import 'package:cooperado_minha_unimed/screens/indicadores/attendace-comparative/main.dart';
import 'package:cooperado_minha_unimed/screens/indicadores/historical-production/main.dart';
import 'package:cooperado_minha_unimed/screens/indicadores/producao-medica/main.dart';
import 'package:cooperado_minha_unimed/shared/screen-transitions/fade.transition.dart';
import 'package:cooperado_minha_unimed/shared/widgets/comparative-production/view/card_comparative_production.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';

class MedicalProductionScreen extends StatefulWidget {
  final FirebaseAnalytics analytics;
  final FirebaseAnalyticsObserver observer;

  const MedicalProductionScreen(
      {super.key, required this.analytics, required this.observer});

  @override
  MedicalProductionScreenState createState() => MedicalProductionScreenState();
}

class MedicalProductionScreenState extends State<MedicalProductionScreen> {
  late final DateTime lastDate;

  @override
  void initState() {
    super.initState();
    lastDate = context.read<LastProductionCubit>().lastDate ?? DateTime.now();
    widget.analytics.logScreenView(
      screenName: 'Produção Médica',
      screenClass: 'MedicalProductionScreen',
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        centerTitle: true,
        title: const Text("Produção Médica"),
        actions: [
             Padding(
             padding: const EdgeInsets.only(right: 15.0),
             child: IconButton(
                       icon: BlocBuilder<SensitiveDataCubit, SensitiveDataState>(
              builder: (context, state) {
                return Icon(
                  state.isSensitiveDataVisible ? Icons.visibility : Icons.visibility_off,
                );
              },
                       ),
                       onPressed: () {
              context.read<SensitiveDataCubit>().toggleSensitiveDataVisibility();
                       },
                     ),
           ),
        ],
      ),
      body: BlocConsumer<LastProductionCubit, LastProductionState>(
        listener: (context, state) {
          // if (state is LoadedLastProductionState) {
          //   lastDate = context.read<LastProductionCubit>().lastDate;
          // }
        },
        builder: (context, state) {
          if (state is LoadingLastProductionState) {
            return const SpinKitCircle(
              color: CooperadoColors.tealGreen,
            );
          } else if (state is ErrorLastProductionState) {
            return const Center(
              child: Padding(
                padding: EdgeInsets.all(20),
                child: Text(
                  "Ocorreu um erro ao tentar listar as informações de produção médica. Por favor, tente novamente mais tarde.",
                  textAlign: TextAlign.center,
                ),
              ),
            );
          }
          return RefreshIndicator(
            onRefresh: () => Navigator.pushReplacement(
              context,
              FadeRoute(
                page: MedicalProductionScreen(
                  analytics: widget.analytics,
                  observer: widget.observer,
                ),
              ),
            ),
            color: CooperadoColors.tealGreen,
            child: SafeArea(
              child: SingleChildScrollView(
                child: Column(
                  children: <Widget>[
                    CardComparativoProducao(
                      lastDate: lastDate,
                    ),
                    ProducaoMedicaScreen(
                      lastDate: lastDate,
                    ),
                    ProductionHistoryScreen(
                      lastDate: lastDate,
                    ),
                    AttendanceComparativeScreen(lastDate: lastDate),
                  ],
                ),
              ),
            ),
          );
        },
      ),
    );
  }
}
