// ignore_for_file: use_build_context_synchronously

import 'package:cooperado_minha_unimed/bloc/auth/auth_cubit.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:url_launcher/url_launcher.dart';

class InsuranceScreen extends StatefulWidget {
  const InsuranceScreen({super.key});

  @override
  State<InsuranceScreen> createState() => _InsuranceScreenState();
}

class _InsuranceScreenState extends State<InsuranceScreen> {
  @override
  void initState() {
    super.initState();
    _launchURL();
  }
Future<void> _launchURL() async {
    final url = BlocProvider.of<AuthCubit>(context).modelGeneralConfigModel.links?.plataformaSeguros;
    if (url != null && await canLaunchUrl(Uri.parse(url))) {
      await launchUrl(Uri.parse(url), mode: LaunchMode.externalApplication);
      Navigator.pop(context); // Volta para a home page
    } else {
      throw 'Unable to open url: $url';
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Seguros'),
      ),
      body: const Center(
        child: CircularProgressIndicator(),
      ),
    );
  }
  
}