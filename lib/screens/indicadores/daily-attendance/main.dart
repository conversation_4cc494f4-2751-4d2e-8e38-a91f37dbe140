import 'package:cooperado_minha_unimed/bloc/indicators/daily_attendance/daily_attendance_cubit.dart';
import 'package:cooperado_minha_unimed/colors.dart';
import 'package:cooperado_minha_unimed/screens/indicadores/daily-attendance/daily.dart';
import 'package:cooperado_minha_unimed/screens/indicadores/daily-attendance/details_screen.dart';
import 'package:cooperado_minha_unimed/shared/widgets/card_refresh.dart';
import 'package:cooperado_minha_unimed/shared/widgets/error-banner/error_banner.component.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';

class DailyAttendanceScreen extends StatefulWidget {
  const DailyAttendanceScreen({super.key});

  @override
  DailyAttendanceScreenState createState() => DailyAttendanceScreenState();
}

class DailyAttendanceScreenState extends State<DailyAttendanceScreen> {
  bool showDetails = false;
  @override
  void initState() {
    _init();
    super.initState();
  }

  void _init() {
    context.read<DailyAttendanceCubit>().listDailyAttendance();
  }

  @override
  Widget build(BuildContext context) {
    return CardRefresh(
      title: const Text("Serviços este mês",
          style: TextStyle(
            color: CooperadoColors.blackText,
            fontSize: 16,
            fontWeight: FontWeight.bold,
          )),
      refresh: _iconRefresh(),
      child: Material(
        color: Colors.white,
        child: BlocBuilder<DailyAttendanceCubit, DailyAttendanceState>(
          builder: (context, state) {
            if (state is LoadingDailyAttendanceState) {
              return const SpinKitCircle(
                color: CooperadoColors.tealGreen,
              );
            } else if (state is ErrorDailyAttendanceState) {
              return ErrorBanner(message: state.message);
            } else if (state is LoadedDailyAttendanceState) {
              return Column(
                children: <Widget>[
                  Hero(
                    tag: 'daily-attendance',
                    child: Daily(services: state.services),
                  ),
                  Row(
                    children: <Widget>[
                      Expanded(
                        child: Padding(
                            padding: const EdgeInsets.only(top: 8.0),
                            child: _buttonDetails(
                                context, state.list, state.services)),
                      ),
                    ],
                  )
                ],
              );
            } else {
              return Container();
            }
          },
        ),
      ),
    );
  }

  Widget _iconRefresh() {
    return BlocBuilder<DailyAttendanceCubit, DailyAttendanceState>(
        builder: (context, state) {
      if (state is ErrorDailyAttendanceState) {
        return InkWell(child: const Icon(Icons.refresh), onTap: () => _init());
      } else {
        return Container();
      }
    });
  }

  Widget _buttonDetails(context, list, services) {
    return Align(
        child: InkWell(
            onTap: () {
              Navigator.push(
                context,
                PageRouteBuilder(
                  transitionDuration: const Duration(seconds: 1),
                  pageBuilder: (_, __, ___) =>
                      DetailsScreen(list: list, services: services),
                ),
              );
            },
            child: Container(
                padding: const EdgeInsets.only(
                    top: 12.0, bottom: 12.0, left: 40, right: 40),
                decoration: const BoxDecoration(
                    color: CooperadoColors.tealGreen,
                    borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(20),
                        topRight: Radius.circular(20))),
                child: const Text(
                  "VER MAIS",
                  style: TextStyle(
                    //fontSize: 18,
                    color: Colors.white,
                  ),
                ))));
  }
}
