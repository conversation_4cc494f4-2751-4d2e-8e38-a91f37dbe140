import 'package:cooperado_minha_unimed/models/res-internal/image_result.model.dart';
import 'package:cooperado_minha_unimed/shared/api/graphql-res-internal.api.dart';
import 'package:cooperado_minha_unimed/shared/locator.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

part 'res_image_exam_result_cubit_state.dart';

class ResImageExamResultCubit extends Cubit<ResImageExamResultState> {
  ResImageExamResultCubit() : super(InitialResImageExamResultState());

  List<ResImageExamResultModel> _listImageExamResults =
      List.empty(growable: true);
  List<ResImageExamResultModel> get listImageExamResults =>
      _listImageExamResults;

  void listResImageExamResults({required String card}) async {
    try {
      emit(LoadingResImageExamResultState());

      _listImageExamResults = await Locator.instance!<GraphQlApiInternal>()
          .resInternalImageExamResultsByCard(card: card);

      emit(LoadedResImageExamResultState(
          listImageExamResults: _listImageExamResults));
    } catch (e) {
      emit(ErrorResImageExamResultState(message: e.toString()));
    }
  }

  void searchListImageExamResults({required String searchKey}) async {
    try {
      emit(LoadingResImageExamResultState());

      final filtredList = _listImageExamResults
          .where((element) => element
              .toJson()
              .toString()
              .toLowerCase()
              .contains(searchKey.toLowerCase()))
          .toList();

      emit(LoadedResImageExamResultState(listImageExamResults: filtredList));
    } catch (e) {
      emit(ErrorResImageExamResultState(message: e.toString()));
    }
  }
}
