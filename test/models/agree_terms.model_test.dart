import 'package:cooperado_minha_unimed/models/agree_terms.model.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  AgreeTermsModel? agreeTermsModel;
  Map? json;

  setUpAll(
    () {
      agreeTermsModel = AgreeTermsModel(
        code: 16,
        title: 'Termo de Isenção de Conflito de Interesses',
        subtitle:
            'Declaro que não me encontro em situação que possa ser caracterizada como conflito de interesses em relação às minhas funções e responsabilidades no exercício de atividades relacionadas à Unimed Fortaleza, onde reforço que não possuo:<br/><br/>\n\n<ul>\n<li>Cargo de servidor público e/ou parentesco de até 2° grau[*], união estável ou matrimonial com servidores públicos, em cargos de fiscalização ou chefia em órgãos da Administração Pública Municipal, Estadual ou Federal (Ex.: Prefeitura, Governo do Estado, ANS, Ministério Público, Judiciário etc.), no âmbito da atuação de negócios ou atividades da Unimed Fortaleza, e/ou de pessoas politicamente expostas (PPE)[**].</li><br/>\n\n<li>Vínculo empregatício, participação societária em Pessoa Jurídica, parentesco de até 2º grau[*], união estável ou vínculo matrimonial com empregados em cargos de chefia ou sócios de empresas que possuem contrato ativo com a Unimed Fortaleza.</li><br/>\n\n<li>Parentesco de até 2º grau [*], união estável ou vínculo matrimonial com empregados, membros da alta gestão e médicos cooperados na Unimed Fortaleza.</li><br/>\n</ul>\n\n<b>[*] 1º grau de parentesco</b>: mãe, pai e filhos. <b>2º grau de parentesco</b>: avô, avó, irmãos e netos<br/><br/>\n\n<b>[**] Pessoa Politicamente Exposta (PPE)</b>: São todas as pessoas que, nos últimos cinco anos, exercem ou exerceram, no Brasil ou no exterior, algum cargo, emprego ou função pública relevante ou se têm, nessas condições, familiares, representantes ou ainda pessoas de seu relacionamento próximo.<br/><br/>\n\nEm relação aos vínculos mencionados, comprometo-me a notificar, por meio do formulário disponível no link a seguir, todos os vínculos que possuo e que possam resultar em potenciais conflitos de interesse.<br/><br/>\n\nFormulário de Declaração de Vínculo: <a href="https://forms.gle/bGPBjXvhSJLXQ9zGA" target="_blank">https://forms.gle/bGPBjXvhSJLXQ9zGA</a>',
        pdf: ' ',
      );

      agreeTermsModel = AgreeTermsModel(
        code: 1,
        title: 'title',
        subtitle: 'subtitle',
        pdf:
            'https://www.unimedfortaleza.com.br/portaluploads/uploads/2022/10/cartilha_Codigo-de-Conduta-so-texto-1.pdf',
      );

      json = {
        "codigo": 1,
        "titulo": "title",
        "descricao": "subtitle",
        "parametro":
            "https://www.unimedfortaleza.com.br/portaluploads/uploads/2022/10/cartilha_Codigo-de-Conduta-so-texto-1.pdf"
      };
    },
  );

  group(
    "isInstanceOf AgreeTermsModel model tests",
    () {
      test("Should be return instance of AgreeTermsModel", () {
        expect(agreeTermsModel, isInstanceOf<AgreeTermsModel>());
      });
      test("Should be return instance of int", () {
        expect(agreeTermsModel!.code, isInstanceOf<int>());
      });

      test("Should be return instance of String", () {
        expect(agreeTermsModel!.pdf, isInstanceOf<String>());
      });
    },
  );

  group("Json test", () {
    test("Should be return instance of AgreeTermsModel to json", () {
      expect(agreeTermsModel!.toJson(), isInstanceOf<Map<dynamic, dynamic>>());
    });
    test("Should be return instance of AgreeTermsModel from json", () {
      expect(AgreeTermsModel.fromJson(json!), isInstanceOf<AgreeTermsModel>());
    });

    test("Type json", () {
      expect(json!["codigo"], isInstanceOf<int>());
      expect(json!["titulo"], isInstanceOf<String>());
      expect(json!["descricao"], isInstanceOf<String>());
      expect(json!["parametro"], isInstanceOf<String>());
    });

    test("Can´t return if is null", () {
      expect(json!["codigo"] == null, false);
      expect(json!["titulo"] == null, false);
      expect(json!["descricao"] == null, false);
      expect(json!["parametro"] == null, false);
    });
  });
  group(
    "Other tests",
    () {
      test("Should be return https:// true", () {
        expect(agreeTermsModel!.pdf.contains("https://"), true);
        expect(json!["parametro"].contains("https://"), true);
      });

      test("Should be return true PDF extension", () {
        expect(agreeTermsModel!.pdf.contains(".pdf"), true);
        expect(json!["parametro"].contains(".pdf"), true);
      });
    },
  );

  group('Other Test with HTML service ', () {
    test('deve ter o código correto', () {
      expect(agreeTermsModel!.code, isInstanceOf<int>());
    });
    test('deve ter o título correto', () {
      expect(agreeTermsModel!.title, isInstanceOf<String>());
    });
    test('deve conter a informação de servidor público', () {
      expect(agreeTermsModel!.subtitle, isInstanceOf<String>());
    });

    test('deve conter a informação de pessoa politicamente exposta', () {
      expect(agreeTermsModel!.subtitle, isInstanceOf<String>());
    });

    test('deve conter o link do formulário', () {
      expect(agreeTermsModel!.subtitle, isInstanceOf<String>());
    });
  });
}
