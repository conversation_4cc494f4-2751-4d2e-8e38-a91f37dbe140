import 'package:cooperado_minha_unimed/bloc/sensitive-data/sensitive_data_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/sensitive-data/sensitive_data_state.dart';
import 'package:cooperado_minha_unimed/bloc/transparency/contingency_index/contingency_index_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/transparency/loss_ratio/loss_ratio.cubit.dart';
import 'package:cooperado_minha_unimed/colors.dart';
import 'package:cooperado_minha_unimed/shared/widgets/error-banner/error_banner.component.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';

class CardIndexesUnimed extends StatefulWidget {
  const CardIndexesUnimed({super.key});

  @override
  CardIndexesUnimedState createState() => CardIndexesUnimedState();
}

class CardIndexesUnimedState extends State<CardIndexesUnimed> {
  @override
  void initState() {
    context.read<LossRatioCubit>().getLossRatio();
    context.read<ContingencyIndexCubit>().getContingencyIndex();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(8.0),
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(8.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: <Widget>[
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  const Text(
                    "Indicadores Unimed",
                    style: TextStyle(
                        color: CooperadoColors.blackText,
                        fontWeight: FontWeight.bold),
                  ),
                  _buidiIconRefresh()
                ],
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: <Widget>[
                  Expanded(child: _sinistralidade()),
                  Container(
                    width: 1,
                    color: CooperadoColors.grayLight,
                  ),
                  Expanded(child: _contingencia())
                ],
              )
            ],
          ),
        ),
      ),
    );
  }

  Widget _buidiIconRefresh() {
    return BlocBuilder<LossRatioCubit, LossRatioState>(
      builder: (context, state) {
        if (state is ErrorLossRatioState) {
          return _iconRefresh();
        } else {
          return BlocBuilder<ContingencyIndexCubit, ContingencyIndexState>(
            builder: (context, state) {
              if (state is ErrorContingencyIndexState) {
                return _iconRefresh();
              } else {
                return Container();
              }
            },
          );
        }
      },
    );
  }

  _iconRefresh() {
    return IconButton(
      icon: const Icon(Icons.refresh),
      onPressed: () {
        context.read<LossRatioCubit>().getLossRatio();
        context.read<ContingencyIndexCubit>().getContingencyIndex();
      },
    );
  }

  Widget _sinistralidade() {
  return BlocBuilder<LossRatioCubit, LossRatioState>(
    builder: (context, lossRatioState) {
      return BlocBuilder<SensitiveDataCubit, SensitiveDataState>(
        builder: (context, sensitiveState) {
          if (lossRatioState is LoadedLossRatioState) {
            return Container(
              padding: const EdgeInsets.all(8.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: <Widget>[
                  const SizedBox(height: 5),
                  const Text(
                    "Índice de \nSinistralidade",
                    textAlign: TextAlign.center,
                    style: TextStyle(
                        fontSize: 16, color: CooperadoColors.grayLight2),
                  ),
                  const SizedBox(height: 10),
                  Text(
                    sensitiveState.isSensitiveDataVisible
                        ? '${lossRatioState.lossRatio.last!.projectedValue}%'
                        : '*****',
                    style: const TextStyle(
                        color: CooperadoColors.limaColor, fontSize: 26),
                  ),
                  const SizedBox(height: 10),
                  const Text(
                    'SINISTRALIDADE \n PROJETADA',
                    textAlign: TextAlign.center,
                  ),
                  Text(
                      '${lossRatioState.lossRatio.last!.referenceMonthLabel} - ${lossRatioState.lossRatio.last!.referenceYear}')
                ],
              ),
            );
          } else if (lossRatioState is LoadingLossRatioState) {
            return const SpinKitCircle(
              color: CooperadoColors.tealGreen,
            );
          } else if (lossRatioState is ErrorLossRatioState) {
            return ErrorBanner(message: lossRatioState.message);
          } else {
            return Container();
          }
        },
      );
    },
  );
}

  Widget _contingencia() {
  return BlocBuilder<ContingencyIndexCubit, ContingencyIndexState>(
    builder: (context, contingencyState) {
      return BlocBuilder<SensitiveDataCubit, SensitiveDataState>(
        builder: (context, sensitiveState) {
          if (contingencyState is LoadedContingencyIndexState) {
            return Container(
              padding: const EdgeInsets.all(8.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: <Widget>[
                  const Text(
                    "Fundo de \n Contingência",
                    textAlign: TextAlign.center,
                    style: TextStyle(
                        fontSize: 16, color: CooperadoColors.grayLight2),
                  ),
                  const SizedBox(
                    height: 5,
                  ),
                  Text(
                    sensitiveState.isSensitiveDataVisible
                        ? '${contingencyState.contingencyIndex.last!.monthValue}%'
                        : '*****',
                    style: const TextStyle(
                        color: CooperadoColors.limaColor, fontSize: 26),
                  ),
                  const SizedBox(
                    height: 5,
                  ),
                  const Text(
                    'PERCENTUAL \n DE DESCONTO',
                    textAlign: TextAlign.center,
                  ),
                  Text(
                      '${contingencyState.contingencyIndex.last!.referenceMonthLabel} - ${contingencyState.contingencyIndex.last!.referenceYear}')
                ],
              ),
            );
          } else if (contingencyState is LoadingContingencyIndexState) {
            return const SpinKitCircle(
              color: CooperadoColors.tealGreen,
            );
          } else if (contingencyState is ErrorContingencyIndexState) {
            return ErrorBanner(message: contingencyState.message);
          } else {
            return Container();
          }
        },
      );
    },
  );
}
}
