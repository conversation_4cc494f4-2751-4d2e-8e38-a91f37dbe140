import 'package:another_flushbar/flushbar.dart';
import 'package:auto_size_text/auto_size_text.dart';
import 'package:cooperado_minha_unimed/bloc/auth/auth_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/office-notices/office_notices_cubit.dart';
import 'package:cooperado_minha_unimed/colors.dart';
import 'package:cooperado_minha_unimed/models/office_notices.model.dart';
import 'package:cooperado_minha_unimed/shared/widgets/error-banner/error_banner.component.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class OfficeNoticesScreen extends StatefulWidget {
  const OfficeNoticesScreen({super.key});

  @override
  State<OfficeNoticesScreen> createState() => _OfficeNoticesScreenState();
}

class _OfficeNoticesScreenState extends State<OfficeNoticesScreen> {
  bool _showHiddenNotifications = false;
  List<OfficeNoticeModel> _listItens = [];
  int _currentPage = 1;
  final int _pageSize = 10;
  final ScrollController _scrollController = ScrollController();
  bool listFull = false;

  int _countReadNotifications(List<OfficeNoticeModel> notifications) {
    return notifications
        .where((notification) => notification.dataLeitura != null)
        .length;
  }

  int _countUnreadNotifications(List<OfficeNoticeModel> notifications) {
    return notifications
        .where((notification) => notification.dataLeitura == null)
        .length;
  }

  @override
  void initState() {
    super.initState();
    // _scrollController.addListener(() {
    //   if (!listFull) {
    //     if (_scrollController.position.pixels ==
    //         _scrollController.position.maxScrollExtent) {
    //       _currentPage++;
    //       _updateData();
    //     }
    //   }
    // });
    _updateData();
  }

  void _updateData({bool isPullRefresh = false}) {
    if (isPullRefresh) {
      _currentPage = 1;
      _listItens = [];
      listFull = false;
    }
    context.read<OfficeNoticesCubit>().getAllOfficeNotices(
          codPrestador: context.read<AuthCubit>().credentials.crm,
          showHideNotifications: _showHiddenNotifications,
          page: _currentPage,
          pageSize: _pageSize,
        );
  }

  void _loadMore() {
    if (!listFull) {
      setState(() {
        _currentPage++;
      });
      _updateData();
    } 
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        backgroundColor: CooperadoColors.backgroundColor,
        appBar: AppBar(
          centerTitle: true,
          title: AutoSizeText(
            "Avisos do Consultório (${_listItens.length})",
            minFontSize: 10,
          ),
        ),
        body: _buildListWithLoadMoreButton());
  }

   Widget _buildListWithLoadMoreButton() {
    return Column(
      children: [
        Expanded(
          child: BlocConsumer<OfficeNoticesCubit, OfficeNoticesState>(
            listener: (context, state) {
              if (state is DoneReadNotificationState ||
                  state is DoneHideNotificationState) {
                _updateData(isPullRefresh: true);
              } else if (state is LoadedListOfficeNoticesState) {
                setState(() {
                  for (var notice in state.officeNotices) {
                    if (!_listItens.any(
                        (item) => item.codNotificacao == notice.codNotificacao)) {
                      _listItens.add(notice);
                    }
                  }
                  if (state.officeNotices.length < _pageSize) {
                    listFull = true;
                  }
                });
              } else if (state is EmptyOfficeNoticesState) {
                setState(() {
                  listFull = true;
                });
                Flushbar(
                  title: "AVISOS CONSULTÓRIO ONLINE",
                  message: "Não foi encontrado itens a serem carregados.",
                  duration: const Duration(seconds: 5),
                  boxShadows: [
                    BoxShadow(
                        color: Colors.blue[800]!,
                        offset: const Offset(0.0, 2.0),
                        blurRadius: 3.0)
                  ],
                  backgroundGradient: const LinearGradient(
                      colors: [Colors.blueGrey, CooperadoColors.tealGreen]),
                  backgroundColor: CooperadoColors.greenDark,
                ).show(context);
              }
            },
            builder: (context, state) {
              if (state is LoadingOfficeNoticesState) {
                return const Center(
                  child: CircularProgressIndicator(),
                );
              } else if (state is ErrorOfficeNoticesState) {
                return _errorOfficeNotices(state.message);
              }
              return RefreshIndicator(
                onRefresh: () async => _updateData(isPullRefresh: true),
                child: Column(
                  children: [
                    const SizedBox(height: 10),
                    _buildCaption(),
                    _readUnread(),
                    Expanded(
                      child: ListView.builder(
                        controller: _scrollController,
                        itemCount: _listItens.length + 1,
                        itemBuilder: (context, index) {
                          if (index == _listItens.length) {
                            if (_listItens.isEmpty || listFull) {
                              return const SizedBox.shrink();
                            }
                            return Padding(
                              padding: const EdgeInsets.all(16.0),
                              child: ElevatedButton(
                                onPressed: _loadMore,
                                child: const Text('Carregar mais'),
                              ),
                            );
                          }
                          final notice = _listItens[index];
                          return _buildNoticeItem(officeNotice: notice);
                        },
                      ),
                    ),
                    const SizedBox(height: 10),
                  ],
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _readUnread() {
    final int readCount = _countReadNotifications(_listItens);
    final int unreadCount = _countUnreadNotifications(_listItens);

    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        const AutoSizeText(
          'Avisos Lidos: ',
          style: TextStyle(
            fontSize: 15,
          ),
        ),
        Text(
          '$readCount',
          style: const TextStyle(
            fontSize: 15,
            color: Colors.black,
          ),
        ),
        const SizedBox(width: 20),
        const AutoSizeText(
          'Avisos Não Lidos: ',
          style: TextStyle(
            fontSize: 15,
          ),
        ),
        Text(
          '$unreadCount',
          style: const TextStyle(
            fontSize: 15,
            color: Colors.black,
          ),
        ),
      ],
    );
  }

  Widget _buildNoticeItem({required OfficeNoticeModel officeNotice}) {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      child: ClipPath(
        clipper: ShapeBorderClipper(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        ),
        child: Container(
          decoration: BoxDecoration(
            color: officeNotice.dataLeitura == null
                ? CooperadoColors.grayLight3
                : Colors.white,
            border: Border(
              right: BorderSide(
                color: officeNotice.codTipoNotificacao == 1
                    ? CooperadoColors.darkRed
                    : CooperadoColors.orange2,
                width: 10,
              ),
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.grey.withOpacity(0.5),
                spreadRadius: 1,
                blurRadius: 2,
                offset: const Offset(0, 1),
              ),
            ],
          ),
          child: Stack(
            children: [
              Padding(
                padding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildTextItem(
                      title: officeNotice.codTipoNotificacao == 1
                          ? 'Pendência'
                          : 'Aviso',
                      value: officeNotice.notificacao,
                    ),
                    _buildTextItem(
                      title: 'Beneficiário',
                      value: officeNotice.nomeBeneficiario,
                    ),
                    const Divider(thickness: 1),
                    const SizedBox(height: 10),
                    Row(
                      children: [
                        Expanded(
                          child: Row(
                            children: [
                              const Icon(Icons.calendar_today,
                                  color: CooperadoColors.grayDark),
                              const SizedBox(width: 5),
                              AutoSizeText(officeNotice.dataNotificacao ?? ''),
                            ],
                          ),
                        ),
                        if (officeNotice.dataOcultacao == null)
                          InkWell(
                            onTap: () =>
                                _showOptionsModal(officeNotice: officeNotice),
                            child: const Row(
                              children: [
                                AutoSizeText(
                                  'Opções',
                                  style: TextStyle(
                                    color: CooperadoColors.green,
                                    fontSize: 14,
                                    decoration: TextDecoration.underline,
                                  ),
                                ),
                                SizedBox(width: 2),
                                Icon(Icons.keyboard_arrow_right_rounded,
                                    color: CooperadoColors.green),
                              ],
                            ),
                          ),
                      ],
                    ),
                    const SizedBox(height: 5),
                  ],
                ),
              ),
              if (officeNotice.dataOcultacao != null)
                const Positioned(
                  right: 10,
                  top: 10,
                  child: Icon(Icons.hide_source_rounded,
                      color: CooperadoColors.grayDark),
                ),
            ],
          ),
        ),
      ),
    );
  }

  void _showOptionsModal({required OfficeNoticeModel officeNotice}) {
    showModalBottomSheet<void>(
      context: context,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(15)),
      builder: (BuildContext context) {
        return Padding(
          padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Align(
                alignment: Alignment.topRight,
                child: IconButton(
                  color: CooperadoColors.tealGreen,
                  icon: const Icon(Icons.close),
                  onPressed: () => Navigator.pop(context),
                ),
              ),
              if (officeNotice.dataLeitura == null)
                _optionTile(
                  icon: Icons.remove_red_eye,
                  label: 'Marcar como lido',
                  onPressed: () {
                    context.read<OfficeNoticesCubit>().readNotification(
                          codPrestador:
                              context.read<AuthCubit>().credentials.crm,
                          codNotificacao: officeNotice.codNotificacao ?? 0,
                        );
                    Navigator.pop(context); // Feche o modal
                  },
                ),
              _optionTile(
                icon: Icons.hide_source_outlined,
                label: 'Marcar como oculto',
                onPressed: () {
                  context.read<OfficeNoticesCubit>().hideNotification(
                        codPrestador: context.read<AuthCubit>().credentials.crm,
                        codNotificacao: officeNotice.codNotificacao ?? 0,
                      );
                  Navigator.pop(context); // Feche o modal
                },
              ),
              const SizedBox(height: 30),
            ],
          ),
        );
      },
    );
  }

  Widget _buildTextItem({required String title, required String? value}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          AutoSizeText(
            title,
            style: const TextStyle(
              fontSize: 14,
            ),
          ),
          AutoSizeText(
            value ?? 'Não informado',
            style: const TextStyle(
              fontSize: 16,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCaption() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        Row(
          children: [
            const AutoSizeText(
              'PENDÊNCIA',
              style: TextStyle(
                fontSize: 12,
              ),
            ),
            const SizedBox(width: 5),
            Container(
              height: 15,
              width: 15,
              decoration: BoxDecoration(
                color: CooperadoColors.darkRed,
                borderRadius: BorderRadius.circular(5),
              ),
            ),
          ],
        ),
        Row(
          children: [
            const AutoSizeText(
              'AVISO',
              style: TextStyle(
                fontSize: 12,
              ),
            ),
            const SizedBox(width: 5),
            Container(
              height: 15,
              width: 15,
              decoration: BoxDecoration(
                color: CooperadoColors.orange2,
                borderRadius: BorderRadius.circular(5),
              ),
            ),
          ],
        ),
        Row(
          children: [
            const AutoSizeText(
              'Exibir ocultos?',
              style: TextStyle(
                fontSize: 12,
              ),
            ),
            Switch(
              value: _showHiddenNotifications,
              onChanged: (value) {
                setState(() {
                  _showHiddenNotifications = value;
                });
                _updateData(isPullRefresh: true);
              },
            ),
          ],
        ),
      ],
    );
  }

  Widget _errorOfficeNotices(String? error) {
    return RefreshIndicator(
      onRefresh: () async => _updateData(isPullRefresh: true),
      child: SingleChildScrollView(
        padding: const EdgeInsets.only(top: 8.0),
        physics: const ClampingScrollPhysics(
            parent: AlwaysScrollableScrollPhysics()),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Center(
              child: ErrorBanner(
                message: error ?? "Nenhum dado encontrado.",
                backgroundColor: Colors.transparent,
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _optionTile({
    required IconData icon,
    required String label,
    required VoidCallback onPressed,
    double iconSize = 24,
  }) {
    return InkWell(
      onTap: onPressed,
      child: Column(
        children: [
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 8.0),
            child: Row(
              children: [
                Icon(icon, color: CooperadoColors.tealGreen, size: iconSize),
                const SizedBox(width: 16.0),
                Expanded(
                  child: Text(
                    label,
                    style: const TextStyle(color: CooperadoColors.tealGreen),
                  ),
                ),
              ],
            ),
          ),
          const Divider(color: Colors.grey),
        ],
      ),
    );
  }
}
