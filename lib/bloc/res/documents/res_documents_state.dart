part of 'res_documents_cubit.dart';

abstract class ResDocumentsState extends Equatable {
  const ResDocumentsState();

  @override
  List<Object> get props => [];
}

class InitialResDocumentsState extends ResDocumentsState {}

class LoadingResDocumentsState extends ResDocumentsState {
  @override
  List<Object> get props => [];
}

class NoDataResDocumentsState extends ResDocumentsState {
  const NoDataResDocumentsState();
}

class ErrorResDocumentsState extends ResDocumentsState {
  final String message;

  @override
  List<Object> get props => [message];

  const ErrorResDocumentsState({required this.message});
}

class LoadedResDocumentsState extends ResDocumentsState {
  final List<ResAttendanceModel> listResDocuments;

  @override
  List<Object> get props => [listResDocuments];

  const LoadedResDocumentsState({required this.listResDocuments});
}

class LoadingResAllergiesSearchState extends ResDocumentsState {
  @override
  List<Object> get props => [];
}

class ErrorResAllergiesSearchState extends ResDocumentsState {
  final String message;

  @override
  List<Object> get props => [message];

  const ErrorResAllergiesSearchState({required this.message});
}
