import 'package:intl/intl.dart';

class ResLabExamModel {
  String? doctorEmail;
  late String doctorId;
  late String doctorName;
  late String orderDate;
  late String orderId;
  late String orderLabel;
  late String orderStatus;
  late String patientName;
  String? resultPdfUrl;
  late String resultScheduledDate;
  late String unit;
  late String unitId;
  late String unitName;

  ResLabExamModel(
      {this.doctorEmail,
      required this.doctorId,
      required this.doctorName,
      required this.orderDate,
      required this.orderId,
      required this.orderLabel,
      required this.orderStatus,
      required this.patientName,
      this.resultPdfUrl,
      required this.resultScheduledDate,
      required this.unit,
      required this.unitId,
      required this.unitName});

  String get dateOrderFormatted {
    return DateFormat('dd/MM/yyyy HH:mm').format(DateTime.parse(orderDate));
  }

  ResLabExamModel.fromJson(Map<String, dynamic> json) {
    doctorEmail = json['doctorEmail'];
    doctorId = json['doctorId'];
    doctorName = json['doctorName'];
    orderDate = json['orderDate'];
    orderId = json['orderId'];
    orderLabel = json['orderLabel'];
    orderStatus = json['orderStatus'];
    patientName = json['patientName'];
    resultPdfUrl = json['resultPdfUrl'];
    resultScheduledDate = json['resultScheduledDate'];
    unit = json['unit'];
    unitId = json['unitId'];
    unitName = json['unitName'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['doctorEmail'] = doctorEmail;
    data['doctorId'] = doctorId;
    data['doctorName'] = doctorName;
    data['orderDate'] = orderDate;
    data['orderId'] = orderId;
    data['orderLabel'] = orderLabel;
    data['orderStatus'] = orderStatus;
    data['patientName'] = patientName;
    data['resultPdfUrl'] = resultPdfUrl;
    data['resultScheduledDate'] = resultScheduledDate;
    data['unit'] = unit;
    data['unitId'] = unitId;
    data['unitName'] = unitName;
    return data;
  }
}
