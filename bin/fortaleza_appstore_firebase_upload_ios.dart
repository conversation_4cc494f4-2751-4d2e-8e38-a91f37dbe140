import 'dart:convert';
import 'dart:io';

void _uploadFirebase() {
  final _parameters = [
    'appdistribution:distribute',
    'build/ios/iphoneos/Payload.ipa',
    '--app',
    '1:546581952282:ios:5047f1d42ae95362269ea3',
    '--release-notes',
    'Autodeploy Fortaleza Appstore iOS - HMG',
    '--groups',
    'testers'
    // '--testers',
    // '<EMAIL>'
  ];
  Process.start('firebase', _parameters).then((Process process) {
    process.exitCode.then((exitCode) {
      print('exit code: $exitCode');
    });

    process.stderr.transform(utf8.decoder).listen((data) {
      print(data);
    });

    process.stdout.transform(utf8.decoder).listen((data) {
      print(data);
    });
  }).catchError((onError) {
    print('onError $onError');
  });
}

void main(List<String> args) {
  print('Build iOS app...');

  Process.start('flutter', [
    'build',
    'ios',
    '--flavor',
    'fortaleza-appstore',
    '--dart-define=CLIENT_ID=UNIMED_FORTALEZA',
    '--release',
    '--verbose',
    ...args,
  ]).then((Process processBuild) {
    processBuild.exitCode.then((exitCode) {
      print('build success');

      Process.start('./generate_ipa.sh', []).then((zip) {
        zip.stdout.transform(utf8.decoder).listen((data) => print(data));
        zip.stderr.transform(utf8.decoder).listen((data) => print(data));

        zip.exitCode.then((exitCode) {
          print('exit code: $exitCode');

          _uploadFirebase();
        });
      }).catchError((onError) {
        print('error generate: $onError');
      });
    });
    processBuild.stdout.transform(utf8.decoder).listen((data) {
      print('build: $data');
    });

    processBuild.stderr.transform(utf8.decoder).listen((data) {
      print('build error: $data');
    });
  }).catchError((onError) {
    print('build onError $onError');
  });
}
