import 'package:bloc/bloc.dart';
import 'package:cooperado_minha_unimed/models/fiscal_council.model.dart';
import 'package:cooperado_minha_unimed/shared/api/fiscal_council.api.dart';
import 'package:cooperado_minha_unimed/shared/locator.dart';
import 'package:equatable/equatable.dart';

part 'form_council_state.dart';

class FormCouncilCubit extends Cubit<FormCouncilState> {
  FormCouncilCubit() : super(FormCouncilInitial());

  getTopicEvent() async {
    emit(LoadingGetTopic());
    try {
      final retorno = await Locator.instance!<FiscalCouncilApi>().getTopics();

      emit(DoneGetTopic(list: retorno));
    } catch (ex) {
      emit(ErrorGetTopic(message: '$ex'));
    }
  }
}
