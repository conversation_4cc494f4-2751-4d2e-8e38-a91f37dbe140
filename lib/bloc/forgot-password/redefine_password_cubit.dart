import 'package:bloc/bloc.dart';
import 'package:cooperado_minha_unimed/models/redefine_password.model.dart';
import 'package:cooperado_minha_unimed/shared/api/auth.api.dart';
import 'package:cooperado_minha_unimed/shared/locator.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';

part 'redefine_password_state.dart';

class RedefinePasswordCubit extends Cubit<RedefinePasswordState> {
  RedefinePasswordCubit() : super(RedefinePasswordInitial());

  Future<void> redefinePassword({
    required RedefinePasswordModel redefinePasswordModel,
  }) async {
    try {
      emit(LoadingRedefinePassworddState());

      final response = await Locator.instance!<AuthApi>().redefinePassword(
        redefinePasswordModel: redefinePasswordModel,
      );
      debugPrint(response);
      if (response.isNotEmpty) {
        emit(SucessRedefinePasswordState(response));
      } else {
        emit(
          const ErrorRedefinePassworddState(
              "Não foi possível acessar esse serviço, tente novamente mais tarde."),
        );
      }
    } catch (e) {
      emit(ErrorRedefinePassworddState(e.toString()));
    }
  }
}
