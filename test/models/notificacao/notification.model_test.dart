import 'package:cooperado_minha_unimed/models/notificacao/notification.model.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:intl/intl.dart';

void main() {
  group('NotificationModel', () {
    test('fromJson deve criar objeto corretamente', () {
      final json = {
        'notificationId': '123',
        'title': 'Teste',
        'description': 'Descrição de teste',
        'readAt': '2023-03-21T10:00:00Z',
        'pinned': true,
        'createdAt': '2023-03-21T15:30:00Z'
      };

      final model = NotificationModel.fromJson(json);

      expect(model.notificationId, equals('123'));
      expect(model.title, equals('Teste'));
      expect(model.description, equals('Descrição de teste'));
      expect(model.readAt, equals('2023-03-21T10:00:00Z'));
      expect(model.pinned, equals(true));
      expect(model.createdAt, equals('2023-03-21T15:30:00Z'));
    });

    test('toJson deve retornar o mapa correto', () {
      final model = NotificationModel(
        notificationId: '123',
        title: 'Teste',
        description: 'Descrição de teste',
        readAt: '2023-03-21T10:00:00Z',
        pinned: false,
        createdAt: '2023-03-21T15:30:00Z',
      );

      final json = model.toJson();
      expect(
          json,
          equals({
            'notificationId': '123',
            'title': 'Teste',
            'description': 'Descrição de teste',
            'readAt': '2023-03-21T10:00:00Z',
            'pinned': false,
            'createdAt': '2023-03-21T15:30:00Z',
          }));
    });

    test(
        'formattedCreatedAtDate deve retornar data formatada quando createdAt não for nulo',
        () {
      const createdAt = '2023-03-21T15:30:00Z';
      final model = NotificationModel(createdAt: createdAt);

      // Calcula o valor esperado considerando a conversão para horário local.
      final dateTime = DateTime.parse(createdAt).toLocal();
      final expectedFormattedDate =
          DateFormat("dd/MM/yyyy HH:mm").format(dateTime);
      expect(model.formattedCreatedAtDate, equals(expectedFormattedDate));
    });

    test(
        'formattedCreatedAtDate deve retornar "Não informado" quando createdAt for nulo',
        () {
      final model = NotificationModel(createdAt: null);
      expect(model.formattedCreatedAtDate, equals('Não informado'));
    });
  });

  group('NotificationResponse', () {
    test('fromJson deve criar objeto com notifications corretamente', () {
      final json = {
        'totalPages': 3,
        'notifications': [
          {
            'notificationId': '123',
            'title': 'Teste',
            'description': 'Descrição de teste',
            'readAt': '2023-03-21T10:00:00Z',
            'pinned': true,
            'createdAt': '2023-03-21T15:30:00Z',
          },
          {
            'notificationId': '456',
            'title': 'Teste 2',
            'description': 'Descrição de teste 2',
            'readAt': null,
            'pinned': false,
            'createdAt': null,
          }
        ]
      };

      final response = NotificationResponse.fromJson(json);
      expect(response.totalPages, equals(3));
      expect(response.notifications, isNotNull);
      expect(response.notifications!.length, equals(2));

      // Verifica a primeira notificação
      final first = response.notifications!.first;
      expect(first.notificationId, equals('123'));
      expect(first.title, equals('Teste'));
      expect(first.description, equals('Descrição de teste'));
      expect(first.readAt, equals('2023-03-21T10:00:00Z'));
      expect(first.pinned, equals(true));
      expect(first.createdAt, equals('2023-03-21T15:30:00Z'));

      // Verifica a segunda notificação
      final second = response.notifications![1];
      expect(second.notificationId, equals('456'));
      expect(second.title, equals('Teste 2'));
      expect(second.description, equals('Descrição de teste 2'));
      expect(second.readAt, isNull);
      expect(second.pinned, equals(false));
      expect(second.createdAt, isNull);
    });

    test('fromJson deve criar objeto com notifications nulo corretamente', () {
      final json = {
        'totalPages': 2,
        'notifications': null,
      };

      final response = NotificationResponse.fromJson(json);
      expect(response.totalPages, equals(2));
      expect(response.notifications, isNull);
    });

    test('toJson deve retornar o mapa correto com notifications', () {
      final notification1 = NotificationModel(
        notificationId: '123',
        title: 'Teste',
        description: 'Descrição de teste',
        readAt: '2023-03-21T10:00:00Z',
        pinned: true,
        createdAt: '2023-03-21T15:30:00Z',
      );

      final notification2 = NotificationModel(
        notificationId: '456',
        title: 'Teste 2',
        description: 'Descrição de teste 2',
        readAt: null,
        pinned: false,
        createdAt: null,
      );

      final response = NotificationResponse(
        totalPages: 5,
        notifications: [notification1, notification2],
      );

      final json = response.toJson();
      expect(
          json,
          equals({
            'totalPages': 5,
            'notifications': [
              {
                'notificationId': '123',
                'title': 'Teste',
                'description': 'Descrição de teste',
                'readAt': '2023-03-21T10:00:00Z',
                'pinned': true,
                'createdAt': '2023-03-21T15:30:00Z',
              },
              {
                'notificationId': '456',
                'title': 'Teste 2',
                'description': 'Descrição de teste 2',
                'readAt': null,
                'pinned': false,
                'createdAt': null,
              }
            ],
          }));
    });

    test('toJson deve retornar o mapa correto quando notifications for nulo',
        () {
      final response = NotificationResponse(
        totalPages: 1,
        notifications: null,
      );

      final json = response.toJson();
      expect(
          json,
          equals({
            'totalPages': 1,
            'notifications': null,
          }));
    });
  });
}
