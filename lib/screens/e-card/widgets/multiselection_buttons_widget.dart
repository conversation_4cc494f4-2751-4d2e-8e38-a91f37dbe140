import 'package:cooperado_minha_unimed/colors.dart';
import 'package:flutter/material.dart';

class MultiSelecionButton extends StatelessWidget {
  final String? title;
  final List<String> defaultValues;
  final List<String> selectedValues;
  final Function(String, bool) onSelectionChanged;
  final bool isMultiSelection;
  final bool isPeriodInDays;

  const MultiSelecionButton({super.key, 
    required this.defaultValues,
    required this.selectedValues,
    required this.onSelectionChanged,
    this.isMultiSelection = true,
    this.isPeriodInDays = false,
    this.title,
  });

  @override
  Widget build(BuildContext context) {
    final bool allSelected = selectedValues.length == defaultValues.length;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: <Widget>[
        Visibility(
          visible: title != null,
          child: Text(
            title ?? '',
            style: const TextStyle(fontSize: 18),
          ),
        ),
        const SizedBox(height: 16),
        Wrap(
          spacing: 10.0,
          children: [
            if (isMultiSelection)
              ChoiceChip(
                label: const Text('Todos'),
                selected: allSelected,
                onSelected: (selected) {
                  if (selected) {
                    for (var local in defaultValues) {
                      if (!selectedValues.contains(local)) {
                        onSelectionChanged(local, true);
                      }
                    }
                  } else {
                    for (var local in defaultValues) {
                      if (selectedValues.contains(local)) {
                        onSelectionChanged(local, false);
                      }
                    }
                  }
                },
                selectedColor: CooperadoColors.greenDark,
                backgroundColor: Colors.white,
                shape: StadiumBorder(
                  side: BorderSide(
                    color: allSelected ? Colors.transparent : CooperadoColors.grayDark,
                  ),
                ),
                labelStyle: TextStyle(
                  color: allSelected ? Colors.white : CooperadoColors.grayDark,
                ),
                padding: const EdgeInsets.symmetric(vertical: 10.0, horizontal: 5.0),
              ),
            ...defaultValues.map((local) {
              final isSelected = selectedValues.contains(local);
              final label = isPeriodInDays ? '$local dias' : local;
              return ChoiceChip(
                label: Text(label),
                selected: isSelected,
                onSelected: (selected) {
                  if (isMultiSelection) {
                    onSelectionChanged(local, selected);
                  } else {
                    if (selected) {
                      // Cria uma cópia da lista para evitar ConcurrentModificationError
                      List<String> selectedValuesCopy = List.from(selectedValues);
                      for (var value in selectedValuesCopy) {
                        onSelectionChanged(value, false);
                      }
                      onSelectionChanged(local, true);
                    } else {
                      onSelectionChanged(local, false);
                    }
                  }
                },
                selectedColor: CooperadoColors.greenDark,
                backgroundColor: Colors.white,
                shape: StadiumBorder(
                  side: BorderSide(
                    color: isSelected ? Colors.transparent : CooperadoColors.grayDark,
                  ),
                ),
                labelStyle: TextStyle(
                  color: isSelected ? Colors.white : CooperadoColors.grayDark,
                ),
                padding: const EdgeInsets.symmetric(vertical: 10.0, horizontal: 5.0),
              );
            }),
          ],
        ),
      ],
    );
  }
}