import 'package:cooperado_minha_unimed/models/address.model.dart';
import 'package:cooperado_minha_unimed/models/zipcode.model.dart';
import 'package:cooperado_minha_unimed/shared/api/config_profile.api.dart';
import 'package:cooperado_minha_unimed/shared/locator.dart';
import 'package:cooperado_minha_unimed/shared/vo/profile/profile-payload.vo.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

part 'config_profile_state.dart';

class ConfigProfileCubit extends Cubit<ConfigProfileState> {
  ConfigProfileCubit() : super(InitialConfigProfileState());

  int? _page;
  int? get page => _page;

  PayloadAddress? _payloadAddress;
  PayloadAddress? get payloadAddress => _payloadAddress;

  List<AddressModel>? _listAddresses;
  List<AddressModel>? get listAddresses => _listAddresses;

  Future<void> changeEmail(String email, String password) async {
    // try {
    //   emit(LoadingState());
    //   await Locator.instance<ConfigProfileApi>().changeEmail(email, password);
    //   emit(ChangedEmailState());
    // } catch (e) {
    //   emit(ErrorConfigProfileState(e.message));
    // }
  }

  Future<void> getAddresses() async {
    try {
      emit(LoadingProfileAddressState());
      _listAddresses =
          await Locator.instance!<ConfigProfileApi>().getProfileAddresses();
      emit(LoadedProfileAddressesState(_listAddresses));
    } catch (e) {
      emit(ErrorConfigProfileState(e.toString()));
    }
  }

  Future<void> searchAddressesByZipcode(String zipcode) async {
    try {
      emit(LoadingAddressesByZipcodeState());
      final zipCodeResult =
          await Locator.instance!<ConfigProfileApi>().searchAddresses(zipcode);
      emit(LoadedZipcodeAddressesState(zipCodeResult));
    } catch (e) {
      emit(ErrorLoadAddressByZipcodeState(e.toString()));
    }
  }

  Future<void> renewPassword(
      String password, String newPass, String confirmPass) async {
    try {
      emit(const ChangingPasswordState());
      await Locator.instance!<ConfigProfileApi>()
          .renewPassword(password, newPass, confirmPass);
      emit(ChangedPasswordState('Senha modificada com sucesso!', newPass));
    } catch (e) {
      emit(ErrorChangePasswordState(e.toString()));
    }
  }

  setInitial() {
    emit(InitialConfigProfileState());
  }

  Future<void> getAllAddresses(String? crm) async {
    try {
      emit(LoadingAllAdressesState());
      _payloadAddress =
          await Locator.instance!<ConfigProfileApi>().getAllAddresses(crm);
      emit(LoadedAllProfileAddressesState(_payloadAddress));
    } catch (e) {
      emit(ErrorConfigProfileState(e.toString()));
    }
  }

  Future<void> updateEndereco(
      {required String crm, Enderecos? endereco}) async {
    try {
      emit(UpdatingProfileState());

      final PayloadAddress payloadAddressTemp =
          PayloadAddress.fromJson(_payloadAddress!.toJson());

      // replacement
      final idx = payloadAddressTemp.enderecos!.indexWhere(
          (element) => element!.codEndereco == endereco!.codEndereco);
      if (idx >= 0) payloadAddressTemp.enderecos![idx] = endereco;

      _payloadAddress = await Locator.instance!<ConfigProfileApi>()
          .updateProfile(crm, payloadAddressTemp);
      emit(UpdatedProfileState(_payloadAddress));
    } catch (e) {
      emit(ErrorUpdateProfileState(e.toString()));
    }
  }

  Future<void> updateContato(
      {required String crm, Enderecos? endereco, Contatos? newContato}) async {
    try {
      emit(UpdatingProfileState());

      final PayloadAddress payloadAddressTemp =
          PayloadAddress.fromJson(_payloadAddress!.toJson());

      // replacement
      final idxAddress = payloadAddressTemp.enderecos!.indexWhere(
          (element) => element!.codEndereco == endereco!.codEndereco);
      if (idxAddress >= 0) {
        final idxContato = payloadAddressTemp.enderecos![idxAddress]!.contatos!
            .indexWhere((contato) => contato!.id == newContato!.id);
        if (idxContato >= 0) {
          payloadAddressTemp.enderecos![idxAddress]!.contatos![idxContato] =
              newContato;
        }
      }
      _payloadAddress = await Locator.instance!<ConfigProfileApi>()
          .updateProfile(crm, payloadAddressTemp);

      emit(UpdatedProfileState(_payloadAddress));
    } catch (e) {
      emit(ErrorUpdateProfileState(e.toString()));
    }
  }
}
