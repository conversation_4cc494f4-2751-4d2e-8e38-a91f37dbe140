import 'package:cooperado_minha_unimed/colors.dart';
import 'package:cooperado_minha_unimed/models/res/res_document_detail_model.dart';
import 'package:cooperado_minha_unimed/screens/res/widget/info_row.dart';
import 'package:cooperado_minha_unimed/screens/res/widget/title_result.dart';
import 'package:flutter/material.dart';

class ResDocumentDetails extends StatelessWidget {
  final List<ResDocumentDetailModel> resDocumentsDetailModel;

  const ResDocumentDetails({super.key, required this.resDocumentsDetailModel});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Center(
            child: Container(
              height: 4,
              width: 100,
              margin: const EdgeInsets.symmetric(
                vertical: 10,
              ),
              decoration: BoxDecoration(
                color: CooperadoColors.grayLight3,
                borderRadius: BorderRadius.circular(2),
              ),
            ),
          ),
          Padding(
              padding: const EdgeInsets.only(top: 27),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  const Text(
                    'Documentos',
                    style: TextStyle(
                      fontSize: 15,
                      fontWeight: FontWeight.bold,
                      color: CooperadoColors.blackText,
                    ),
                  ),
                  TitleResult(
                    title: resDocumentsDetailModel.length > 1 ? 'Resultados ' : 'Resultado ',
                    quantity: resDocumentsDetailModel.length.toString().padLeft(2, '0'),
                  ),
                ],
              )),
          const SizedBox(height: 24),
          Expanded(
            child: Scrollbar(
              trackVisibility: true,
              thumbVisibility: true,
              child: SingleChildScrollView(
                child: Column(
                  children: [
                    ...resDocumentsDetailModel.map(
                      (detail) => Padding(
                        padding: const EdgeInsets.all(12.0),
                        child: Container(
                          decoration: BoxDecoration(
                            border: Border.all(color: CooperadoColors.grayLight3),
                            borderRadius: BorderRadius.circular(10),
                          ),
                          child: Padding(
                            padding: const EdgeInsets.all(12.0),
                            child: Column(
                              children: [
                                InfoRow(label: 'Solicitante', value: detail.nomeMedico),
                                const SizedBox(height: 16),
                                InfoRow(label: 'Conteúdo', value: detail.conteudo),
                                const SizedBox(height: 16),
                                InfoRow(label: 'Data', value: detail.dataFormatted),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
          const SizedBox(height: 24),
        ],
      ),
    );
  }
}
