import 'package:cooperado_minha_unimed/bloc/sensitive-data/sensitive_data_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/sensitive-data/sensitive_data_state.dart';
import 'package:cooperado_minha_unimed/colors.dart';
import 'package:cooperado_minha_unimed/shared/utils/string_utils.dart';
import 'package:cooperado_minha_unimed/shared/widgets/graphics/indicator.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class GenderDonutChart extends StatefulWidget {
  final bool animate;
  final dynamic custos;

  const GenderDonutChart({
    super.key,
    required this.animate,
    this.custos,
  });

  @override
  GenderDonutChartState createState() => GenderDonutChartState();
}

class GenderDonutChartState extends State<GenderDonutChart> {
  double total = 0.0;
  int touchedIndex = -1;
  List<PieChartSectionData> pieDataCustos = [];

  @override
  void initState() {
    pieDataCustos.clear();
    pieDataCustos = _createData();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        children: [
        Expanded(
  child: BlocBuilder<SensitiveDataCubit, SensitiveDataState>(
    builder: (context, sensitiveState) {
      bool isSensitiveDataVisible = !sensitiveState.isSensitiveDataVisible;
      return AspectRatio(
        aspectRatio: 1,
        child: PieChart(
          PieChartData(
            pieTouchData: PieTouchData(),
            borderData: FlBorderData(
              show: false,
            ),
            sectionsSpace: 2,
            centerSpaceRadius: 50,
            startDegreeOffset: 270,
            sections: isSensitiveDataVisible
                ? [
                    PieChartSectionData(
                      color: Colors.grey,
                      value: 100,
                      showTitle: false,
                    ),
                  ]
                : showingSections(),
          ),
        ),
      );
    },
  ),
),
         BlocBuilder<SensitiveDataCubit, SensitiveDataState>(
  builder: (context, sensitiveState) {
    bool isSensitiveDataVisible = !sensitiveState.isSensitiveDataVisible;

    String displayValue1 = isSensitiveDataVisible
        ? '*****'
        : StringUtils.formatThousands(pieDataCustos[0].value.toInt());

    String displayValue2 = isSensitiveDataVisible
        ? '*****'
        : StringUtils.formatThousands(pieDataCustos[1].value.toInt());

    return Column(
      children: [
        Indicator(
          color: CooperadoColors.chartColors[0],
          text: "${widget.custos[0].descricao} ($displayValue1)",
          isSquare: false,
        ),
        const SizedBox(
          height: 6,
        ),
        Indicator(
          color: CooperadoColors.chartColors[1],
          text: "${widget.custos[1].descricao} ($displayValue2)",
          isSquare: false,
        ),
      ],
    );
  },
),
          const SizedBox(
            height: 6,
          ),
        ],
      ),
    );
  }

  _createData() {
    List<PieChartSectionData> data = [];
    const double fontSize = 16.0;
    const double radius = 50.0;
    const shadows = [Shadow(color: Colors.black, blurRadius: 2)];

    for (int i = 0; i < widget.custos!.length; i++) {
      data.add(PieChartSectionData(
        color: _getColor(CooperadoColors.chartColors[i]),
        value: double.parse(widget.custos![i].quantidade.toString()),
        title: "",
        radius: radius,
        titleStyle: const TextStyle(
          fontSize: fontSize,
          fontWeight: FontWeight.bold,
          color: Colors.white,
          shadows: shadows,
        ),
      ));
    }
    return data;
  }

  Color _getColor(Color color) {
    return Color.fromARGB(color.alpha, color.red, color.green, color.blue);
  }

  List<PieChartSectionData> showingSections() {
    return pieDataCustos;
  }
}
