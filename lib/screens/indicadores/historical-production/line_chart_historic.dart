import 'package:cooperado_minha_unimed/colors.dart';
import 'package:cooperado_minha_unimed/screens/indicadores/graphics/linegraph_history.dart';
import 'package:cooperado_minha_unimed/shared/utils/string_utils.dart';
import 'package:cooperado_minha_unimed/shared/vo/indicadores/production_history.dart';
import 'package:flutter/material.dart';

class LineChartHistoric extends StatefulWidget {
  final List<ProductionHistoryVO>? data;
  final bool? animate;
  const LineChartHistoric({super.key, this.data, this.animate});

  @override
  LineChartHistoricState createState() => LineChartHistoricState();
}

class LineChartHistoricState extends State<LineChartHistoric> {
  bool secondSemester = false;
  List<Valores> data = [];
  String? valueSelected;
  Widget? lineChart;

  @override
  void initState() {
    _createChartValues();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        (valueSelected != null)
            ? Text(
                '$valueSelected',
                style: const TextStyle(
                  color: CooperadoColors.green,
                  fontWeight: FontWeight.bold,
                ),
              )
            : Container(),
        SizedBox(
          height: 220,
          //child: lineChart ??= _lineChart(),
          child: LineGraphHistory(data: data),
        ),
      ],
    );
  }

  /// Create series list with multiple series
  _createChartValues() {
    int index = 0;
    data = [];
    for (ProductionHistoryVO d in widget.data!) {
      // index = 0;
      for (ProductionReport mes in d.meses!) {
        data.add(Valores('${meses[mes.mes! - 1]}/${d.ano2Digitos}',
            double.parse(mes.producoes![0].valor.toString()), index));
        index++;
      }
    }
  }
}

const meses = [
  "JAN",
  "FEV",
  "MAR",
  "ABR",
  "MAI",
  "JUN",
  "JUL",
  "AGO",
  "SET",
  "OUT",
  "NOV",
  "DEZ"
];

class Valores {
  final String mes;
  final double? valor;
  final int index;

  Valores(this.mes, this.valor, this.index);

  String get valorFormat => StringUtils.formatMoney(valor!);
}
