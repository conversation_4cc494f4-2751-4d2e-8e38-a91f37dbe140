part of 'config_profile_cubit.dart';

abstract class ConfigProfileState extends Equatable {
  const ConfigProfileState();

  @override
  List<Object?> get props => [];
}

class InitialConfigProfileState extends ConfigProfileState {}

class LoadingAllAdressesState extends ConfigProfileState {
  @override
  List<Object> get props => [];
}

class UpdatingProfileState extends ConfigProfileState {
  @override
  List<Object> get props => [];
}

class ErrorUpdateProfileState extends ConfigProfileState {
  final String message;
  @override
  List<Object> get props => [message];
  const ErrorUpdateProfileState(this.message);
}

class LoadingProfileAddressState extends ConfigProfileState {
  @override
  List<Object> get props => [];
}

class LoadingAddressesByZipcodeState extends ConfigProfileState {
  @override
  List<Object> get props => [];
}

class ErrorConfigProfileState extends ConfigProfileState {
  final String message;

  @override
  List<Object> get props => [message];

  const ErrorConfigProfileState(this.message);
}

class ErrorLoadAddressByZipcodeState extends ConfigProfileState {
  final String message;
  @override
  List<Object> get props => [message];
  const ErrorLoadAddressByZipcodeState(this.message);
}

class ChangedPageState extends ConfigProfileState {
  final int page;

  @override
  List<Object> get props => [page];

  const ChangedPageState(this.page);
}

class ChangedEmailState extends ConfigProfileState {
  @override
  List<Object> get props => [];

  const ChangedEmailState();
}

class UpdatedProfileState extends ConfigProfileState {
  final PayloadAddress? payloadAddress;
  @override
  List<Object?> get props => [payloadAddress];
  const UpdatedProfileState(this.payloadAddress);
}

class LoadedProfileAddressesState extends ConfigProfileState {
  final List<AddressModel>? listAddresses;

  @override
  List<Object?> get props => [listAddresses];

  const LoadedProfileAddressesState(this.listAddresses);
}

class LoadedZipcodeAddressesState extends ConfigProfileState {
  final ZipCodeModel zipcodeResult;

  @override
  List<Object> get props => [zipcodeResult];

  const LoadedZipcodeAddressesState(this.zipcodeResult);
}

class ChangingPasswordState extends ConfigProfileState {
  @override
  List<Object> get props => [];
  const ChangingPasswordState();
}

class ErrorChangePasswordState extends ConfigProfileState {
  final String message;
  @override
  List<Object> get props => [];
  const ErrorChangePasswordState(this.message);
}

class ChangedPasswordState extends ConfigProfileState {
  final String message;
  final String newPassword;
  @override
  List<Object> get props => [message, newPassword];

  const ChangedPasswordState(this.message, this.newPassword);
}

class LoadedAllProfileAddressesState extends ConfigProfileState {
  final PayloadAddress? payloadAddress;

  @override
  List<Object?> get props => [payloadAddress];

  const LoadedAllProfileAddressesState(this.payloadAddress);
}
