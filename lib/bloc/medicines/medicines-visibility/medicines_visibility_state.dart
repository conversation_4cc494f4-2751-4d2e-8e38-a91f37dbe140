part of 'medicines_visibility_cubit.dart';

abstract class MedicinesVisibilityState extends Equatable {
  const MedicinesVisibilityState();
}

class MedicinesVisibilityInitial extends MedicinesVisibilityState {
  @override
  List<Object> get props => [];
}

class VisibilityListMedicinesState extends MedicinesVisibilityState {
  @override
  List<Object> get props => [];

  const VisibilityListMedicinesState();
}

class NoVisibilityListMedicinesState extends MedicinesVisibilityState {
  @override
  List<Object> get props => [];

  const NoVisibilityListMedicinesState();
}

class ErrorMedicinesVisibilityState extends MedicinesVisibilityState {
  final String message;
  @override
  List<Object> get props => [message];
  const ErrorMedicinesVisibilityState(this.message);
}
