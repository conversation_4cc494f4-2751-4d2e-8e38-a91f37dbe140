part of 'medical_production_cubit.dart';

abstract class MedicalProductionState extends Equatable {
  const MedicalProductionState();
}

class InitialMedicalProductionState extends MedicalProductionState {
  @override
  List<Object> get props => [];
}

class LoadingMedicalProductionState extends MedicalProductionState {
  @override
  List<Object> get props => [];
}

class ErrorMedicalProductionState extends MedicalProductionState {
  final String? message;
  @override
  List<Object?> get props => [message];
  const ErrorMedicalProductionState(this.message);
}

class LoadedMedicalProductionState extends MedicalProductionState {
  final List<RetornoProducaoMedica> retornoComparativoCustoVO;
  @override
  List<Object> get props => [retornoComparativoCustoVO];

  const LoadedMedicalProductionState(this.retornoComparativoCustoVO);
}

class SelectDateLoading extends MedicalProductionState {
  @override
  List<Object> get props => [];
}

class DoneSelectDate extends MedicalProductionState {
  final DateTime? date;
  @override
  List<Object?> get props => [date];
  const DoneSelectDate(this.date);
}
