import 'package:bloc/bloc.dart';
import 'package:cooperado_minha_unimed/shared/api/auth.api.dart';
import 'package:cooperado_minha_unimed/shared/locator.dart';
import 'package:cooperado_minha_unimed/shared/services/version.service.dart';
import 'package:equatable/equatable.dart';

part 'update_version_state.dart';

class UpdateVersionCubit extends Cubit<UpdateVersionState> {
  UpdateVersionCubit() : super(UpdateVersionInitial());

  checkVersionEvent() async {
    emit(CheckingVersionState());

    try {
      final versionInfo =
          await (Locator.instance!.get<VersionService>().getInfo());

      final versionValidateResponse =
          await Locator.instance!.get<AuthApi>().checkVersionValidade(
                version: versionInfo.version.trim(),
                buildNumber: versionInfo.buildNumber.trim(),
              );

      emit(versionValidateResponse.isOutOfDate!
          ? OutOfDateState(
              localVersion: versionInfo.version,
              remoteVersion: versionValidateResponse.lastVersion ?? "",
              forceUpdate: versionValidateResponse.force ?? false,
            )
          : UpdatedState());
    } catch (e) {
      emit(ErrorCheckingVersionState(message: e.toString()));
    }
  }
}
