import 'package:cooperado_minha_unimed/shared/api/transparencia.api.dart';
import 'package:cooperado_minha_unimed/shared/locator.dart';
import 'package:cooperado_minha_unimed/shared/vo/transparencia/producao_medica_resumida.vo.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';

part 'medical_production_state.dart';

class MedicalProductionCubit extends Cubit<MedicalProductionState> {
  MedicalProductionCubit() : super(InitialMedicalProductionState());
  DateTime? _selectedDate;
  DateTime? get selectedDate => _selectedDate;
  String? _formattedDate;
  String? get formattedDate => _formattedDate;

  selectDate(DateTime date) {
    emit(SelectDateLoading());
    _selectedDate = date;
    _formattedDate = DateFormat('MM/yyyy').format(_selectedDate!);
    emit(DoneSelectDate(_selectedDate));
  }

  getMedicalProduction(DateTime? gte, DateTime? lte) async {
    try {
      emit(LoadingMedicalProductionState());
      final producaoMed = await Locator.instance!<TransparenciaApi>()
          .getHistoricoProducao(
              dataInicio: gte, dataFim: lte, tipoProducao: 'P');

      emit(LoadedMedicalProductionState(producaoMed));
    } catch (e) {
      emit(ErrorMedicalProductionState(e.toString()));
    }
  }
}
