import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:sensors_plus/sensors_plus.dart';

abstract class OrientationPage extends StatefulWidget {
  final bool activateOrientation;
  const OrientationPage({super.key, required this.activateOrientation});
}

abstract class OrientationPageState<Page extends OrientationPage>
    extends State<Page> {}

mixin OrientationActivate<Page extends OrientationPage>
    on OrientationPageState<Page> {
  final _streamSubscriptions = <StreamSubscription<dynamic>>[];

  _disableRotation() {
    for (final subscription in _streamSubscriptions) {
      subscription.cancel();
    }
  }

  _activateRotation() {
    _streamSubscriptions
        .add(accelerometerEvents.listen((AccelerometerEvent event) {
      if (event.x > 7) {
        SystemChrome.setPreferredOrientations(
            [DeviceOrientation.landscapeLeft]);
      }
      if (event.x < -7) {
        SystemChrome.setPreferredOrientations(
            [DeviceOrientation.landscapeRight]);
      }
      if (event.y > 7) {
        SystemChrome.setPreferredOrientations([DeviceOrientation.portraitUp]);
      }
    }));
  }

  @override
  void initState() {
    if (widget.activateOrientation) _activateRotation();
    super.initState();
  }

  @override
  void dispose() {
    if (widget.activateOrientation) _disableRotation();
    SystemChrome.setPreferredOrientations([DeviceOrientation.portraitUp]);
    super.dispose();
  }
}
