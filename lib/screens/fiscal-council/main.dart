import 'package:cooperado_minha_unimed/bloc/auth/auth_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/fiscal-council/fiscal_council_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/fiscal-council/form-council/form_council_cubit.dart';
import 'package:cooperado_minha_unimed/colors.dart';
import 'package:cooperado_minha_unimed/screens/fiscal-council/card_form.dart';
import 'package:cooperado_minha_unimed/screens/fiscal-council/card_members.dart';
import 'package:cooperado_minha_unimed/screens/fiscal-council/documents/main.dart';
import 'package:cooperado_minha_unimed/shared/constants.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class FiscalCouncilScreen extends StatefulWidget {
  final FirebaseAnalytics analytics;
  final FirebaseAnalyticsObserver observer;

  const FiscalCouncilScreen(
      {super.key, required this.analytics, required this.observer});
  @override
  FiscalCouncilScreenState createState() => FiscalCouncilScreenState();
}

class FiscalCouncilScreenState extends State<FiscalCouncilScreen> {
  String description = "";
  int index = 0;
  bool _isExpanded = false;

  @override
  void initState() {
    description = context.read<AuthCubit>().getMessageConfig(idCounsil);

    super.initState();
    widget.analytics.logScreenView(
      screenName: 'Conselho Fiscal',
      screenClass: 'FiscalCouncilScreen',
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        centerTitle: true,
        title: const Text("Conselho Fiscal"),
        backgroundColor: CooperadoColors.tealGreenDark,
      ),
      body: SafeArea(
        child: RefreshIndicator(
          onRefresh: () async {
            context.read<FiscalCouncilCubit>().getCouncil();
            context.read<FormCouncilCubit>().getTopicEvent();
          },
          color: CooperadoColors.tealGreenDark,
          child: SingleChildScrollView(
            physics: const ClampingScrollPhysics(
                parent: AlwaysScrollableScrollPhysics()),
            child: Container(
              constraints: BoxConstraints(
                minHeight: MediaQuery.of(context).size.height,
              ),
              padding: const EdgeInsets.all(8.0),
              child: Column(
                children: <Widget>[
                  _header(),
                  const CardMembers(),
                  const CardDocuments(),
                  const CardForm()
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _header() {
    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: 8.0,
      ),
      decoration: BoxDecoration(
        color:
            _isExpanded ? CooperadoColors.aliceBlue : CooperadoColors.tealGreen,
        borderRadius: const BorderRadius.only(
          bottomLeft: Radius.circular(15),
          topLeft: Radius.circular(15),
          topRight: Radius.circular(15),
        ),
      ),
      child: Theme(
        data: ThemeWidgets.expansionTile(
          context,
          colorArrow: _isExpanded ? CooperadoColors.tealGreen : Colors.white,
        ),
        child: ListTileTheme(
          contentPadding: const EdgeInsets.symmetric(horizontal: 8),
          child: ExpansionTile(
            backgroundColor: _isExpanded
                ? CooperadoColors.aliceBlue
                : CooperadoColors.tealGreen,
            onExpansionChanged: (value) {
              setState(() {
                _isExpanded = value;
              });
            },
            title: Text(
              'Conselho Fiscal',
              style: TextStyle(
                  color:
                      _isExpanded ? CooperadoColors.blackText : Colors.white),
            ),
            children: <Widget>[
              Padding(
                padding: const EdgeInsets.all(8),
                child: Text(
                  description,
                  style: const TextStyle(color: CooperadoColors.blackText),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
