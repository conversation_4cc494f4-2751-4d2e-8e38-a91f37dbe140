import 'package:cooperado_minha_unimed/bloc/medicines/medicines-visibility/medicines_visibility_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/servicos/relatorio-producao/report_production_cubit.dart';
import 'package:cooperado_minha_unimed/colors.dart';
import 'package:cooperado_minha_unimed/screens/medicine/main.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class CardListMendicines extends StatefulWidget {
  const CardListMendicines({super.key});

  @override
  CardListMendicinesState createState() => CardListMendicinesState();
}

class CardListMendicinesState extends State<CardListMendicines> {
  DateTime? selectedDateTime;

  @override
  void initState() {
    context.read<ReportProductionCubit>().resetState();
    selectedDateTime = context.read<ReportProductionCubit>().lastDate;
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<MedicinesVisibilityCubit, MedicinesVisibilityState>(
      builder: (context, state) {
        if (state is NoVisibilityListMedicinesState) return Container();

        return Card(
          child: Container(
            padding: const EdgeInsets.all(10),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: <Widget>[
                const Text(
                  "Listagem Medicamentosa",
                  style: TextStyle(
                    color: CooperadoColors.blackText,
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 10),
                //indicatorErrorState(),
                const SizedBox(height: 5),
                ElevatedButton(
                  style: ElevatedButton.styleFrom(backgroundColor: CooperadoColors.tealGreen),
                  onPressed: () => Navigator.push(context, MaterialPageRoute(builder: (context) => const MedicineScreen())),
                  child: const Text("Pesquisar Medicamento"),
                ),
                const SizedBox(height: 5)
              ],
            ),
          ),
        );
      },
    );
  }
}
