import 'package:cooperado_minha_unimed/models/glosa_resource/glosa_resource.model.dart';
import 'package:equatable/equatable.dart';

abstract class GlosaResourceState extends Equatable {
  const GlosaResourceState();
}

class GlosaResourceInitial extends GlosaResourceState {
  @override
  List<Object> get props => [];
}

class LoadingGetGlosaResourceState extends GlosaResourceState {
  @override
  List<Object> get props => [];
}

class ErrorGlosaResourceState extends GlosaResourceState {
  final String message;
  @override
  List<Object> get props => [message];
  const ErrorGlosaResourceState(this.message);
}

class DoneGetGlosaResourceState extends GlosaResourceState {
  final List<GlosaResourceData> list;
  @override
  List<Object?> get props => [list];

  const DoneGetGlosaResourceState(this.list);
}
