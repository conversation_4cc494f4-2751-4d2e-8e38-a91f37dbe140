import 'package:flutter_masked_text2/flutter_masked_text2.dart';
import 'package:intl/intl.dart';

class StringUtils {
  static String enumName(String enumToString) {
    List<String> paths = enumToString.split(".");
    return paths[paths.length - 1];
  }

  static bool validateEmail(String value) {
    return RegExp(r"^[a-zA-Z.a-zA-Z0-9.a-zA-Z0-9.!#$%&'*+-/=?^_`{|}~]+@[a-zA-Z0-9]+\.[a-zA-Z]+").hasMatch(value) && !value.contains(' ') && _validateLastEmailPart(value);
  }

  static bool _validateLastEmailPart(String value) {
    if (value.indexOf('.') > 0) {
      final parts = value.split('.');

      final last = parts[parts.length - 1];

      if (last.length > 3) return false;
      return RegExp(r"^[a-z]{2,3}").hasMatch(last);
    }

    return false;
  }

  static String onlyNumber(String str) {
    final numberRegex = RegExp(r'[0-9]', multiLine: true);
    String strOnlyNumber = '';
    Iterable matches = numberRegex.allMatches(str);
    for (var match in matches) {
      strOnlyNumber += str.substring(match.start, match.end);
    }
    return strOnlyNumber;
  }

  static String formatMoney(double value) {
    NumberFormat formatter = NumberFormat.simpleCurrency(locale: 'pt_BR');
    return formatter.format(value);
  }

  static String formatMoneyDynamic(dynamic value) {
    NumberFormat formatter = NumberFormat.simpleCurrency(locale: 'pt_BR');
    return formatter.format(value);
  }

  static String formatThousands(int? value) {
    NumberFormat formatter = NumberFormat("###,###,###", "eu");
    return formatter.format(value);
  }

   static String formatDuration(Duration duration) {
    String twoDigits(int n) => n.toString().padLeft(2, "0");
    String twoDigitMinutes = twoDigits(duration.inMinutes.remainder(60));
    String twoDigitSeconds = twoDigits(duration.inSeconds.remainder(60));
    return "${twoDigits(duration.inHours)}:$twoDigitMinutes:${twoDigitSeconds}s";
  }

  static bool validateCpf(String strCPF) {
    if (strCPF.length < 11) return false;
    int soma = 0;
    int resto = 0;
    soma = 0;
    if (strCPF == "00000000000" || strCPF == "11111111111" || strCPF == "22222222222" || strCPF == "3333333333" || strCPF == "44444444444" || strCPF == "55555555555" || strCPF == "66666666666" || strCPF == "77777777777" || strCPF == "88888888888" || strCPF == "99999999999") {
      return false;
    }

    for (int i = 1; i <= 9; i++) {
      soma = soma + int.parse(strCPF.substring(i - 1, i)) * (11 - i);
    }
    resto = (soma * 10) % 11;

    if ((resto == 10) || (resto == 11)) resto = 0;
    if (resto != int.parse(strCPF.substring(9, 10))) return false;

    soma = 0;
    for (int i = 1; i <= 10; i++) {
      soma = soma + int.parse(strCPF.substring(i - 1, i)) * (12 - i);
    }
    resto = (soma * 10) % 11;

    if ((resto == 10) || (resto == 11)) resto = 0;
    if (resto != int.parse(strCPF.substring(10, 11))) {
      return false;
    } else {
      return true;
    }
  }

  static String formatMonth(int mes) {
    return mes < 13 && mes > 0 ? ['Janeiro', 'Fevereiro', 'Março', 'Abril', 'Maio', 'Junho', 'Julho', 'Agosto', 'Setembro', 'Outubro', 'Novembro', 'Dezembro'][mes - 1] : '';
  }

  static String pontuationSeparator(int? value) {
    return value.toString().replaceAllMapped(RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'), (Match m) => '${m[1]}.');
  }

  static String ptBrValue(double? value) {
    final f = NumberFormat("###0.0#", "pt_BR");
    return '${f.format(value)}%';
  }

  static String formatFilenamePDF(String? filename) {
    if (filename == null) return 'file.pdf';
    String filenameChecked;
    filenameChecked = filename.replaceAll(RegExp(r"[ _/|\$%&#$@!`~*]"), "-");
    filenameChecked = filenameChecked.replaceAll(RegExp(r"[ÀÁÂÃÄÅàáâãäåÒÓÔÕÕÖØòóôõöøÈÉÊËèéêëðÇçÐÌÍÎÏìíîïÙÚÛÜùúûüÑñŠšŸÿýŽž]"), "");
    // filenameChecked = filenameChecked.split('.')[0] ?? 'file';
    filenameChecked = filenameChecked.split('.').first;
    return '$filenameChecked.pdf';
  }

  static String formatPhone(String? value) {
    return MaskedTextController(text: value, mask: '(00) 00000-0000').text;
  }

  static String formatPeriod(String? dateBegin, String dateEnd) {
    final dateEndSplit = dateEnd.split('/');
    if (dateEndSplit.length < 3) return '';
    final dayBegin = dateBegin!.split('/')[0];
    final dayEnd = dateEndSplit[0];
    final month = StringUtils.formatMonth(int.tryParse(dateEndSplit[1])!);
    final year = dateEndSplit[2];
    return 'De $dayBegin a $dayEnd de $month  de $year';
  }

  static String limitString(String texto, int limite) {
    if (texto.length <= limite) {
      return texto;
    } else {
      return texto.substring(0, limite);
    }
  }

  static String formatDate(DateTime? date) {
    if (date == null) return '';
    return DateFormat('MM/yyyy').format(date);
  }
}
