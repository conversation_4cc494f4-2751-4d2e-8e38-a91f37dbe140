import 'package:cooperado_minha_unimed/shared/api/transparencia.api.dart';
import 'package:cooperado_minha_unimed/shared/locator.dart';
import 'package:cooperado_minha_unimed/shared/vo/transparencia/demostrativo_resultado.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

part 'demonstatives_results_state.dart';

class DemonstrativesResultsCubit extends Cubit<DemonstrativesResultsState> {
  DemonstrativesResultsCubit() : super(InitialDemonstrativesResultsState());

  getDemonstrativesResults({int? pagina}) async {
    try {
      emit(LoadingDemonstrativesResultsState());
      emit(LoadedDemonstrativesResultsState(await Locator.instance!
              <TransparenciaApi>()
          .getDemonstrativoResultados(pagina: pagina)));
    } catch (e) {
      emit(ErrorDemonstrativesResultsState(e.toString()));
    }
  }
}
