part of 'res_indicators_cubit.dart';

abstract class ResIndicatorsState extends Equatable {
  const ResIndicatorsState();

  @override
  List<Object> get props => [];
}

class InitialResIndicatorState extends ResIndicatorsState {}

class LoadingResAllIndicatorsState extends ResIndicatorsState {
  @override
  List<Object> get props => [];
}

class ErrorResIndicatorState extends ResIndicatorsState {
  final String message;

  @override
  List<Object> get props => [message];

  const ErrorResIndicatorState({required this.message});
}

class LoadedResIndicatorState extends ResIndicatorsState {
  final List<dynamic> listResIndicators;

  @override
  List<Object> get props => [listResIndicators];

  const LoadedResIndicatorState({required this.listResIndicators});
}

class LoadingResIndicatorSearchState extends ResIndicatorsState {
  @override
  List<Object> get props => [];
}

class ErrorResIndicatorsSearchState extends ResIndicatorsState {
  final String message;

  @override
  List<Object> get props => [message];

  const ErrorResIndicatorsSearchState({required this.message});
}

class NoDataResIndicatorState extends ResIndicatorsState {
  @override
  List<Object> get props => [];
}
