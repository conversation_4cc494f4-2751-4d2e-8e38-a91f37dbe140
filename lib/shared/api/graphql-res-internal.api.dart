import 'package:cooperado_minha_unimed/models/res-internal/image_result.model.dart';
import 'package:cooperado_minha_unimed/models/res-internal/lab_exam.model.dart';
import 'package:cooperado_minha_unimed/models/res-internal/lab_exam_detail.model.dart';
import 'package:cooperado_minha_unimed/models/res-internal/solicitation.model.dart';
import 'package:cooperado_minha_unimed/shared/api/graphql.api.dart';
import 'package:cooperado_minha_unimed/shared/exceptions.dart';

import 'package:graphql/client.dart';

class GraphQlApiInternal extends GraphQlApi {
  GraphQlApiInternal(super.httpClient);

  Future<List<ResSolicitationModel>> resInternalSolicitationsByCRM(
      {required String crm}) async {
    try {
      final GraphQLClient client = await getGithubGraphQLClient();

      String query = '''
       query ResInternalSolicitationsByCRM {
          resInternalSolicitationsByCRM(crm: "$crm") {
              numInvoice
              typeSolicitation
              card
              codProvider
              nameProvider
              nameBeneficiary
              attendanceDate
          }
      }
      ''';

      logger.e('resInternalSolicitationsByCRM query : $query');

      final QueryOptions options = QueryOptions(
        document: gql(
          query,
        ),
      );

      final QueryResult result = await client.query(options);

      if (result.hasException) {
        logger
            .e('resInternalSolicitationsByCRM exception : ${result.exception}');
        throw UnimedException('Não foi possível no momento.');
      } else {
        final data = result.data!['resInternalSolicitationsByCRM'] as List;
        logger.d('resInternalSolicitationsByCRM success list ${data.length}');

        final collection = data.map((e) {
          return ResSolicitationModel.fromJson(e);
        }).toList();

        return collection;
      }
    } on UnimedException catch (ex) {
      logger.e('resInternalSolicitationsByCRM ${ex.runtimeType} : $ex');
      throw DiretoriaException(ex.message);
    } catch (ex) {
      logger.e('resInternalSolicitationsByCRM exception : $ex');
      throw DiretoriaException('Não foi possível no momento.');
    }
  }

  Future<List<ResLabExamModel>> resInternalLabExamsByCard(
      {required String card}) async {
    try {
      final GraphQLClient client = await getGithubGraphQLClient();

      String query = '''
        query ResInternalLabExamsByCard {
            resInternalLabExamsByCard(card: "$card") {
                doctorEmail
                doctorId
                doctorName
                orderDate
                orderId
                orderLabel
                orderStatus
                patientName
                resultPdfUrl
                resultScheduledDate
                unit
                unitId
                unitName
            }
        }
      ''';

      logger.e('resInternalLabExamsByCard query : $query');

      final QueryOptions options = QueryOptions(
        document: gql(
          query,
        ),
      );

      final QueryResult result = await client.query(options);

      if (result.hasException) {
        String errorMessage = result.exception?.graphqlErrors.first.message ??
            'Erro desconhecido';

        // Extrair apenas a parte da mensagem antes do parêntese
        if (errorMessage.contains('(')) {
          errorMessage =
              errorMessage.substring(0, errorMessage.indexOf('(')).trim();
        }

        logger.e('resInternalLabExamsByCard exception : ${result.exception}');
        throw UnimedException(errorMessage);
      } else {
        final data = result.data!['resInternalLabExamsByCard'] as List;
        logger.d('resInternalLabExamsByCard success list ${data.length}');

        final collection = data.map((e) {
          return ResLabExamModel.fromJson(e);
        }).toList();

        return collection;
      }
    } on UnimedException catch (ex) {
      logger.e('resInternalLabExamsByCard ${ex.runtimeType} : $ex');
      throw DiretoriaException(ex.message);
    } catch (ex) {
      logger.e('resInternalLabExamsByCard exception : $ex');
      throw DiretoriaException('Não foi possível no momento.');
    }
  }

  Future<List<ResLabExamDetailModel>> resInternalLabExamDetailsByCardAndOrderId(
      {required String card, required String orderId}) async {
    try {
      final GraphQLClient client = await getGithubGraphQLClient();

      String query = '''
        query ResInternalLabExamDetailsByCardAndOrderId {
            resInternalLabExamDetailsByCardAndOrderId(
                card: "$card"
                orderId: "$orderId"
            ) {
                material
                resultScheduledDate
                samplePendency
                testId
                testName
                testPdfUrl
                testStatus
                collectionDate
            }
        }
      ''';

      logger.e('resInternalLabExamDetailsByCardAndOrderId query : $query');

      final QueryOptions options = QueryOptions(
        document: gql(
          query,
        ),
      );

      final QueryResult result = await client.query(options);

      if (result.hasException) {
        logger.e(
            'resInternalLabExamDetailsByCardAndOrderId exception : ${result.exception}');
        throw UnimedException('Não foi possível no momento.');
      } else {
        final data =
            result.data!['resInternalLabExamDetailsByCardAndOrderId'] as List;
        logger.d(
            'resInternalLabExamDetailsByCardAndOrderId success list ${data.length}');

        final collection = data.map((e) {
          return ResLabExamDetailModel.fromJson(e);
        }).toList();

        return collection;
      }
    } on UnimedException catch (ex) {
      logger.e(
          'resInternalLabExamDetailsByCardAndOrderId ${ex.runtimeType} : $ex');
      throw DiretoriaException(ex.message);
    } catch (ex) {
      logger.e('resInternalLabExamDetailsByCardAndOrderId exception : $ex');
      throw DiretoriaException('Não foi possível no momento.');
    }
  }

  Future<List<ResImageExamResultModel>> resInternalImageExamResultsByCard({
    required String card,
  }) async {
    try {
      final GraphQLClient client = await getGithubGraphQLClient();

      String query = '''
        query ResInternalLabExamsByCard {
          resInternalImageExamResultsByCard(card: "$card") {
              descriptionExam
              codOrder
              cpf
              codPatient
              namePatient
              crm
              nameProvider
              solicitationDate
              resultDate
              imageLinks {
                  codOrder
                  codOrderItem
                  examName
                  link
              }
          }
      }
      ''';

      logger.e('resInternalImageExamResultsByCard query : $query');

      final QueryOptions options = QueryOptions(
        document: gql(
          query,
        ),
      );

      final QueryResult result = await client.query(options);

      if (result.hasException) {
        final errorMessage = result.exception?.graphqlErrors.first.message ??
            'Erro desconhecido';
        logger.e('resInternalImageExamResultsByCard exception : $errorMessage');
        throw UnimedException(errorMessage);
      } else {
        final data = result.data!['resInternalImageExamResultsByCard'] as List;
        logger
            .d('resInternalImageExamResultsByCard success list ${data.length}');

        final collection = data.map((e) {
          return ResImageExamResultModel.fromJson(e);
        }).toList();

        return collection;
      }
    } on UnimedException catch (ex) {
      logger.e('resInternalImageExamResultsByCard ${ex.runtimeType} : $ex');
      throw GraphQlException(ex.message);
    } catch (ex) {
      logger.e('resInternalImageExamResultsByCard exception : $ex');
      throw GraphQlException('Não foi possível no momento.');
    }
  }
}
