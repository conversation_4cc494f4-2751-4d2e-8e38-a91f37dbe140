import 'package:cooperado_minha_unimed/bloc/sensitive-data/sensitive_data_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/sensitive-data/sensitive_data_state.dart';
import 'package:cooperado_minha_unimed/colors.dart';
import 'package:cooperado_minha_unimed/screens/transparencia/contingency-index/widgets/contingency_table.dart';
import 'package:cooperado_minha_unimed/screens/transparencia/graph-transparency/contingency_barchart.dart';
import 'package:cooperado_minha_unimed/screens/transparencia/widgets/header_chart.dart';
import 'package:cooperado_minha_unimed/shared/vo/transparencia/indices.dart';
import 'package:cooperado_minha_unimed/shared/widgets/transparency_collapsible_head.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../bloc/transparency/contingency_index/contingency_index_cubit.dart';

class ContingencyIndexDetail extends StatefulWidget {
  final VOIndicatorModel? contingencyIndex;
  const ContingencyIndexDetail({super.key, this.contingencyIndex});
  @override
  ContingencyIndexDetailState createState() => ContingencyIndexDetailState();
}

class ContingencyIndexDetailState extends State<ContingencyIndexDetail> {
  @override
  void initState() {
    super.initState();
  }


  void _getContingencyIndex() {
    context.read<ContingencyIndexCubit>().getContingencyIndex();
  }
  int dataLimit = 6;

  @override
  Widget build(BuildContext context) {
    return OrientationBuilder(
      builder: (context, orientation) {
        dataLimit =
            MediaQuery.of(context).orientation == Orientation.portrait ? 6 : 12;
        return PopScope(
          onPopInvokedWithResult: (value, result) async {
            _getContingencyIndex();
          },
          child: Scaffold(
            appBar: AppBar(
              title: const Text("Contingência"),
              backgroundColor: CooperadoColors.tealGreenDark,
              actions: [
                   Padding(
                   padding: const EdgeInsets.only(right: 15.0),
                   child: IconButton(
                             icon: BlocBuilder<SensitiveDataCubit, SensitiveDataState>(
                    builder: (context, state) {
                      return Icon(
                        state.isSensitiveDataVisible ? Icons.visibility : Icons.visibility_off,
                      );
                    },
                             ),
                             onPressed: () {
                    context.read<SensitiveDataCubit>().toggleSensitiveDataVisibility();
                             },
                           ),
                 ),_iconChangeOrientations(orientation)
              ],
            ),
            body: SafeArea(
              child: Container(
                padding: const EdgeInsets.all(10.0),
                child: ListView(
                  children: <Widget>[
                    const TransparencyCollapsibleHead(
                      title: 'O que é fundo de contingência',
                      dividend: 'Montante a pagar no mês',
                      divider: 'Valor da produção + Pró-labore',
                      quotient: 'Desconto em %',
                      quotientSub: '(Resultado x 100)',
                    ),
                    const TransparencyHeaderChart(title: 'Fundo de contingência'),
                    const SizedBox(height: 20),
                    SizedBox(
                      height: 200,
                      child: ContingencyBarChart(
                        animate: true,
                        data: widget.contingencyIndex!.data,
                        colors: [
                          CooperadoColors.chartColors[7],
                        ],
                        orientation: MediaQuery.of(context).orientation,
                          dataLimit: dataLimit,
                      ),
                    ),
                    const SizedBox(height: 15),
                    ContingencyTable(
                      contingencyIndex: widget.contingencyIndex,
                      color: CooperadoColors.chartColors[7],
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      }
    );
  }

   Widget _iconChangeOrientations(orientation) {
    return IconButton(
      icon: const Icon(Icons.screen_rotation_rounded, color: Colors.white),
      onPressed: () {
        orientation.index == DeviceOrientation.portraitUp.index
            ? SystemChrome.setPreferredOrientations(
                [DeviceOrientation.landscapeLeft])
            : SystemChrome.setPreferredOrientations(
                [DeviceOrientation.portraitUp]);
      },
    );
  }
}
