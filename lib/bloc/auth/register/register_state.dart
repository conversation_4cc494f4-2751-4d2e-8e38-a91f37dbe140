part of 'register_cubit.dart';

abstract class RegisterState extends Equatable {
  const RegisterState();

  @override
  List<Object?> get props => [];
}

class RegisterInitial extends RegisterState {}

class LoadingRegisterState extends RegisterState {
  @override
  List<Object> get props => [];
}

class ErrorRegisterState extends RegisterState {
  final String message;

  @override
  List<Object> get props => [message];

  const ErrorRegisterState(this.message);
}

class DoneRegisterState extends RegisterState {
  final String? user;

  @override
  List<Object?> get props => [user];

  const DoneRegisterState(this.user);
}
