// GENERATED CODE - DO NOT MODIFY BY HAND
// This code was generated by ObjectBox. To update it run the generator again
// with `dart run build_runner build`.
// See also https://docs.objectbox.io/getting-started#generate-objectbox-code

// ignore_for_file: camel_case_types, depend_on_referenced_packages
// coverage:ignore-file

import 'dart:typed_data';

import 'package:flat_buffers/flat_buffers.dart' as fb;
import 'package:objectbox/internal.dart'
    as obx_int; // generated code can access "internal" functionality
import 'package:objectbox/objectbox.dart' as obx;
import 'package:objectbox_flutter_libs/objectbox_flutter_libs.dart';

import 'models/medicine.model.dart';

export 'package:objectbox/objectbox.dart'; // so that callers only have to import this file

final _entities = <obx_int.ModelEntity>[
  obx_int.ModelEntity(
      id: const obx_int.IdUid(1, 6065420062645272092),
      name: 'Medicine<PERSON><PERSON><PERSON>',
      lastPropertyId: const obx_int.IdUid(13, 4718767397104344303),
      flags: 0,
      properties: <obx_int.ModelProperty>[
        obx_int.ModelProperty(
            id: const obx_int.IdUid(1, 1052784538226618901),
            name: 'id',
            type: 6,
            flags: 1),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(4, 1666465937154841173),
            name: 'codProduto',
            type: 9,
            flags: 2048,
            indexId: const obx_int.IdUid(2, 4433880279138473262)),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(5, 9037578023627495238),
            name: 'descricaoProduto',
            type: 9,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(6, 8642648986981787316),
            name: 'codigoUnidade',
            type: 9,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(7, 6455795718173183460),
            name: 'descricaoUnidade',
            type: 9,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(8, 5548511493012502856),
            name: 'descricaoClasse',
            type: 9,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(9, 9219227405121542620),
            name: 'valorFormatadoUltimaEntrada',
            type: 9,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(10, 6837400757240078012),
            name: 'descricaoSubstancia',
            type: 9,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(12, 5079960661590223253),
            name: 'dataUltimaEntrada',
            type: 9,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(13, 4718767397104344303),
            name: 'createdAt',
            type: 9,
            flags: 0)
      ],
      relations: <obx_int.ModelRelation>[],
      backlinks: <obx_int.ModelBacklink>[])
];

/// Shortcut for [obx.Store.new] that passes [getObjectBoxModel] and for Flutter
/// apps by default a [directory] using `defaultStoreDirectory()` from the
/// ObjectBox Flutter library.
///
/// Note: for desktop apps it is recommended to specify a unique [directory].
///
/// See [obx.Store.new] for an explanation of all parameters.
///
/// For Flutter apps, also calls `loadObjectBoxLibraryAndroidCompat()` from
/// the ObjectBox Flutter library to fix loading the native ObjectBox library
/// on Android 6 and older.
Future<obx.Store> openStore(
    {String? directory,
    int? maxDBSizeInKB,
    int? maxDataSizeInKB,
    int? fileMode,
    int? maxReaders,
    bool queriesCaseSensitiveDefault = true,
    String? macosApplicationGroup}) async {
  await loadObjectBoxLibraryAndroidCompat();
  return obx.Store(getObjectBoxModel(),
      directory: directory ?? (await defaultStoreDirectory()).path,
      maxDBSizeInKB: maxDBSizeInKB,
      maxDataSizeInKB: maxDataSizeInKB,
      fileMode: fileMode,
      maxReaders: maxReaders,
      queriesCaseSensitiveDefault: queriesCaseSensitiveDefault,
      macosApplicationGroup: macosApplicationGroup);
}

/// Returns the ObjectBox model definition for this project for use with
/// [obx.Store.new].
obx_int.ModelDefinition getObjectBoxModel() {
  final model = obx_int.ModelInfo(
      entities: _entities,
      lastEntityId: const obx_int.IdUid(1, 6065420062645272092),
      lastIndexId: const obx_int.IdUid(2, 4433880279138473262),
      lastRelationId: const obx_int.IdUid(0, 0),
      lastSequenceId: const obx_int.IdUid(0, 0),
      retiredEntityUids: const [],
      retiredIndexUids: const [1464655163667718871],
      retiredPropertyUids: const [
        8595638658350706797,
        2631742396173689119,
        5357227378264756403
      ],
      retiredRelationUids: const [],
      modelVersion: 5,
      modelVersionParserMinimum: 5,
      version: 1);

  final bindings = <Type, obx_int.EntityDefinition>{
    MedicineModel: obx_int.EntityDefinition<MedicineModel>(
        model: _entities[0],
        toOneRelations: (MedicineModel object) => [],
        toManyRelations: (MedicineModel object) => {},
        getId: (MedicineModel object) => object.id,
        setId: (MedicineModel object, int id) {
          object.id = id;
        },
        objectToFB: (MedicineModel object, fb.Builder fbb) {
          final codProdutoOffset = object.codProduto == null
              ? null
              : fbb.writeString(object.codProduto!);
          final descricaoProdutoOffset = object.descricaoProduto == null
              ? null
              : fbb.writeString(object.descricaoProduto!);
          final codigoUnidadeOffset = object.codigoUnidade == null
              ? null
              : fbb.writeString(object.codigoUnidade!);
          final descricaoUnidadeOffset = object.descricaoUnidade == null
              ? null
              : fbb.writeString(object.descricaoUnidade!);
          final descricaoClasseOffset = object.descricaoClasse == null
              ? null
              : fbb.writeString(object.descricaoClasse!);
          final valorFormatadoUltimaEntradaOffset =
              object.valorFormatadoUltimaEntrada == null
                  ? null
                  : fbb.writeString(object.valorFormatadoUltimaEntrada!);
          final descricaoSubstanciaOffset = object.descricaoSubstancia == null
              ? null
              : fbb.writeString(object.descricaoSubstancia!);
          final dataUltimaEntradaOffset = object.dataUltimaEntrada == null
              ? null
              : fbb.writeString(object.dataUltimaEntrada!);
          final createdAtOffset = object.createdAt == null
              ? null
              : fbb.writeString(object.createdAt!);
          fbb.startTable(14);
          fbb.addInt64(0, object.id);
          fbb.addOffset(3, codProdutoOffset);
          fbb.addOffset(4, descricaoProdutoOffset);
          fbb.addOffset(5, codigoUnidadeOffset);
          fbb.addOffset(6, descricaoUnidadeOffset);
          fbb.addOffset(7, descricaoClasseOffset);
          fbb.addOffset(8, valorFormatadoUltimaEntradaOffset);
          fbb.addOffset(9, descricaoSubstanciaOffset);
          fbb.addOffset(11, dataUltimaEntradaOffset);
          fbb.addOffset(12, createdAtOffset);
          fbb.finish(fbb.endTable());
          return object.id;
        },
        objectFromFB: (obx.Store store, ByteData fbData) {
          final buffer = fb.BufferContext(fbData);
          final rootOffset = buffer.derefObject(0);
          final codProdutoParam = const fb.StringReader(asciiOptimization: true)
              .vTableGetNullable(buffer, rootOffset, 10);
          final descricaoProdutoParam =
              const fb.StringReader(asciiOptimization: true)
                  .vTableGetNullable(buffer, rootOffset, 12);
          final codigoUnidadeParam =
              const fb.StringReader(asciiOptimization: true)
                  .vTableGetNullable(buffer, rootOffset, 14);
          final descricaoUnidadeParam =
              const fb.StringReader(asciiOptimization: true)
                  .vTableGetNullable(buffer, rootOffset, 16);
          final descricaoClasseParam =
              const fb.StringReader(asciiOptimization: true)
                  .vTableGetNullable(buffer, rootOffset, 18);
          final valorFormatadoUltimaEntradaParam =
              const fb.StringReader(asciiOptimization: true)
                  .vTableGetNullable(buffer, rootOffset, 20);
          final descricaoSubstanciaParam =
              const fb.StringReader(asciiOptimization: true)
                  .vTableGetNullable(buffer, rootOffset, 22);
          final dataUltimaEntradaParam =
              const fb.StringReader(asciiOptimization: true)
                  .vTableGetNullable(buffer, rootOffset, 26);
          final createdAtParam = const fb.StringReader(asciiOptimization: true)
              .vTableGetNullable(buffer, rootOffset, 28);
          final object = MedicineModel(
              codProduto: codProdutoParam,
              descricaoProduto: descricaoProdutoParam,
              codigoUnidade: codigoUnidadeParam,
              descricaoUnidade: descricaoUnidadeParam,
              descricaoClasse: descricaoClasseParam,
              valorFormatadoUltimaEntrada: valorFormatadoUltimaEntradaParam,
              descricaoSubstancia: descricaoSubstanciaParam,
              dataUltimaEntrada: dataUltimaEntradaParam,
              createdAt: createdAtParam)
            ..id = const fb.Int64Reader().vTableGet(buffer, rootOffset, 4, 0);

          return object;
        })
  };

  return obx_int.ModelDefinition(model, bindings);
}

/// [MedicineModel] entity fields to define ObjectBox queries.
class MedicineModel_ {
  /// See [MedicineModel.id].
  static final id =
      obx.QueryIntegerProperty<MedicineModel>(_entities[0].properties[0]);

  /// See [MedicineModel.codProduto].
  static final codProduto =
      obx.QueryStringProperty<MedicineModel>(_entities[0].properties[1]);

  /// See [MedicineModel.descricaoProduto].
  static final descricaoProduto =
      obx.QueryStringProperty<MedicineModel>(_entities[0].properties[2]);

  /// See [MedicineModel.codigoUnidade].
  static final codigoUnidade =
      obx.QueryStringProperty<MedicineModel>(_entities[0].properties[3]);

  /// See [MedicineModel.descricaoUnidade].
  static final descricaoUnidade =
      obx.QueryStringProperty<MedicineModel>(_entities[0].properties[4]);

  /// See [MedicineModel.descricaoClasse].
  static final descricaoClasse =
      obx.QueryStringProperty<MedicineModel>(_entities[0].properties[5]);

  /// See [MedicineModel.valorFormatadoUltimaEntrada].
  static final valorFormatadoUltimaEntrada =
      obx.QueryStringProperty<MedicineModel>(_entities[0].properties[6]);

  /// See [MedicineModel.descricaoSubstancia].
  static final descricaoSubstancia =
      obx.QueryStringProperty<MedicineModel>(_entities[0].properties[7]);

  /// See [MedicineModel.dataUltimaEntrada].
  static final dataUltimaEntrada =
      obx.QueryStringProperty<MedicineModel>(_entities[0].properties[8]);

  /// See [MedicineModel.createdAt].
  static final createdAt =
      obx.QueryStringProperty<MedicineModel>(_entities[0].properties[9]);
}
