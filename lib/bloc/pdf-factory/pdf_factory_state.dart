part of 'pdf_factory_cubit.dart';

abstract class PdfFactoryState extends Equatable {
  const PdfFactoryState();

  @override
  List<Object> get props => [];
}

class PdfFactoryInitial extends PdfFactoryState {}

class LoadingPdf extends PdfFactoryState {
  @override
  List<Object> get props => [];
}

class DonePdf extends PdfFactoryState {
  final PdfController? pdfDocument;
  final File? file;
  @override
  List<Object> get props => [];

  const DonePdf({this.pdfDocument, this.file});
}

class ErrorPdf extends PdfFactoryState {
  final String message;
  @override
  List<Object> get props => [message];

  const ErrorPdf(this.message);
}
