import 'package:cooperado_minha_unimed/models/glosa_resource/glosa_resource.model.dart';
import 'package:equatable/equatable.dart';

abstract class AddGlosaResourceState extends Equatable {
  const AddGlosaResourceState();
}

class AddGlosaResourceInitial extends AddGlosaResourceState {
  @override
  List<Object> get props => [];
}

class LoadingAddGlosaResourceState extends AddGlosaResourceState {
  @override
  List<Object> get props => [];
}

class ErrorAddGlosaResourceState extends AddGlosaResourceState {
  final String message;
  @override
  List<Object> get props => [message];
  const ErrorAddGlosaResourceState(this.message);
}

class DoneAddGlosaResourceState extends AddGlosaResourceState {
  final GlosaResourceData solicitacao;
  @override
  List<Object?> get props => [solicitacao];

  const DoneAddGlosaResourceState(this.solicitacao);
}
