part of 'medicines_cubit.dart';

abstract class MedicinesState extends Equatable {
  const MedicinesState();
}

class MedicinesInitial extends MedicinesState {
  @override
  List<Object> get props => [];
}

class LoadingMedicinesState extends MedicinesState {
  @override
  List<Object?> get props => [];
}

class LoadedListMedicinesState extends MedicinesState {
  final List<MedicineModel> medicines;

  @override
  List<Object?> get props => [];

  const LoadedListMedicinesState({required this.medicines});
}

class ErrorMedicinesState extends MedicinesState {
  final String message;

  @override
  List<Object> get props => [];

  const ErrorMedicinesState(this.message);
}
