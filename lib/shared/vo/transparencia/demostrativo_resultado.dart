import 'package:intl/intl.dart';

class DemonstrativoResultadosVO {
  String? titulo;
  String? descricao;
  int? totalPaginas;
  List<Noticia>? noticias;
  int? paginaAtual;

  DemonstrativoResultadosVO(
      {this.titulo,
      this.descricao,
      this.totalPaginas,
      this.noticias,
      this.paginaAtual});

  DemonstrativoResultadosVO.fromJson(Map<String, dynamic> json) {
    titulo = json['titulo'];
    descricao = json['descricao'];
    totalPaginas = json['totalPaginas'];
    if (json['noticias'] != null) {
      noticias = [];
      json['noticias'].forEach((v) {
        noticias!.add(Noticia.fromJson(v));
      });
    }
    paginaAtual = json['paginaAtual'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['titulo'] = titulo;
    data['descricao'] = descricao;
    data['totalPaginas'] = totalPaginas;
    if (noticias != null) {
      data['noticias'] = noticias!.map((v) => v.toJson()).toList();
    }
    data['paginaAtual'] = paginaAtual;
    return data;
  }
}

class Noticia {
  int? id;
  String? titulo;
  String? status;
  String? destinatario;
  String? assunto;
  String? arquivo;
  String? url;
  List<String>? tipoNoticia;
  DateTime? data;
  Seo? seo;
  String? content;
  bool? lido;

  Noticia(
      {this.id,
      this.titulo,
      this.status,
      this.destinatario,
      this.assunto,
      this.arquivo,
      this.url,
      this.tipoNoticia,
      this.data,
      this.seo,
      this.content,
      this.lido});

  String get dataFormatted => DateFormat('dd/MM/yyyy').format(data!);

  Noticia.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    titulo = json['titulo'];
    status = json['status'];
    destinatario = json['destinatario'];
    assunto = json['assunto'];
    arquivo = json['arquivo'];
    url = json['url'];
    data = DateTime.parse(json['data']['year'].toString() +
        json['data']['monthOfYear'].toString().padLeft(2, '0') +
        json['data']['dayOfMonth'].toString().padLeft(2, '0'));
    tipoNoticia = json['tipo_noticia'].cast<String>();
    seo = json['seo'] != null ? Seo.fromJson(json['seo']) : null;
    content = json['content'];
    lido = json['lido'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['titulo'] = titulo;
    data['status'] = status;
    data['destinatario'] = destinatario;
    data['assunto'] = assunto;
    data['arquivo'] = arquivo;
    data['url'] = url;

    data['tipo_noticia'] = tipoNoticia;
    data['data'] = this.data;
    if (seo != null) {
      data['seo'] = seo!.toJson();
    }
    data['content'] = content;
    data['lido'] = lido;

    return data;
  }
}

class Seo {
  String? metaDescription;
  String? metaTitle;
  String? robots;
  String? type;
  String? locale;
  String? image;

  Seo(
      {this.metaDescription,
      this.metaTitle,
      this.robots,
      this.type,
      this.locale,
      this.image});

  Seo.fromJson(Map<String, dynamic> json) {
    metaDescription = json['meta_description'];
    metaTitle = json['meta_title'];
    robots = json['robots'];
    type = json['type'];
    locale = json['locale'];
    image = json['image'];

    if (image != null) {
      image = image!.replaceFirst('portalhmg', 'www');
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['meta_description'] = metaDescription;
    data['meta_title'] = metaTitle;
    data['robots'] = robots;
    data['type'] = type;
    data['locale'] = locale;
    data['image'] = image;
    return data;
  }
}
