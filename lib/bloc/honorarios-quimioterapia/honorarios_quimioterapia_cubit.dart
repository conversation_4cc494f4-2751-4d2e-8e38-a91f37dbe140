import 'dart:convert';

import 'package:bloc/bloc.dart';
import 'package:cooperado_minha_unimed/models/user.model.dart';
import 'package:cooperado_minha_unimed/shared/api/services.api.dart';
import 'package:cooperado_minha_unimed/shared/locator.dart';
import 'package:cooperado_minha_unimed/shared/utils/file.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';

part 'honorarios_quimioterapia_state.dart';

class HonorarioCubit extends Cubit<ReportHonorarioState> {
  HonorarioCubit() : super(HonorariosInitial());

  bool? exibeCard = false;
  String? crmId;

  Future<bool?> getReportHonorarioCodeVisibility(
      List<Especialidades> specialities) async {
    try {
      final servicesApi = Locator.instance!<ServicesApi>();
      var response = await servicesApi.getReportHonorarioVisible(
          specialities: specialities);
      var result = jsonDecode(response.body);

      if (result['_id'] != null) {
        crmId = result['_id'];
        emit(VisibilityGetHonorariosState(crmId: result['_id']));
        exibeCard = true;
        return exibeCard;
      } else {
        exibeCard = false;
        emit(ErrorGetReportHonorarioState(result['message']));
        return exibeCard;
      }
    } catch (ex) {
      debugPrint(ex.toString());
      emit(ErrorGetReportHonorarioState('$ex'));
      return null;
    }
  }

  getReportHonorarioCodeEvent(
      {DateTime? selectedDateTime, required String crmId}) async {
    emit(LoadingGetReportHonorarioState());
    try {
      final servicesApi = Locator.instance!<ServicesApi>();
      final String? base64 = await servicesApi.getReportHonorariosCode(
          date: selectedDateTime, crmId: crmId);
      final pathFile = await FileUtils.createFileFromString(
          base64String: base64, extension: FileExtension.pdf);

      emit(DoneGetReportHonorarioState(
          "${selectedDateTime!.month}-${selectedDateTime.year}", pathFile));
    } catch (ex) {
      debugPrint(ex.toString());
      emit(ErrorGetReportHonorarioState('$ex'));
    }
  }

  resetState() => emit(HonorariosInitial());
}
