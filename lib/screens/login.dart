import 'package:cooperado_minha_unimed/bloc/auth/auth_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/medicines/medicines-visibility/medicines_visibility_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/medicines/medicines_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/profile/profile_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/update-version/update_version_cubit.dart';
import 'package:cooperado_minha_unimed/colors.dart';
import 'package:cooperado_minha_unimed/models/user.model.dart';
import 'package:cooperado_minha_unimed/screens/forgot-password/main.dart';
import 'package:cooperado_minha_unimed/screens/main/main.dart';
import 'package:cooperado_minha_unimed/screens/register/main.dart';
import 'package:cooperado_minha_unimed/shared/api/auth.api.dart';
import 'package:cooperado_minha_unimed/shared/locator.dart';
import 'package:cooperado_minha_unimed/shared/screen-transitions/fade.transition.dart';
import 'package:cooperado_minha_unimed/shared/utils/logger_print.dart';
import 'package:cooperado_minha_unimed/shared/widgets/alert.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:header_login/header_login.dart';
import 'package:local_auth/local_auth.dart';

const String clientId = String.fromEnvironment('CLIENT_ID');

class LoginScreen extends StatefulWidget {
  const LoginScreen({
    super.key,
    this.openBiometryOnInit = true,
    required this.analytics,
    required this.observer,
  });

  final bool openBiometryOnInit;
  final FirebaseAnalytics analytics;
  final FirebaseAnalyticsObserver observer;

  @override
  LoginScreenState createState() => LoginScreenState();
}

class LoginScreenState extends State<LoginScreen> with WidgetsBindingObserver {
  final logger = UnimedLogger(className: 'LoginPage');
  TextEditingController crmController = TextEditingController();
  TextEditingController passwordController = TextEditingController();
  bool _hidePassword = true;
  bool _isLocalAuth = false;
  final FocusNode _crmFocus = FocusNode();
  final FocusNode _passwordFocus = FocusNode();
  final _formKey = GlobalKey<FormState>();
  bool _canOpenBiometry = true;

  @override
  void initState() {
    super.initState();
    if (widget.openBiometryOnInit) {
      SchedulerBinding.instance.addPostFrameCallback((_) {
        _openBiometry();
      });
    }
    WidgetsBinding.instance.addObserver(this);
    widget.analytics.logScreenView(
      screenName: 'Login',
      screenClass: 'LoginScreen',
    );
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    if (state == AppLifecycleState.inactive) {
      _canOpenBiometry = false;
    } else if (state == AppLifecycleState.resumed) {
      _canOpenBiometry = true;
    }
  }

  @override
  void dispose() {
    _crmFocus.dispose();
    _passwordFocus.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      child: Scaffold(
        backgroundColor: CooperadoColors.backgroundColor,
        body: SingleChildScrollView(
          padding: const EdgeInsets.all(20.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: <Widget>[
              HeaderLogin(
                clientId: clientId,
                innerCircleColor: CooperadoColors.tealGreenDark,
                outerCircleColor: CooperadoColors.tealGreen,
              ),
              _form()
            ],
          ),
        ),
      ),
    );
  }

  Widget _form() {
    return Form(
      key: _formKey,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: <Widget>[
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'CRM',
                  style:
                      TextStyle(color: CooperadoColors.blackText, fontSize: 16),
                ),
                const SizedBox(height: 5),
                TextFormField(
                  textAlign: TextAlign.left,
                  textAlignVertical: TextAlignVertical.top,
                  keyboardType: TextInputType.number,
                  controller: crmController,
                  focusNode: _crmFocus,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Digite o CRM';
                    } else {
                      return null;
                    }
                  },
                  textInputAction: TextInputAction.next,
                  onFieldSubmitted: (term) {
                    FocusScope.of(context).requestFocus(_passwordFocus);
                  },
                  decoration: const InputDecoration(
                    focusedBorder: OutlineInputBorder(
                      borderSide: BorderSide(color: CooperadoColors.tealGreen),
                    ),
                    border: OutlineInputBorder(),
                    contentPadding: EdgeInsets.fromLTRB(20.0, 15.0, 20.0, 15.0),
                  ),
                  style: const TextStyle(color: CooperadoColors.tealGreen),
                ),
              ],
            ),
          ),
          Padding(
            padding: const EdgeInsets.only(top: 10),
            child: Semantics(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Senha',
                    style: TextStyle(
                        color: CooperadoColors.blackText, fontSize: 16),
                  ),
                  const SizedBox(height: 5),
                  TextFormField(
                    enableInteractiveSelection: false,
                    textAlign: TextAlign.left,
                    textAlignVertical: TextAlignVertical.top,
                    controller: passwordController,
                    obscureText: _hidePassword,
                    focusNode: _passwordFocus,
                    textInputAction: TextInputAction.done,
                    autovalidateMode: AutovalidateMode.onUserInteraction,
                    maxLength: 100,
                    onFieldSubmitted: (term) {
                      _pressLogin();
                    },
                    validator: (value) {
                      if (!_isLocalAuth && (value == null || value.isEmpty)) {
                        return 'Digite a senha';
                      }
                      return null;
                    },
                    decoration: InputDecoration(
                      counterText: '',
                      focusedBorder: const OutlineInputBorder(
                        borderSide:
                            BorderSide(color: CooperadoColors.tealGreen),
                      ),
                      border: const OutlineInputBorder(),
                      contentPadding:
                          const EdgeInsets.fromLTRB(20.0, 15.0, 20.0, 15.0),
                      suffixIcon: IconButton(
                        icon: _hidePassword
                            ? const Icon(
                                Icons.visibility_off,
                                color: CooperadoColors.grayDark,
                              )
                            : const Icon(
                                Icons.remove_red_eye,
                                color: CooperadoColors.tealGreen,
                              ),
                        onPressed: () async {
                          setState(() => _hidePassword = !_hidePassword);
                        },
                      ),
                    ),
                    style: const TextStyle(color: CooperadoColors.tealGreen),
                  ),
                ],
              ),
            ),
          ),
          _btnConfirmar(),
        ],
      ),
    );
  }

   Widget _btnConfirmar() {
    return BlocConsumer<AuthCubit, AuthState>(
      listener: (context, state) {
        if (state is ErrorAuthState) {
          showDialog(
            context: context,
            builder: (context) => CooperadoAlertDialog(
              textWidget: Text(
                state.message,
                textAlign: TextAlign.center,
              ),
              onPressed: () {
                Navigator.of(context).pop();
              },
            ),
          );
        } else if (state is LoadedAuthState) {
          context.read<ProfileCubit>().setUser(state.user);
          context.read<UpdateVersionCubit>().checkVersionEvent();
          context.read<MedicinesVisibilityCubit>().getListMedicinesVisibility(
              specialities: state.user.especialidades!);
          context.read<MedicinesCubit>().syncAllMedicines();

          Navigator.pushReplacement(
            context,
            FadeRoute(
              page: HomeScreen(
                analytics: widget.analytics,
                observer: widget.observer,
              ),
            ),
          );
        }
      },
      builder: (context, state) {
        if (state is LoadingAuthState) {
          return const SpinKitThreeBounce(
            color: CooperadoColors.tealGreen,
            size: 20,
          );
        } else {
          return Padding(
            padding: const EdgeInsets.only(bottom: 16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: <Widget>[
                Align(
                  alignment: Alignment.topRight,
                  child: TextButton(
                    onPressed: () {
                      Navigator.push(
                        context,
                        FadeRoute(
                          page: ForgotPasswordScreen(
                            analytics: widget.analytics,
                            observer: widget.observer,
                          ),
                        ),
                      );
                    },
                    child: const Text(
                      "Esqueci minha senha",
                      style: TextStyle(
                        fontSize: 16,
                        color: CooperadoColors.blackText,
                      ),
                    ),
                  ),
                ),
                const SizedBox(height: 20),
                ElevatedButton(
                  style: ElevatedButton.styleFrom(
                    backgroundColor: CooperadoColors.tealGreen,
                    shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8.0),
                        side:
                            const BorderSide(color: CooperadoColors.tealGreen)),
                    padding: const EdgeInsets.symmetric(vertical: 16.0),
                  ),
                  onPressed: _pressLogin,
                  child: const Text("ACESSE"),
                ),
                const SizedBox(height: 20),
                ElevatedButton(
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.white,
                    textStyle: const TextStyle(
                      color: CooperadoColors.tealGreen,
                    ),
                    shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8.0),
                        side:
                            const BorderSide(color: CooperadoColors.tealGreen)),
                    padding: const EdgeInsets.symmetric(vertical: 16.0),
                  ),
                  onPressed: () {
                    Navigator.push(
                      context,
                      FadeRoute(
                        page: RegisterScreen(
                          analytics: widget.analytics,
                          observer: widget.observer,
                        ),
                      ),
                    );
                  },
                  child: const Text(
                    "NOVO CADASTRO",
                    style: TextStyle(
                      fontSize: 14.0,
                      color: CooperadoColors.tealGreen,
                    ),
                  ),
                ),
              ],
            ),
          );
        }
      },
    );
  }

  void _pressLogin() async {
    FocusScope.of(context).requestFocus(FocusNode());

    widget.analytics.setUserId(id: crmController.text);

    if (_formKey.currentState != null) {
      if (_formKey.currentState!.validate()) {
        await context.read<AuthCubit>().autenticate(
              credentials: UserCredentials(
                crm: crmController.text,
                password: passwordController.text,
              ),
            );
        passwordController.clear();

        setState(() {
          _isLocalAuth = false;
        });
      }
    }
  }

  void _openBiometry() async {
    // Verifica se foi iniciado no dart_define
    if (User.isTokenInit()) {
      crmController.text = "INIT_TOKEN";
      passwordController.text = "123456";
      _pressLogin();
    } else {
      final credentials =
          await Locator.instance!.get<AuthApi>().getCredentials();
      if (credentials != null &&
          credentials.crm.isNotEmpty &&
          credentials.password?.isNotEmpty == true) {
        final localAuth = LocalAuthentication();

        bool existBiometrics = await localAuth.canCheckBiometrics;
        logger.i('Permitido usar biometria : $existBiometrics');
        List<BiometricType> availableBiometrics =
            await localAuth.getAvailableBiometrics();

        setState(() {
          _isLocalAuth = true;
        });

        if (existBiometrics && availableBiometrics.isNotEmpty) {
          if (!_canOpenBiometry) return;
          try {
            final valid = await localAuth.authenticate(
              // biometricOnly: true,
              localizedReason: 'Permitir para acessar',
            );
            if (valid) {
              _loginUser(credentials);
            }
          } on PlatformException catch (_) {
            _loginUser(credentials);
          } on Exception catch (_) {
            _loginUser(credentials);
          }
        } else {
          logger.i('sem biometria disponível.');
          _loginUser(credentials);
        }
      }
    }
  }

  void _loginUser(UserCredentials credentials) {
    crmController.text = credentials.crm;
    if (credentials.password != null) {
      passwordController.text = credentials.password!;
    }
    _pressLogin();
  }
}
