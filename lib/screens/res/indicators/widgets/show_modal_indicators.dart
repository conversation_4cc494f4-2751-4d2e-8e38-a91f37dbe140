// ignore_for_file: unused_import, prefer_const_constructors

import 'package:cooperado_minha_unimed/bloc/res/allergies/res_allergies_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/res/configs/res_configs_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/res/indicators/res_indicator_data_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/res/indicators/res_indicator_data_state.dart';
import 'package:cooperado_minha_unimed/bloc/res/indicators/res_indicators_cubit.dart';
import 'package:cooperado_minha_unimed/colors.dart';
import 'package:cooperado_minha_unimed/models/res/res_configs_model.dart';
import 'package:cooperado_minha_unimed/shared/widgets/elevated_button_custom.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:graphql/client.dart';

// ignore: must_be_immutable
class IndicatorsSelection extends StatefulWidget {
  IndicatorsSelection(
      {super.key,
      required this.cpfBeneficiary,
      required this.dateRangeToFilter});
  String cpfBeneficiary = '';
  late DateTimeRange dateRangeToFilter;

  @override
  IndicatorsSelectionState createState() => IndicatorsSelectionState();
}

class IndicatorsSelectionState extends State<IndicatorsSelection> {
  final TextEditingController _controller = TextEditingController();

  bool _selectAll = false;

  List<ResIndicatorModel> listIndicatorsTemp = [];
  List<ResIndicatorModel> resconfigIndicatorsData = [];

  @override
  void initState() {
    super.initState();

    resconfigIndicatorsData =
        context.read<ResConfigCubit>().resConfigModel.indicators;
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: <Widget>[
        GestureDetector(
          onTap: _showIndicatorsSelectionModal,
          child: AbsorbPointer(
            child: TextFormField(
              controller: _controller,
              readOnly: true,
              canRequestFocus: false,
              decoration: InputDecoration(
                hintText: 'Selecione',
                fillColor: Colors.white,
                hintStyle: const TextStyle(
                  color: CooperadoColors.grayLight7,
                ),
                filled: true,
                border: OutlineInputBorder(
                  borderSide: BorderSide(
                    color: CooperadoColors.grayLight3,
                  ),
                ),
                enabledBorder: const OutlineInputBorder(
                  borderSide: BorderSide(
                    color: CooperadoColors.grayLight3,
                  ),
                ),
                focusedBorder: const OutlineInputBorder(
                  borderSide: BorderSide(
                    color: CooperadoColors.grayLight3,
                  ),
                ),
                suffixIcon: IconButton(
                  icon: const Icon(
                    Icons.keyboard_arrow_down,
                    color: CooperadoColors.grayLight3,
                  ),
                  onPressed: _showIndicatorsSelectionModal,
                ),
              ),
            ),
          ),
        ),
        _labelsSelectedsIndicators(),
        const SizedBox(height: 8),
      ],
    );
  }

  void _showIndicatorsSelectionModal() {
    showModalBottomSheet<void>(
      context: context,
      backgroundColor: Colors.white,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(20),
        ),
      ),
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (BuildContext context, StateSetter setModalState) {
            return SafeArea(
              child: Theme(
                data: Theme.of(context).copyWith(
                  unselectedWidgetColor: CooperadoColors.grayLight3,
                ),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: <Widget>[
                    Center(
                      child: Container(
                        height: 4,
                        width: 100,
                        margin: const EdgeInsets.symmetric(
                          vertical: 16,
                        ),
                        decoration: BoxDecoration(
                          color: CooperadoColors.grayLight3,
                          borderRadius: BorderRadius.circular(2),
                        ),
                      ),
                    ),
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 16.0),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          const Text(
                            'Indicadores',
                            style: TextStyle(
                              fontSize: 15,
                              fontWeight: FontWeight.bold,
                              color: CooperadoColors.blackText,
                            ),
                            textAlign: TextAlign.left,
                          ),
                          Checkbox(
                            materialTapTargetSize:
                                MaterialTapTargetSize.shrinkWrap,
                            value: _selectAll,
                            onChanged: (bool? value) {
                              setModalState(() {
                                _selectAll = value ?? false;
                                if (_selectAll) {
                                  context
                                      .read<ResIndsicatorsDataCubit>()
                                      .clearSelectedIndicators();
                                  context
                                      .read<ResIndsicatorsDataCubit>()
                                      .addAllSelectedIndicators(context
                                          .read<ResConfigCubit>()
                                          .resConfigModel
                                          .indicators
                                          .map((e) => e.id.toString())
                                          .toList());
                                } else {
                                  context
                                      .read<ResIndsicatorsDataCubit>()
                                      .clearSelectedIndicators();
                                }
                              });
                            },
                          )
                        ],
                      ),
                    ),
                    const SizedBox(height: 24),
                    Expanded(
                      child: Stack(
                        children: [
                          ListView(
                            padding: EdgeInsets.only(
                              bottom: MediaQuery.of(context).size.height * 0.1,
                            ),
                            children: context
                                .read<ResConfigCubit>()
                                .resConfigModel
                                .indicators
                                .map((indicator) {
                              return Column(
                                children: [
                                  CheckboxListTile(
                                    title: Text(
                                      indicator.description,
                                      style: const TextStyle(
                                        fontSize: 14,
                                        fontWeight: FontWeight.w400,
                                      ),
                                    ),
                                    value: context
                                        .read<ResIndsicatorsDataCubit>()
                                        .containsSelectedIndicator(
                                            indicator.id.toString()),
                                    onChanged: (bool? value) {
                                      setModalState(() {
                                        if (value == true) {
                                          context
                                              .read<ResIndsicatorsDataCubit>()
                                              .addSelectedIndicator(
                                                  indicator.id.toString());
                                        } else {
                                          context
                                              .read<ResIndsicatorsDataCubit>()
                                              .removeSelectedIndicator(
                                                  indicator.id.toString());
                                        }

                                        if (context
                                                .read<ResIndsicatorsDataCubit>()
                                                .selectedIndicators
                                                .length ==
                                            context
                                                .read<ResConfigCubit>()
                                                .resConfigModel
                                                .indicators
                                                .length) {
                                          _selectAll = true;
                                        } else {
                                          _selectAll = false;
                                        }
                                      });
                                    },
                                  ),
                                  const Divider(height: 0),
                                ],
                              );
                            }).toList(),
                          ),
                          Positioned(
                            bottom: 0,
                            left: 0,
                            right: 0,
                            child: Container(
                              color: Colors.white,
                              margin: EdgeInsets.zero,
                              padding: EdgeInsets.zero,
                              child: Padding(
                                padding: const EdgeInsets.only(
                                  left: 14,
                                  right: 14,
                                  bottom: 20,
                                ),
                                child: ElevatedButtonCustom(
                                  onPressed: () {
                                    Navigator.pop(context);

                                    context
                                        .read<ResIndicatorsCubit>()
                                        .searchCategoryIndicator(
                                          indicators: context
                                              .read<ResIndsicatorsDataCubit>()
                                              .selectedIndicators,
                                        );

                                    context
                                        .read<ResIndsicatorsDataCubit>()
                                        .loadResBrazilBeneficiaryIndicatorsData(
                                          cpf: widget.cpfBeneficiary,
                                          indicatorsId: context
                                                  .read<
                                                      ResIndsicatorsDataCubit>()
                                                  .selectedIndicators
                                                  .isEmpty
                                              ? context
                                                  .read<ResIndicatorsCubit>()
                                                  .listIndicators
                                                  .map((e) => e.id.toString())
                                                  .toList()
                                              : context
                                                  .read<
                                                      ResIndsicatorsDataCubit>()
                                                  .selectedIndicators,
                                          dateRange: widget.dateRangeToFilter,
                                        );
                                  },
                                  color: CooperadoColors.greenDark,
                                  title: 'Aplicar',
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            );
          },
        );
      },
    ).whenComplete(() {
      setState(() {});
    });
  }

  Widget _labelsSelectedsIndicators() {
    return Row(
      children: [
        Flexible(
          fit: FlexFit.loose,
          child: Wrap(
            spacing: 4.0,
            clipBehavior: Clip.hardEdge,
            children: context
                    .read<ResIndsicatorsDataCubit>()
                    .selectedIndicators
                    .isEmpty
                ? []
                : context
                    .read<ResIndsicatorsDataCubit>()
                    .selectedIndicators
                    .map((indicator) {
                    return Chip(
                      label: Text(
                        _getSelectedIndicators(
                                indicatorId: indicator, context: context) ??
                            indicator,
                        style: const TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w600,
                          color: CooperadoColors.backgroundWhiteColor,
                        ),
                      ),
                      deleteIcon: const Icon(
                        Icons.close,
                        color: CooperadoColors.backgroundWhiteColor,
                      ),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(2),
                      ),
                      backgroundColor: CooperadoColors.green,
                      onDeleted: () {
                        setState(() {
                          context
                              .read<ResIndsicatorsDataCubit>()
                              .removeSelectedIndicator(indicator);
                          if (context
                              .read<ResIndsicatorsDataCubit>()
                              .selectedIndicators
                              .isEmpty) {
                            _selectAll = false;
                          }
                        });
                        context
                            .read<ResIndsicatorsDataCubit>()
                            .loadResBrazilBeneficiaryIndicatorsData(
                              cpf: widget.cpfBeneficiary,
                              indicatorsId: context
                                      .read<ResIndsicatorsDataCubit>()
                                      .selectedIndicators
                                      .isEmpty
                                  ? context
                                      .read<ResIndicatorsCubit>()
                                      .listIndicators
                                      .map((e) => e.id.toString())
                                      .toList()
                                  : context
                                      .read<ResIndsicatorsDataCubit>()
                                      .selectedIndicators,
                              dateRange: widget.dateRangeToFilter,
                            );
                      },
                    );
                  }).toList(),
          ),
        ),
      ],
    );
  }

  String? _getSelectedIndicators(
      {required BuildContext context, required String indicatorId}) {
    var listIndicators =
        context.read<ResConfigCubit>().resConfigModel.indicators;

    if (listIndicators.isNotEmpty) {
      final String indicators = context
          .read<ResConfigCubit>()
          .resConfigModel
          .indicators
          .firstWhere((element) => element.id.toString() == indicatorId)
          .description;
      return indicators;
    } else {
      return '';
    }
  }
}
