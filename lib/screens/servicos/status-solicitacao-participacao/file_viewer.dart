// ignore_for_file: use_build_context_synchronously

import 'dart:io';

import 'package:cooperado_minha_unimed/bloc/servicos/status-solicitacao-participacao/add_file/add_file_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/servicos/status-solicitacao-participacao/add_file/add_file_state.dart';
import 'package:cooperado_minha_unimed/colors.dart';
import 'package:cooperado_minha_unimed/models/glosa_resource/file-attach.model.dart';
import 'package:cooperado_minha_unimed/screens/servicos/status-solicitacao-participacao/file_preview.dart';
import 'package:cooperado_minha_unimed/shared/locator.dart';
import 'package:cooperado_minha_unimed/shared/utils/file.dart';
import 'package:cooperado_minha_unimed/shared/utils/handle_camera.dart';
import 'package:cooperado_minha_unimed/shared/utils/logger_print.dart';
import 'package:cooperado_minha_unimed/shared/widgets/alert.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:remote_log_elastic/remote_log_elastic.dart';

final logger = UnimedLogger(className: 'FileViewer');

class FileViewer extends StatefulWidget {
  final FileAttach? fileAttach;
  final int index;

  @override
  FileViewerState createState() => FileViewerState();

  const FileViewer({super.key, required this.fileAttach, required this.index});
}

class FileViewerState extends State<FileViewer> with WidgetsBindingObserver {
  bool loading = false;
  bool? sended;
  String? message;

  bool enableAddFilesButtons = true;
  late String filetype;
  late List<String> fileParts;
  late FileAttach? fileAttach;

  bool _requestingPermission = false;

  @override
  void initState() {
    sended = widget.fileAttach?.sended;
    super.initState();
    WidgetsBinding.instance.addObserver(this);
  }

  @override
  void dispose() {
    super.dispose();
    WidgetsBinding.instance.removeObserver(this);
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    if (state == AppLifecycleState.resumed) {
      if (_requestingPermission) {
        _requestingPermission = false;
        _onPressFile();
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    fileAttach = widget.fileAttach;
    fileParts = (widget.fileAttach?.file.path ?? "").split('.');
    filetype = fileParts.last.toLowerCase();

    return BlocListener<GlosaResourceAddFileCubit, GlosaResourceAddFileState>(
        listener: (context, state) {
          if (state is LoadingDocumentState) {
            setState(() {
              sended = widget.index == state.index ? null : sended;
              loading = state.index == widget.index;
              message = state.message;
            });
          } else if (state is FileSendedState && sended != true) {
            setState(() {
              sended = widget.index == state.index ? true : sended;
              loading = false;
            });
          } else if (state is ErrorSendedState) {
            setState(() {
              sended = widget.index == state.index ? false : sended;
              loading = false;
            });
          } else if (state is DoneState) {
            setState(() {
              if (state.attachments.length > widget.index) {
                sended = state.attachments[widget.index]?.sended;
              }

              loading = false;
            });
          } else {
            setState(() {
              loading = false;
            });
          }
        },
        child: Container(
            margin: EdgeInsets.symmetric(
                vertical: MediaQuery.of(context).size.height * 0.1,
                horizontal: 8.0),
            child: Card(
                child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: fileAttach != null
                        ? <Widget>[
                            if (fileAttach?.name != null)
                              Padding(
                                  padding:
                                      const EdgeInsets.symmetric(vertical: 8),
                                  child: Text(
                                    fileAttach?.name ?? "",
                                    style: const TextStyle(fontSize: 16),
                                  )),
                            Expanded(
                              child: InkWell(
                                onTap: () {
                                  if (!loading) {
                                    Navigator.push(
                                        context,
                                        MaterialPageRoute(
                                          builder: (context) =>
                                              PreviewFileScreen(
                                            title: 'Arquivo',
                                            file: fileAttach!.file,
                                            fileName: fileAttach!.name,
                                            fileType: filetype,
                                          ),
                                        ));
                                  }
                                },
                                child: Padding(
                                  padding: const EdgeInsets.all(5),
                                  child: ClipRRect(
                                    borderRadius: BorderRadius.circular(8.0),
                                    child: _viewFile(fileAttach, filetype),
                                  ),
                                ),
                              ),
                            ),
                            if (!loading) _footer(filetype)
                          ]
                        : <Widget>[
                            Expanded(
                                child: loading
                                    ? Container(
                                        color: Theme.of(context)
                                            .hintColor
                                            .withOpacity(.55),
                                        child: Column(
                                          children: [
                                            const Expanded(child: SizedBox()),
                                            const SpinKitThreeBounce(
                                              color: Colors.white,
                                              size: 20,
                                            ),
                                            Expanded(
                                              child: Center(
                                                  child: Text(
                                                message ?? '',
                                                style: const TextStyle(
                                                    color: Colors.white),
                                              )),
                                            ),
                                          ],
                                        ),
                                      )
                                    : Column(
                                        children: [
                                          Expanded(
                                            child: Column(
                                              children: [
                                                loading
                                                    ? const SpinKitThreeBounce(
                                                        color: Colors.white,
                                                        size: 20,
                                                      )
                                                    : Expanded(
                                                        child: Row(
                                                          children: [
                                                            Expanded(
                                                              child: InkWell(
                                                                onTap: () {
                                                                  if (enableAddFilesButtons) {
                                                                    _openImagesModal(
                                                                        context);
                                                                  }
                                                                },
                                                                child:
                                                                    const Column(
                                                                  mainAxisAlignment:
                                                                      MainAxisAlignment
                                                                          .center,
                                                                  children: [
                                                                    Icon(
                                                                      Icons
                                                                          .note_add,
                                                                      size: 56,
                                                                      color: CooperadoColors
                                                                          .grayDark,
                                                                    ),
                                                                    SizedBox(
                                                                      height:
                                                                          10,
                                                                    ),
                                                                    Text(
                                                                      'Clique para adicionar arquivo',
                                                                      style: TextStyle(
                                                                          color:
                                                                              CooperadoColors.grayDark),
                                                                    ),
                                                                  ],
                                                                ),
                                                              ),
                                                            ),
                                                          ],
                                                        ),
                                                      ),
                                              ],
                                            ),
                                          ),
                                        ],
                                      ))
                          ]))));
  }

  Widget _viewFile(FileAttach? file, String fileType) {
    if (fileType == 'pdf') {
      return Stack(fit: StackFit.expand, children: [
        Column(children: [
          const Expanded(child: Icon(Icons.picture_as_pdf_outlined, size: 56)),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 8.0),
            child: Text(file?.name ?? "Arquivo"),
          )
        ]),
        if (loading)
          Container(
              color: Theme.of(context).hintColor.withOpacity(.55),
              child: Column(children: [
                const Expanded(child: SizedBox()),
                const SpinKitThreeBounce(
                  color: Colors.white,
                  size: 20,
                ),
                Expanded(
                  child: Center(
                      child: Text(
                    message ?? '',
                    style: const TextStyle(color: Colors.white),
                  )),
                )
              ])),
        _sendedLabel()
      ]);
    } else {
      return Stack(
        fit: StackFit.expand,
        children: [
          Image.file(
            file?.thumbnail ?? file!.file,
            fit: BoxFit.cover,
            filterQuality: FilterQuality.low,
          ),
          Container(
            color: Theme.of(context).hintColor.withOpacity(.55),
            child: Column(
              children: [
                const Expanded(child: SizedBox()),
                loading
                    ? const SpinKitThreeBounce(
                        color: Colors.white,
                        size: 20,
                      )
                    : const Expanded(
                        child: Icon(
                          Icons.image,
                          size: 56,
                          color: Colors.white,
                        ),
                      ),
                Expanded(
                  child: Center(
                    child: Text(
                      loading ? message ?? "" : 'Clique para visualizar',
                      style: const TextStyle(color: Colors.white),
                    ),
                  ),
                ),
              ],
            ),
          ),
          _sendedLabel()
        ],
      );
    }
  }

  showRemoveDocumentAlert() {
    Alert.open(
      context,
      text:
          "I18nHelper.translate(context, 'checkinSurgery.documents.remove.text')",
      title: "'checkinSurgery.documents.remove.title')",
      actions: <Widget>[
        ElevatedButton(
          style: ElevatedButton.styleFrom(
            foregroundColor: Colors.white,
            backgroundColor: unimedGreen,
          ),
          child: const Text('checkinSurgery.documents.remove.confirm'),
          onPressed: () => {
            BlocProvider.of<GlosaResourceAddFileCubit>(context)
                .removeFile(index: widget.index),
            Navigator.pop(context)
          },
        )
      ],
    );
  }

  _openImagesModal(
    BuildContext context,
  ) {
    showModalBottomSheet<void>(
        context: context,
        builder: (BuildContext context) {
          return SafeArea(
              child: Container(
                  padding: const EdgeInsets.symmetric(
                      horizontal: 24.0, vertical: 16.0),
                  color: Colors.white,
                  child:
                      Column(mainAxisSize: MainAxisSize.min, children: <Widget>[
                    InkWell(
                        onTap: () {
                          Navigator.of(context).pop();
                          _onPressCamera();
                        },
                        child: const Padding(
                          padding: EdgeInsets.all(12),
                          child: Row(
                            children: [
                              Icon(
                                Icons.camera_alt,
                                color: CooperadoColors.green,
                              ),
                              SizedBox(
                                width: 10,
                              ),
                              Text(
                                "Camera",
                                style: TextStyle(
                                  color: CooperadoColors.green,
                                ),
                              )
                            ],
                          ),
                        )),
                    const Divider(
                      height: 5,
                    ),
                    InkWell(
                        onTap: () {
                          Navigator.of(context).pop();
                          _onPressFile();
                        },
                        child: const Padding(
                          padding: EdgeInsets.all(12),
                          child: Row(
                            children: [
                              Icon(
                                Icons.file_copy,
                                color: CooperadoColors.green,
                              ),
                              SizedBox(
                                width: 10,
                              ),
                              Text(
                                "Arquivo",
                                style: TextStyle(
                                  color: CooperadoColors.green,
                                ),
                              )
                            ],
                          ),
                        )),
                  ])));
        });
  }

  _onPressCamera() async {
    if (enableAddFilesButtons) {
      try {
        enableAddFilesButtons = false;

        final cameraStatus = await Permission.camera.status;

        if (cameraStatus == PermissionStatus.granted) {
          HandleCamera.openCameraScreen(context, _onTakePhoto);
        } else {
          HandleCamera.requestCameraPermission(context);
        }
      } catch (e) {
        logger.e('Error on onPressCamera on Solicitations $e');
      } finally {
        enableAddFilesButtons = true;
      }
    }
  }

  _onTakePhoto(File? file, BuildContext context) async {
    if (file == null) {
      BlocProvider.of<GlosaResourceAddFileCubit>(context).stopAttachFile();
    } else {
      BlocProvider.of<GlosaResourceAddFileCubit>(context)
          .attachFile(file: file, index: widget.index);
    }
  }

  _onPressFile() async {
    try {
      final infoJson =
          await Locator.instance!.get<RemoteLog>().deviceInfo?.toJson();
      final androidVersion = int.parse(infoJson?['version.release'] ?? '0');

      final dataAccesStatus = Platform.isAndroid
          ? androidVersion < 13
              ? await Permission.storage.status
              : null
          : await Permission.photos.status;

      setState(() => enableAddFilesButtons = false);

      debugPrint(
          'Status de acesso aos dados: $dataAccesStatus, Android Version: $androidVersion');

      if (dataAccesStatus == PermissionStatus.granted ||
          dataAccesStatus == PermissionStatus.limited ||
          androidVersion >= 13) {
        final FilePickerResult? filePickerResult =
            await FilePicker.platform.pickFiles(
          type: FileType.custom,
          allowMultiple: false,
          allowedExtensions: BlocProvider.of<GlosaResourceAddFileCubit>(context)
              .formatFilesAllowed,
        );

        if (filePickerResult?.files.first == null ||
            filePickerResult!.files.isEmpty) {
          BlocProvider.of<GlosaResourceAddFileCubit>(context).stopAttachFile();
        } else {
          final PlatformFile filePicked = filePickerResult.files.first;
          final File file =
              await FileUtils.copyFileOnDocuments(File(filePicked.path!));

          BlocProvider.of<GlosaResourceAddFileCubit>(context)
              .attachFile(file: file, index: widget.index);
        }
      } else {
        _alertPermissionDataAcces(dataAccesStatus);
      }
    } catch (e) {
      logger.e('Error on onPressFile on Solicitations $e');

      BlocProvider.of<GlosaResourceAddFileCubit>(context).stopAttachFile();
      Alert.open(
        context,
        title: 'Erro',
        text: 'Erro ao abrir o arquivo',
        textButtonClose: 'Fechar',
        callbackClose: () {
          setState(() => enableAddFilesButtons = true);
        },
      );
    } finally {
      setState(() => enableAddFilesButtons = true);
    }
  }

  // _alertPermissionDataAcces(dataAccesStatus) {
  //   bool canClick = true;
  //   Alert.open(
  //     context,
  //     title: 'Acesso aos dados',
  //     text: 'Precisamos acessar seus arquivos para anexar o documento',
  //     textButtonClose: 'Fechar',
  //     callbackClose: () {
  //       setState(() => enableAddFilesButtons = true);
  //     },
  //     actions: <Widget>[
  //       ElevatedButton(
  //         style: ElevatedButton.styleFrom(
  //           foregroundColor: Colors.white,
  //           backgroundColor: CooperadoColors.orange,
  //           shape: RoundedRectangleBorder(
  //               borderRadius: BorderRadius.circular(5.0)),
  //         ),
  //         child: const Text('Autorizar'),
  //         onPressed: () async {
  //           if (canClick) {
  //             setState(() {
  //               canClick = false;
  //             });

  //             if (dataAccesStatus == PermissionStatus.denied) {
  //               final infoJson = await Locator.instance!
  //                   .get<RemoteLog>()
  //                   .deviceInfo
  //                   ?.toJson();
  //               final androidVersion =
  //                   int.parse(infoJson?['version.release'] ?? '0');

  //               Platform.isAndroid
  //                   ? androidVersion < 13
  //                       ? Permission.storage.request().then((_) {
  //                           Navigator.of(context).pop();
  //                         })
  //                       : Permission.photos.request().then((_) {
  //                           Navigator.of(context).pop();
  //                         })
  //                   : Permission.photos.request().then((_) {
  //                       Navigator.of(context).pop();
  //                     });
  //             } else {
  //               Navigator.of(context).pop();
  //               openAppSettings();
  //             }
  //             setState(() => enableAddFilesButtons = true);
  //           }
  //         },
  //       ),
  //     ],
  //   );
  // }
  _alertPermissionDataAcces(dataAccesStatus) {
    bool canClick = true;
    Alert.open(
      context,
      title: 'Acesso aos dados',
      text: 'Precisamos acessar seus arquivos para anexar o documento.',
      textButtonClose: 'Fechar',
      callbackClose: () {
        setState(() => enableAddFilesButtons = true);
      },
      actions: <Widget>[
        ElevatedButton(
          style: ElevatedButton.styleFrom(
            foregroundColor: Colors.white,
            backgroundColor: CooperadoColors.tealGreen,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(5.0),
            ),
          ),
          child: Text('Autorizar'),
          onPressed: () async {
            if (canClick) {
              setState(() {
                canClick = false;
              });
              debugPrint('Status de acesso aos dados: $dataAccesStatus');
              if (dataAccesStatus == PermissionStatus.denied) {
                await _requestPermission();
              } else {
                Navigator.of(context).pop();
                _requestingPermission = true;
                debugPrint('openAppSettings');
                openAppSettings();
              }

              setState(() => enableAddFilesButtons = true);
            }
          },
        ),
      ],
    );
  }

  Future<void> _requestPermission() async {
    try {
      final infoJson =
          await Locator.instance!.get<RemoteLog>().deviceInfo?.toJson();
      final androidVersion = int.parse(infoJson?['version.release'] ?? '0');

      Platform.isAndroid
          ? androidVersion < 13
              ? Permission.storage.request().then((PermissionStatus status) {
                  debugPrint('Permissão storage $androidVersion $status');
                  _handlePermissionStatusResponse(status);
                })
              : null
          : Permission.photos.request().then((PermissionStatus status) {
              debugPrint('Permissão photos $status');
              if (status == PermissionStatus.permanentlyDenied) {
                Navigator.of(context).pop();
                openAppSettings();
              } else {
                Navigator.of(context).pop();
              }
            });
    } catch (e) {
      debugPrint('erro: $e');
    }
  }

  void _handlePermissionStatusResponse(PermissionStatus status) {
    if (status == PermissionStatus.granted ||
        status == PermissionStatus.limited) {
      Navigator.of(context).pop();
      _onPressFile();
    } else {
      Navigator.of(context).pop();
      _alertPermissionDenied(status);
    }
  }

  _alertPermissionDenied(dataAccesStatus) {
    bool canClick = true;
    Alert.open(
      context,
      title: 'Permissão não concedida',
      text:
          'Não será possível acessar seus arquivos para anexar o documento. Tente novamente para permitir o acesso.',
      textButtonClose: 'Fechar',
      callbackClose: () {
        setState(() => enableAddFilesButtons = true);
      },
      actions: <Widget>[
        ElevatedButton(
          style: ElevatedButton.styleFrom(
            foregroundColor: Colors.white,
            backgroundColor: CooperadoColors.tealGreen,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(5.0),
            ),
          ),
          child: Text('Tentar novamente'),
          onPressed: () async {
            if (canClick) {
              setState(() {
                canClick = false;
              });

              if (dataAccesStatus == PermissionStatus.denied) {
                await _requestPermission();
              } else {
                Navigator.of(context).pop();
                openAppSettings();
              }

              setState(() => enableAddFilesButtons = true);
            }
          },
        ),
      ],
    );
  }

  Widget _footer(fileType) {
    return !(fileAttach?.sended ?? false)
        ? Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: <Widget>[
              IconButton(
                icon: const Icon(Icons.delete),
                color: Colors.red,
                onPressed: () {
                  BlocProvider.of<GlosaResourceAddFileCubit>(context)
                      .removeFile(index: widget.index);
                },
              ),
            ],
          )
        : Container();
  }

  bool getStatusBoolean(bool sended, String? status) {
    if (status != null) {
      return status == "S";
    } else {
      return sended;
    }
  }

  String getStatusLabel(bool sended) {
    return sended ? 'Enviado' : 'Não enviado';
  }

  Color getStatusColor(bool sended) {
    return sended ? CooperadoColors.green : CooperadoColors.redCancel;
  }

  _sendedLabel() {
    return sended == null
        ? Container()
        : Padding(
            padding: const EdgeInsets.all(5),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                Container(
                    padding: const EdgeInsets.only(left: 5, top: 5, bottom: 5),
                    decoration: const BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(8),
                        bottomLeft: Radius.circular(8),
                      ),
                    ),
                    child: Text(
                      getStatusLabel(sended!),
                      style: TextStyle(color: getStatusColor(sended!)),
                    )),
                Container(
                    padding: const EdgeInsets.only(right: 5, top: 5, bottom: 5),
                    decoration: const BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.only(
                        topRight: Radius.circular(8),
                        bottomRight: Radius.circular(8),
                      ),
                    ),
                    child: Icon(
                      sended! ? Icons.check_circle : Icons.close,
                      color: getStatusColor(sended!),
                      size: 18,
                    )),
              ],
            ));
  }
}
