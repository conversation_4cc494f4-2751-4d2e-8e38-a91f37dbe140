class RetornoDiretoria {
  List<Conselhos>? conselhos;
  List<Diretores>? diretores;

  RetornoDiretoria({this.conselhos, this.diretores});

  RetornoDiretoria.fromJson(Map<String, dynamic> json) {
    if (json['conselhos'] != null) {
      conselhos = [];
      json['conselhos'].forEach((v) {
        conselhos!.add(Conselhos.fromJson(v));
      });
    }
    if (json['diretores'] != null) {
      diretores = [];
      json['diretores'].forEach((v) {
        diretores!.add(Diretores.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    if (conselhos != null) {
      data['conselhos'] = conselhos!.map((v) => v.toJson()).toList();
    }
    if (diretores != null) {
      data['diretores'] = diretores!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class Conselhos {
  String? nome;
  List<Membros>? membros;

  Conselhos({this.nome, this.membros});

  Conselhos.fromJson(Map<String, dynamic> json) {
    nome = json['nome'];
    if (json['membros'] != null) {
      membros = [];
      json['membros'].forEach((v) {
        membros!.add(Membros.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['nome'] = nome;
    if (membros != null) {
      data['membros'] = membros!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class Membros {
  String? nome;

  Membros({this.nome});

  Membros.fromJson(Map<String, dynamic> json) {
    nome = json['nome'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['nome'] = nome;
    return data;
  }
}

class Diretores {
  String? nome;
  String? cargo;
  String? crm;
  String? especialidade;
  String? imagem;
  String? thumb;

  Diretores(
      {this.nome,
      this.cargo,
      this.crm,
      this.especialidade,
      this.imagem,
      this.thumb});

  Diretores.fromJson(Map<String, dynamic> json) {
    nome = json['nome'];
    cargo = json['cargo'];
    crm = json['crm'];
    especialidade = json['especialidade'];
    imagem = json['imagem'];
    thumb = json['thumb'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['nome'] = nome;
    data['cargo'] = cargo;
    data['crm'] = crm;
    data['especialidade'] = especialidade;
    data['imagem'] = imagem;
    data['thumb'] = thumb;
    return data;
  }
}
