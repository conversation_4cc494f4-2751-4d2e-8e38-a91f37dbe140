// ignore_for_file: public_member_api_docs, sort_constructors_first, must_be_immutable, library_private_types_in_public_api

import 'package:cooperado_minha_unimed/bloc/sensitive-data/sensitive_data_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/sensitive-data/sensitive_data_state.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:flutter/material.dart';

import 'package:cooperado_minha_unimed/shared/utils/graph_utils.dart';
import 'package:cooperado_minha_unimed/shared/vo/transparencia/indices.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import 'widgets/transparency_bardata.dart';

class TransparencyBarChart extends StatefulWidget {
  int dataLimit;
  final bool? animate;
  final List<VODataModel>? data;
  final List<Color>? colors;
  final Function? action;
  final Orientation orientation;

  TransparencyBarChart({
    super.key,
    required this.dataLimit,
    this.animate,
    this.data,
    this.colors,
    this.action,
    required this.orientation,
  });

  @override
  _TransparencyBarChartState createState() => _TransparencyBarChartState();
}

class _TransparencyBarChartState extends State<TransparencyBarChart> {
  List<double> allValues = [];
  Color toolTipBGColor = Colors.blueGrey;
  List<BarChartGroupData> dataGroupData = [];

  double completeNumbers(double input) {
    double result = 0.0;

    if (input % 20 == 0) {
      result = input;
    } else {
      double completeValue = 20 - (input % 20);
      result = input + completeValue;
    }
    return result;
  }

  Widget bottomTitleWidgets(double value, TitleMeta meta) {
    List<String> dates = [];
    for (int i = 0; i < (widget.data ?? []).length; i++) {
      dates.add(
          '${(widget.data?[i].referenceMonthLabel ?? '   ').substring(0, 3)}/${(widget.data?[i].referenceYear ?? '')}');
    }

    int dataLength = dates.length;
    int itemCount = dataLength > widget.dataLimit ? widget.dataLimit : dataLength;

    List<String> selectedDates = dataLength > itemCount
        ? dates.sublist(dataLength - itemCount, dataLength)
        : dates;

    if (value.toInt() < selectedDates.length) {
      return SideTitleWidget(
        axisSide: meta.axisSide,
        child: Transform.rotate(
          angle: -0.5,
          child: SizedBox(
            width: 80,
            child: Text(
              selectedDates.length > value.toInt()
                  ? selectedDates[value.toInt()]
                  : '',
              style: const TextStyle(
                fontSize: 11,
                fontWeight: FontWeight.bold
              ),
            ),
          ),
        ),
      );
    } else {
      return SideTitleWidget(
        axisSide: meta.axisSide,
        child: const Text('---', style: TextStyle(fontSize: 13)),
      );
    }
  }

  List<TransparencyBarData> _createSampleData() {
    List<Indice> mensal = [];
    List<Indice> acumulado = [];
    List<Indice> projetado = [];
    List<TransparencyBarData> listTransparency = [];

    int dataLimit = widget.orientation == Orientation.portrait ? 6 : 12;

    Color getColor(Color color) {
      return Color.fromARGB(
        color.alpha,
        color.red,
        color.green,
        color.blue,
      );
    }

    List<VODataModel> limitedData;
    if (widget.orientation == Orientation.portrait) {
      int startIndex = widget.data!.length - dataLimit;
      limitedData = widget.data!.sublist(startIndex >= 0 ? startIndex : 0);
    } else {
      limitedData = widget.data!.take(dataLimit).toList();
    }

    for (var d in limitedData) {
      TransparencyBarData transp = TransparencyBarData(
          mensal: 0, acumulado: 0, projetado: 0, colors: []);

      mensal.add(Indice(d.referenceMonthLabel!.substring(0, 3), d.monthValue));
      transp.mensal = d.monthValue!;
      transp.colors.add(getColor(widget.colors![0]));
      allValues.add(d.monthValue!);

      if (d.totalValue != null) {
        acumulado
            .add(Indice(d.referenceMonthLabel!.substring(0, 3), d.totalValue));
        transp.acumulado = d.totalValue!;
        transp.colors.add(getColor(widget.colors![1]));
        allValues.add(d.totalValue!);
      }

      if (d.projectedValue != null) {
        projetado.add(
            Indice(d.referenceMonthLabel!.substring(0, 3), d.projectedValue));
        transp.projetado = d.projectedValue!;
        transp.colors.add(getColor(widget.colors![2]));
        allValues.add(d.projectedValue!);
      }

      listTransparency.add(transp);
    }

    return listTransparency;
  }

  List<BarChartGroupData> _mountGroupdata() {
    List<TransparencyBarData> dataTransparency;
    dataTransparency = _createSampleData();

    for (var element in dataTransparency) {
      element.initializeBarData();
    }

    List<BarChartGroupData> listBarChartGroupData = [];

    int dataLimit = widget.orientation == Orientation.portrait ? 6 : 12;
    int dataLength = dataTransparency.length;
    int itemCount = dataLength > dataLimit ? dataLimit : dataLength;

    List<TransparencyBarData> selectedData =
        dataTransparency.sublist(dataLength - itemCount, dataLength);

    for (int i = 0; i < selectedData.length; i++) {
      listBarChartGroupData.add(
        BarChartGroupData(
          x: i, //individualbar.x,

          barRods: [
            BarChartRodData(
                toY: dataTransparency[i].mensal,
                color: dataTransparency[i].colors[0],
                width: 6,
                borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(3), topRight: Radius.circular(3))),
            BarChartRodData(
                toY: dataTransparency[i].acumulado,
                color: dataTransparency[i].colors[1],
                width: 6,
                borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(3), topRight: Radius.circular(3))),
            BarChartRodData(
                toY: dataTransparency[i].projetado,
                color: dataTransparency[i].colors[2],
                width: 6,
                borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(3), topRight: Radius.circular(3))),
          ],
        ),
      );
    }

    return listBarChartGroupData;
  }

  @override
  void initState() {
    super.initState();
    dataGroupData = _mountGroupdata();
  }

  @override
  void dispose() {
    SystemChrome.setPreferredOrientations([DeviceOrientation.portraitUp]);
    super.dispose();
  }

  @override
 Widget build(BuildContext context) {
  dataGroupData = _mountGroupdata();
  return BlocBuilder<SensitiveDataCubit, SensitiveDataState>(
    builder: (context, sensitiveState) {
      bool isSensitiveDataVisible = sensitiveState.isSensitiveDataVisible;
      return Padding(
        padding: const EdgeInsets.symmetric(vertical: 3),
        child: BarChart(
          BarChartData(
            groupsSpace: 1,
            barTouchData: BarTouchData(
              touchCallback: (p0, p1) {
                if (p1?.spot?.touchedRodData.color != null) {
                  setState(() {
                    toolTipBGColor = p1!.spot!.touchedRodData.color!;
                  });
                }
              },
              allowTouchBarBackDraw: false,
              touchTooltipData: BarTouchTooltipData(
                tooltipHorizontalOffset: -40,
                tooltipBgColor: toolTipBGColor,
                tooltipHorizontalAlignment: FLHorizontalAlignment.right,
                tooltipMargin: -50,
                getTooltipItem: (group, groupIndex, rod, rodIndex) {
                  return BarTooltipItem(
                    '${rod.toY}%',
                    const TextStyle(
                      color: Colors.black,
                      fontWeight: FontWeight.bold,
                    ),
                  );
                },
              ),
            ),
            maxY: completeNumbers(GraphUtils.findMax(allValues)),
            minY: 0,
            gridData: FlGridData(
              show: true,
              drawHorizontalLine: true,
              drawVerticalLine: false,
              horizontalInterval: 20,
              verticalInterval: 5,
              getDrawingHorizontalLine: (value) {
                return FlLine(
                  color: Colors.grey.shade500,
                  strokeWidth: 0.8,
                );
              },
            ),
            titlesData: FlTitlesData(
              show: true,
              topTitles: const AxisTitles(
                sideTitles: SideTitles(showTitles: false),
              ),
              rightTitles: const AxisTitles(
                sideTitles: SideTitles(showTitles: false),
              ),
              leftTitles: AxisTitles(
                sideTitles: SideTitles(
                  interval: 20,
                  reservedSize: 44,
                  showTitles: isSensitiveDataVisible,
                ),
              ),
              bottomTitles: AxisTitles(
                sideTitles: SideTitles(
                  showTitles: true,
                  getTitlesWidget: bottomTitleWidgets,
                ),
              ),
            ),
            borderData: FlBorderData(
              show: true,
              border: const Border(
                top: BorderSide(color: Colors.grey),
                bottom: BorderSide(color: Colors.grey),
              ),
            ),
            barGroups: dataGroupData,
          ),
        ),
      );
    },
  );
}
}

class Indice {
  final String mes;
  final double? valor;

  Indice(this.mes, this.valor);
}
