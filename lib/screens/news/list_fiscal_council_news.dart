import 'dart:async';

import 'package:cooperado_minha_unimed/bloc/news/council/council_cubit.dart';
import 'package:cooperado_minha_unimed/colors.dart';
import 'package:cooperado_minha_unimed/screens/news/card_news.dart';
import 'package:cooperado_minha_unimed/screens/news/card_other_news.dart';
import 'package:cooperado_minha_unimed/shared/vo/transparencia/demostrativo_resultado.dart';
import 'package:cooperado_minha_unimed/shared/widgets/error-banner/error_banner.component.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';

class ListFiscalCouncilNews extends StatefulWidget {
  final List<String> categories;
  final TextEditingController searchText;

  const ListFiscalCouncilNews(
      {super.key, required this.categories, required this.searchText});
  @override
  ListFiscalCouncilNewsState createState() => ListFiscalCouncilNewsState();
}

class ListFiscalCouncilNewsState extends State<ListFiscalCouncilNews> {
  Timer? _debounce;
  String textOld = '';

  int page = 1;
  ScrollController controller = ScrollController();

  listenerSearch() {
    if (textOld != widget.searchText.text) {
      if (_debounce?.isActive ?? false) _debounce!.cancel();
      _debounce = Timer(const Duration(milliseconds: 500), () {
        context.read<CouncilCubit>().getListNewsSearch()(
            search: widget.searchText.text);
        setState(() {
          textOld = widget.searchText.text;
        });
      });
    }
  }

  @override
  void initState() {
    context
        .read<CouncilCubit>()
        .getListNewsEventPagination(categories: widget.categories, page: page);

    widget.searchText.addListener(listenerSearch);
    controller.addListener(() {
      if (controller.position.pixels == controller.position.maxScrollExtent) {
        setState(() {
          page++;
        });
        _updateListNoticias();
      }
    });
    super.initState();
  }

  @override
  void dispose() {
    _debounce?.cancel();

    widget.searchText.removeListener(listenerSearch);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return _loadBloc();
  }

  Widget _loadBloc() {
    return BlocBuilder<CouncilCubit, CouncilState>(
      builder: (context, state) {
        if (state is DoneFiscalCouncilNewsState) {
          return Column(
            children: [
              Expanded(child: _listNoticias(state.list)),
            ],
          );
        } else if (state is ErrorFiscalCouncilNewsState) {
          return Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              ErrorBanner(message: state.message),
            ],
          );
        } else if (state is LoadingFiscalCouncilNewsStatePagination) {
          return Column(
            children: [
              Expanded(child: _listNoticias(state.list)),
              const SpinKitCircle(color: CooperadoColors.tealGreen),
            ],
          );
        } else if (state is LoadingFiscalCouncilNewsState) {
          return const SpinKitCircle(color: CooperadoColors.tealGreen);
        } else {
          return Container();
        }
      },
    );
  }

  Widget _listNoticias(List<Noticia> noticias) {
    double height = MediaQuery.of(context).size.height * 0.21;
    double width = MediaQuery.of(context).size.width * 0.545;
    return Padding(
      padding: const EdgeInsets.all(5),
      child: SizedBox(
        height: height,
        width: MediaQuery.of(context).size.width,
        child: ListView.builder(
            controller: controller,
            shrinkWrap: true,
            itemCount: noticias.length,
            itemBuilder: (context, index) {
              final item = noticias.elementAt(index);
              return index == 0
                  ? CardNews(
                      noticia: item,
                      height: height,
                      width: width,
                    )
                  : CardOtherNews(
                      noticiasPortal: item,
                    );
            }),
      ),
    );
  }

  _updateListNoticias() async {
    if (page <= 5 && widget.searchText.text.isEmpty) {
      context.read<CouncilCubit>().getListNewsEventPagination(
            categories: widget.categories,
            page: page,
          );
    }
  }
}
