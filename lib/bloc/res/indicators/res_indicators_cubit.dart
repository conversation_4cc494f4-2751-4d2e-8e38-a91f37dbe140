import 'package:cooperado_minha_unimed/models/res/res_configs_model.dart';
import 'package:cooperado_minha_unimed/shared/api/graphql-res.api.dart';
import 'package:cooperado_minha_unimed/shared/locator.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

part 'res_indicators_state.dart';

class ResIndicatorsCubit extends Cubit<ResIndicatorsState> {
  ResIndicatorsCubit() : super(InitialResIndicatorState());

  List<ResIndicatorModel> _listIndicators = List.empty(growable: true);
  List<ResIndicatorModel> get listIndicators => _listIndicators;

  void getResBrazilBeneficiaryIndicators({
    required String cpf,
  }) async {
    try {
      emit(LoadingResAllIndicatorsState());
      _listIndicators = await Locator.instance!<ResGraphQlApi>()
          .loadResBrazilBeneficiaryIndicators(
        cpf: cpf,
      );

      if (_listIndicators.isEmpty) {
        emit(NoDataResIndicatorState());
      } else {
        emit(LoadedResIndicatorState(listResIndicators: _listIndicators));
      }
    } catch (e) {
      emit(ErrorResIndicatorState(message: e.toString()));
    }
  }

  void searchCategoryIndicator({required List<String> indicators}) async {
    try {
      emit(LoadingResAllIndicatorsState());

      if (indicators.isEmpty) {
        emit(NoDataResIndicatorState());
      } else if (indicators.last == 'Todos') {
        emit(LoadedResIndicatorState(listResIndicators: _listIndicators));
        return;
      }

      final List<ResIndicatorModel> filtredList =
          _listIndicators.where((element) {
        return indicators
            .any((indicator) => element.id.toString().contains(indicator));
      }).toList();

      if (filtredList.isEmpty) {
        emit(NoDataResIndicatorState());
      } else {
        emit(LoadedResIndicatorState(listResIndicators: filtredList));
      }
    } catch (e) {
      emit(ErrorResIndicatorState(message: e.toString()));
    }
  }

/* 
  void listResBrazilBeneficiaryIndicatorsData({required String crm,
      required String card,}) async {
    try {
      emit(LoadingResAllIndicatorsState());
 _listIndicators = await Locator.instance!<GraphQlApi>()
          .resBrazilBeneficiaryIndicatorsData(cpf: '60866109323', indicatorIds: '16', calendarType: 'month', startDate: '2021-12-12', endDate: '2024-12-12'
               );

  /*     Future.delayed( const Duration(seconds: 3),(){
       emit(LoadedResIndicatorState(listResIndicators: _listIndicators));
      }); */

      
    } catch (e) {
      emit(ErrorResIndicatorState(message: e.toString()));
    }
  } */

/*   void getBeneficiaryIndicatorList(){
    try {
      emit(LoadingResAllIndicatorsState());

     var resul = Locator.instance!.get<GraphQlApi>().resBrazilBeneficiaryIndicatorList(cpf: '60866109323');
    } catch (e) {
      print(e);
    }
  } */
}
