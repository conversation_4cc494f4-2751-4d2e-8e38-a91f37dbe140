import 'package:cooperado_minha_unimed/bloc/auth/auth_cubit.dart';
import 'package:cooperado_minha_unimed/colors.dart';
import 'package:cooperado_minha_unimed/shared/constants.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class CardServicesDescription extends StatefulWidget {
  const CardServicesDescription({super.key});

  @override
  CardServicesDescriptionState createState() => CardServicesDescriptionState();
}

class CardServicesDescriptionState extends State<CardServicesDescription> {
  double total = 0.0;
  DateTime? selectedDateTime;
  String description = "";
  bool _isExpanded = false;

  @override
  void initState() {
    description = context.read<AuthCubit>().getMessageConfig(servicos);
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color:
            _isExpanded ? CooperadoColors.aliceBlue : CooperadoColors.tealGreen,
        borderRadius: BorderRadius.circular(15.0),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(40.0),
        child: Theme(
          data: ThemeWidgets.expansionTile(
            context,
            colorArrow: _isExpanded ? CooperadoColors.tealGreen : Colors.white,
          ),
          child: ListTileTheme(
            contentPadding: const EdgeInsets.symmetric(horizontal: 15),
            child: ExpansionTile(
              onExpansionChanged: (value) => setState(() {
                _isExpanded = value;
              }),
              childrenPadding: const EdgeInsets.symmetric(
                horizontal: 15,
                vertical: 10,
              ),
              title: Text(
                'Serviços',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: _isExpanded ? CooperadoColors.blackText : Colors.white,
                ),
              ),
              children: <Widget>[
                Text(
                  description,
                  textAlign: TextAlign.start,
                  style: TextStyle(
                      color: _isExpanded
                          ? CooperadoColors.blackText
                          : CooperadoColors.grayLight),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
