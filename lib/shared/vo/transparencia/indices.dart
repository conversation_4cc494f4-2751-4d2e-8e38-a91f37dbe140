import 'package:json_annotation/json_annotation.dart';

part 'indices.g.dart';

@JsonSerializable(anyMap: true)
class VOIndicatorModel {
  @JsonKey(name: 'dados')
  List<VODataModel>? data;

  @Json<PERSON>ey(name: 'ultimo')
  VODataModel? last;

  VOIndicatorModel({this.data, this.last});

  factory VOIndicatorModel.fromJson(Map json) =>
      _$VOIndicatorModelFromJson(json);
  Map<String, dynamic> toJson() => _$VOIndicatorModelToJson(this);
}

@JsonSerializable(anyMap: true)
class VODataModel {
  @JsonKey(name: 'anoReferencia')
  int? referenceYear;

  @JsonKey(name: 'mesReferencia')
  int? referenceMonth;

  @JsonKey(name: 'mesReferenciaTexto')
  String? referenceMonthLabel;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'valorAcumulado')
  double? totalValue;

  @Json<PERSON>ey(name: 'valorMensal')
  double? monthValue;

  @JsonKey(name: 'valorProjetada')
  double? projectedValue;

  VODataModel(
      {this.referenceMonth,
      this.referenceYear,
      this.referenceMonthLabel,
      this.totalValue,
      this.monthValue,
      this.projectedValue});

  factory VODataModel.fromJson(Map json) => _$VODataModelFromJson(json);
  Map<String, dynamic> toJson() => _$VODataModelToJson(this);
}
