import 'package:bloc/bloc.dart';
import 'package:cooperado_minha_unimed/shared/api/services.api.dart';
import 'package:cooperado_minha_unimed/shared/locator.dart';
import 'package:flutter/material.dart';

import 'add_glosa_resource_state.dart';

class AddGlosaResourceCubit extends Cubit<AddGlosaResourceState> {
  AddGlosaResourceCubit() : super(AddGlosaResourceInitial());

  addGlosaResourceEvent({
    required String crm,
    required String guide,
    required String codService,
    required String justification,
    required String numSohoev,
  }) async {
    emit(LoadingAddGlosaResourceState());
    try {
      final solicitacao = await Locator.instance!<ServicesApi>()
          .addGlosaResource(
              crm: crm,
              guide: guide,
              codService: codService,
              justification: justification,
              numSohoev: numSohoev);

      emit(DoneAddGlosaResourceState(solicitacao));
    } catch (ex) {
      debugPrint(ex.toString());
      emit(ErrorAddGlosaResourceState('$ex'));
    }
  }

  setToInitiaState() {
    emit(AddGlosaResourceInitial());
  }
}
