import 'package:cooperado_minha_unimed/shared/utils/string_utils.dart';

class RetornoProducaoMedica {
  int? ano;
  List<Meses>? meses;

  RetornoProducaoMedica({this.ano, this.meses});

  RetornoProducaoMedica.fromJson(Map<String, dynamic> json) {
    ano = json['ano'];
    if (json['meses'] != null) {
      meses = [];
      json['meses'].forEach((v) {
        meses!.add(Meses.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['ano'] = ano;
    if (meses != null) {
      data['meses'] = meses!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class Meses {
  int? mes;
  List<Producoes>? producoes;
  double? total;

  Meses({this.mes, this.producoes, this.total});
  String get totalFormatted => StringUtils.formatMoney(total!);
  Meses.fromJson(Map<String, dynamic> json) {
    mes = json['mes'];
    if (json['producoes'] != null) {
      producoes = [];
      json['producoes'].forEach((v) {
        producoes!.add(Producoes.fromJson(v));
      });
    }
    total = double.parse(json['total'].toString());
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['mes'] = mes;
    if (producoes != null) {
      data['producoes'] = producoes!.map((v) => v.toJson()).toList();
    }
    data['total'] = total;
    return data;
  }
}

class Producoes {
  String? descricao;
  String? tipoProducao;
  String? dataProducao;
  String? dataProducaoFim;
  String? dataAtendimento;
  double? valor;

  String get valorFormatted => StringUtils.formatMoney(valor!);

  Producoes(
      {this.descricao,
      this.tipoProducao,
      this.dataProducao,
      this.dataProducaoFim,
      this.dataAtendimento,
      this.valor});

  Producoes.fromJson(Map<String, dynamic> json) {
    descricao = json['descricao'];
    tipoProducao = json['tipoProducao'];
    dataProducao = json['dataProducao'];
    dataProducaoFim = json['dataProducaoFim'];
    dataAtendimento = json['dataAtendimento'];
    valor = double.parse(json['valor'].toString());
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['descricao'] = descricao;
    data['tipoProducao'] = tipoProducao;
    data['dataProducao'] = dataProducao;
    data['dataProducaoFim'] = dataProducaoFim;
    data['dataAtendimento'] = dataAtendimento;
    data['valor'] = valor;
    return data;
  }
}
