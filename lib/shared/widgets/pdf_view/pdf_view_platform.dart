import 'dart:async';
import 'dart:io';

import 'package:cooperado_minha_unimed/bloc/pdf-factory/pdf_factory_cubit.dart';
import 'package:cooperado_minha_unimed/shared/messages.exceptions.dart';
import 'package:cooperado_minha_unimed/shared/utils/logger_print.dart';
import 'package:cooperado_minha_unimed/shared/widgets/alert.dart';
import 'package:cooperado_minha_unimed/shared/widgets/error-banner/error_banner.component.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:pdfx/pdfx.dart';
import 'package:share_plus/share_plus.dart';

class PDFViewPlatform extends StatefulWidget {
  const PDFViewPlatform(
    this.url, {
    super.key,
    this.share,
    this.filename = "",
    this.isAgree = false,
    this.title = "Visualização de arquivos",
    this.isPath = false,
    this.isFirebase = false,
  });
  final String? url;
  final bool? share;
  final bool isPath;

  final String filename;
  final String? title;
  final bool isAgree;
  final bool isFirebase;
  @override
  PDFViewPlatformState createState() => PDFViewPlatformState();
}

class PDFViewPlatformState extends State<PDFViewPlatform> {
  final logger = UnimedLogger(className: 'PDFViewPlatform');
  File? _file;
  bool shareIsEnable = false;
  int? pageTotal = 1;
  int? pageCurrent = 1;

  bool pdfLoaded = false;
  bool isError = false;

  @override
  void initState() {
    super.initState();

    if (widget.isPath) {
      context.read<PdfFactoryCubit>().generatePdfDocumentFromPath(
          url: widget.url ?? "", filename: widget.filename);
    } else {
      context.read<PdfFactoryCubit>().generatePdfDocument(
          url: widget.url ?? "",
          filename: widget.filename,
          isFirebase: widget.isFirebase);
    }
  }

  @override
  void dispose() {
    SystemChrome.setPreferredOrientations([DeviceOrientation.portraitUp]);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return OrientationBuilder(builder: (context, orientation) {
      return Scaffold(
        appBar: _isLandscapeMode(orientation) || widget.isAgree
            ? null
            : AppBar(
                title: Text(widget.title!),
                actions: isError
                    ? []
                    : [
                        _iconChangeOrientations(orientation),
                        _iconShare(),
                      ],
              ),
        body: SafeArea(
          child: Center(
            child: BlocConsumer<PdfFactoryCubit, PdfFactoryState>(
              listener: (context, current) {
                if (current is ErrorPdf) _openDialog();
              },
              builder: (context, state) {
                if (state is LoadingPdf) {
                  return const CircularProgressIndicator();
                } else if (state is DonePdf) {
                  _file = state.file;
                  shareIsEnable = true;
                  return _viewPdf(state.pdfDocument, orientation);
                } else {
                  return Container();
                }
              },
            ),
          ),
        ),
      );
    });
  }

  Widget _viewPdf(pdfDocument, orientation) {
    return isError
        ? const Center(
            child: ErrorBanner(
              message: MessageException.genericPdfError,
            ),
          )
        : Stack(
            children: [
              if (Platform.isIOS || Platform.isAndroid)
                PdfView(
                  controller: pdfDocument!,
                  onPageChanged: (page) {
                    setState(() {
                      pageCurrent = page;
                    });
                  },
                  onDocumentLoaded: (document) {
                    setState(() {
                      pdfLoaded = true;
                    });
                  },
                  onDocumentError: ((error) {
                    logger.e("PdfView - erro on load pdf error=> $error");
                    setState(() {
                      isError = true;
                    });
                  }),
                ),
              if ((Platform.isIOS || Platform.isAndroid) &&
                  pdfLoaded &&
                  !widget.isAgree) ...<Widget>[
                Align(
                  alignment: Alignment.bottomRight,
                  child: customContainer(ultimatePage(pdfDocument)),
                ),
                Align(
                  alignment: Alignment.bottomRight,
                  child: Container(
                      margin: const EdgeInsets.only(right: 50),
                      child: customContainer(rightPage(pdfDocument))),
                ),
                Align(
                  alignment: Alignment.bottomLeft,
                  child: customContainer(initialPage(pdfDocument)),
                ),
                Container(
                  margin: const EdgeInsets.only(left: 50),
                  child: Align(
                    alignment: Alignment.bottomLeft,
                    child: customContainer(leftPage(pdfDocument)),
                  ),
                ),
                Align(
                  alignment: Alignment.topRight,
                  child: Padding(
                    padding: _isLandscapeMode(orientation)
                        ? const EdgeInsets.only(right: 60)
                        : const EdgeInsets.only(top: 10),
                    child: Column(
                      children: [
                        customContainer(numberPageTotal(pdfDocument)),
                      ],
                    ),
                  ),
                ),
              ],
              if (_isLandscapeMode(orientation) && !widget.isAgree) ...<Widget>[
                Align(
                  alignment: Alignment.topRight,
                  child: Column(
                    children: [
                      customContainer(_iconChangeOrientations(orientation)),
                      _iconShare(),
                    ],
                  ),
                ),
                Align(
                  alignment: Alignment.topLeft,
                  child: Padding(
                    padding: (_isLandscapeMode(orientation) && !widget.isAgree)
                        ? const EdgeInsets.only(top: 0)
                        : const EdgeInsets.only(top: 50),
                    child: Column(
                      children: [
                        customContainer(_iconBack()),
                      ],
                    ),
                  ),
                ),
              ],
            ],
          );
  }

  Widget rightPage(PdfController pdfDocument) {
    return IconButton(
      icon: const Icon(
        Icons.arrow_forward,
        color: Colors.white,
      ),
      onPressed: () async {
        pdfDocument.nextPage(
            duration: const Duration(milliseconds: 250), curve: Curves.easeIn);
      },
    );
  }

  Widget initialPage(PdfController pdfDocument) {
    return RotatedBox(
      quarterTurns: 2,
      child: IconButton(
        icon: const Icon(
          Icons.double_arrow,
          color: Colors.white,
        ),
        onPressed: () async {
          // await snapshot.data!.setPage(0);
          pdfDocument.animateToPage(0,
              duration: const Duration(milliseconds: 250), curve: Curves.ease);
        },
      ),
    );
  }

  Widget ultimatePage(PdfController pdfDocument) {
    return IconButton(
      icon: const Icon(
        Icons.double_arrow,
        color: Colors.white,
      ),
      onPressed: () async {
        pageTotal = pdfDocument.pagesCount! + 1;
        pdfDocument.animateToPage(pageTotal!,
            duration: const Duration(milliseconds: 250), curve: Curves.ease);
      },
    );
  }

  Widget leftPage(PdfController pdfDocument) {
    Future<PdfController>? future;
    return FutureBuilder<PdfController>(
        // future: controller.future,
        future: future,
        builder: (context, AsyncSnapshot<PdfController> snapshot) {
          return IconButton(
            alignment: Alignment.center,
            icon: const Icon(
              Icons.arrow_back,
              color: Colors.white,
            ),
            onPressed: () async {
              // await snapshot.data!.setPsage(pageCurrent! - 2);

              pdfDocument.previousPage(
                  duration: const Duration(milliseconds: 250),
                  curve: Curves.easeIn);
            },
          );
        });
  }

  Widget numberPageTotal(PdfController pdfDocument) {
    pageTotal = pdfDocument.pagesCount;

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
      child: Text(
        '$pageCurrent/$pageTotal',
        style: const TextStyle(color: Colors.white),
      ),
    );
  }

  Widget customContainer(child) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 25, vertical: 10),
      decoration: BoxDecoration(
        color: Colors.grey[700]!.withAlpha(200),
        borderRadius: const BorderRadius.all(Radius.circular(5)),
      ),
      child: child,
    );
  }

  Widget _iconBack() {
    return IconButton(
      alignment: Platform.isIOS ? Alignment.centerRight : Alignment.center,
      icon: Icon(Platform.isIOS ? Icons.arrow_back_ios : Icons.arrow_back,
          color: Colors.white),
      onPressed: () {
        Navigator.pop(context);
      },
    );
  }

  Widget _iconShare() {
    return widget.share != null
        ? Container(
            margin: const EdgeInsets.symmetric(horizontal: 25, vertical: 10),
            decoration: BoxDecoration(
              color: Colors.grey[700]!.withAlpha(200),
              borderRadius: const BorderRadius.all(Radius.circular(5)),
            ),
            child: IconButton(
              icon: const Icon(Icons.share, color: Colors.white),
              onPressed: shareIsEnable ? _urlFileShare : null,
            ),
          )
        : Container(height: 5, width: 5, color: Colors.transparent);
  }

  Widget _iconChangeOrientations(orientation) {
    return IconButton(
      icon: const Icon(Icons.screen_rotation_rounded, color: Colors.white),
      onPressed: () {
        orientation.index == DeviceOrientation.portraitUp.index
            ? SystemChrome.setPreferredOrientations(
                [DeviceOrientation.landscapeLeft])
            : SystemChrome.setPreferredOrientations(
                [DeviceOrientation.portraitUp]);
      },
    );
  }

  bool _isLandscapeMode(orientation) =>
      orientation.index == DeviceOrientation.landscapeLeft.index;

  _openDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => CooperadoAlertDialog(
        textWidget: const Text('Não foi possível carregar PDF',
            textAlign: TextAlign.center),
        onPressed: () {
          Navigator.pop(context);
          Navigator.pop(context);
        },
      ),
    );
  }

  Future<void> _urlFileShare() async {
    setState(() => shareIsEnable = false);
    if (_file == null) return;
    try {
      await Share.shareXFiles([XFile(_file!.path)]);
    } catch (ex) {
      logger.e('_urlFileShare catch exception - $ex - url: ${widget.url}');
      setState(() => shareIsEnable = true);
    } finally {
      Future.delayed(const Duration(milliseconds: 1500),
          () => setState(() => shareIsEnable = true));
    }
  }
}
