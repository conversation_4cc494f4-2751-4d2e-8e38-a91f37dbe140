import 'package:auto_size_text/auto_size_text.dart';
import 'package:cooperado_minha_unimed/bloc/res-internal/lab_exams_detail/res_lab_exam_detail_cubit.dart';
import 'package:cooperado_minha_unimed/colors.dart';
import 'package:cooperado_minha_unimed/models/res-internal/lab_exam_detail.model.dart';
import 'package:cooperado_minha_unimed/shared/utils/logger_print.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:url_launcher/url_launcher.dart';

class ResLabExamDetailScreen extends StatefulWidget {
  final String card;
  final String orderId;
  final List<ResLabExamDetailModel> listLabExamDetails;
  const ResLabExamDetailScreen(
      {super.key,
      required this.card,
      required this.orderId,
      required this.listLabExamDetails});

  @override
  State<ResLabExamDetailScreen> createState() => _ResLabExamDetailScreenState();
}

class _ResLabExamDetailScreenState extends State<ResLabExamDetailScreen> {
  final logger = UnimedLogger(className: '_ResLabExamDetailScreenState');
  TextEditingController controllerSearch = TextEditingController();

  @override
  void initState() {
    context
        .read<ResLabExamDetailCubit>()
        .getExamDetailsWidget(widget.listLabExamDetails);
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: CooperadoColors.grayLight3,
      appBar: AppBar(
        centerTitle: true,
        title: const Text("Exames Laboratoriais"),
        backgroundColor: CooperadoColors.tealGreenDark,
        actions: [
          IconButton(
              onPressed: () {
                controllerSearch.clear();
                context.read<ResLabExamDetailCubit>().listResLabExamDetails(
                      card: widget.card,
                      orderId: widget.orderId,
                    );
              },
              icon: const Icon(Icons.refresh))
        ],
      ),
      body: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Padding(
                padding: const EdgeInsets.all(8.0),
                child: Container(
                  decoration: const BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.all(
                      Radius.circular(24),
                    ),
                  ),
                  child: _serchLabExam(),
                ),
              ),
              BlocBuilder<ResLabExamDetailCubit, ResLabExamDetailState>(
                builder: (context, state) {
                  if (state is LoadedResLabExamDetailState) {
                    return Expanded(
                      child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const SizedBox(height: 20),
                            AutoSizeText(
                              '${state.listLabExamDetails.length} ${state.listLabExamDetails.length > 1 ? 'exames encontrados' : 'exame encontrado'}',
                              style: const TextStyle(
                                color: CooperadoColors.tealGreen,
                                fontSize: 12,
                              ),
                            ),
                            const SizedBox(height: 5),
                            Expanded(
                              child: ListView.builder(
                                physics: const ClampingScrollPhysics(
                                    parent: AlwaysScrollableScrollPhysics()),
                                itemCount: state.listLabExamDetails.length,
                                itemBuilder: (context, index) {
                                  final labExam =
                                      state.listLabExamDetails[index];
                                  return _buildLabExamItem(
                                      labExamModel: labExam);
                                },
                              ),
                            ),
                          ]),
                    );
                  } else if (state is LoadingResLabExamDetailState) {
                    return const Center(child: CircularProgressIndicator());
                  } else {
                    return Container();
                  }
                },
              ),
            ],
          )),
    );
  }

  Widget _serchLabExam() {
    return TextFormField(
      enableInteractiveSelection: false,
      autofocus: false,
      controller: controllerSearch,
      textInputAction: TextInputAction.search,
      style: const TextStyle(color: Colors.green),
      onChanged: (query) {
        debugPrint('query: $query');

        context.read<ResLabExamDetailCubit>().searchListLabExamDetails(
              searchKey: query,
              widgetList: widget.listLabExamDetails,
            );
      },
      maxLines: 1,
      decoration: InputDecoration(
        hintText: 'Buscar exame',
        hintMaxLines: null,
        suffixIcon: IconButton(
          onPressed: () {
            controllerSearch.clear();
            context.read<ResLabExamDetailCubit>().searchListLabExamDetails(
                  searchKey: '',
                  widgetList: widget.listLabExamDetails,
                );
          },
          icon: const Icon(
            Icons.clear,
            size: 16,
          ),
        ),
        prefixIcon: const Icon(
          Icons.search,
          size: 24,
          color: CooperadoColors.green,
        ),
        contentPadding: const EdgeInsets.only(
          top: 12,
          left: 10,
          right: 10,
          bottom: 6,
        ),
        border: InputBorder.none,
      ),
    );
  }

  Widget _buildLabExamItem({required ResLabExamDetailModel labExamModel}) {
    return Hero(
      tag: labExamModel.testId,
      child: InkWell(
        onTap: () {},
        child: Card(
          margin: const EdgeInsets.symmetric(vertical: 5),
          child: Row(
            children: [
              Expanded(
                child: Padding(
                  padding: const EdgeInsets.all(12.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildItemText(
                        title: 'Exame',
                        value: labExamModel.testName,
                      ),
                      const SizedBox(height: 5),
                      _buildItemText(
                        title: 'Material',
                        value: labExamModel.material,
                      ),
                      const SizedBox(height: 20),
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Expanded(
                            child: _buildDateText(
                              title: 'Data de coleta',
                              value: labExamModel.dateCollectionFormatted,
                            ),
                          ),
                          Expanded(
                            child: _buildDateText(
                              title: 'Previsão do resultado',
                              value: labExamModel.dateScheduledFormatted,
                              color: CooperadoColors.green,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 20),
                      if (labExamModel.testPdfUrl != null)
                        InkWell(
                          onTap: () => {
                            _launchURL(labExamModel.testPdfUrl!),
                          },
                          child: const Center(
                            child: AutoSizeText(
                              'Ver resultado',
                              style: TextStyle(
                                color: CooperadoColors.tealGreen,
                                fontSize: 14,
                                decoration: TextDecoration.underline,
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ),
                        ),
                      const SizedBox(height: 20),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildItemText({required String title, required String? value}) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        AutoSizeText(
          '$title: ',
          style: const TextStyle(
            color: CooperadoColors.blackText,
            fontSize: 12,
          ),
        ),
        Expanded(
          child: AutoSizeText(
            value ?? 'Não informado',
            style: const TextStyle(
              color: CooperadoColors.green,
              fontWeight: FontWeight.bold,
            ),
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ],
    );
  }

  Widget _buildDateText(
      {required String title, required String? value, Color? color}) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        const Icon(Icons.date_range_sharp, color: CooperadoColors.blackText),
        const SizedBox(width: 5),
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            AutoSizeText(
              '$title: ',
              style: const TextStyle(
                color: CooperadoColors.blackText,
                fontSize: 12,
              ),
            ),
            AutoSizeText(
              value ?? 'Não informado',
              style: TextStyle(
                color: color ?? CooperadoColors.blackText,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Future _launchURL(String url) async {
    logger.i('launchURL - abrindo url $url');
    if (await canLaunchUrl(Uri.parse(url))) {
      await launchUrl(Uri.parse(url), mode: LaunchMode.externalApplication);
    } else {
      throw 'Unable to open url : $url';
    }
  }
}
