import 'package:cooperado_minha_unimed/bloc/indicators/medical_production/medical_production_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/sensitive-data/sensitive_data_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/sensitive-data/sensitive_data_state.dart';
import 'package:cooperado_minha_unimed/colors.dart';
import 'package:cooperado_minha_unimed/screens/indicadores/producao-medica/details_screen.dart';
import 'package:cooperado_minha_unimed/shared/screen-transitions/fade.transition.dart';
import 'package:cooperado_minha_unimed/shared/widgets/card_refresh.dart';
import 'package:cooperado_minha_unimed/shared/widgets/error-banner/error_banner.component.dart';
import 'package:cooperado_minha_unimed/shared/widgets/last_production_date_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';

class CardProducaoMedica extends StatefulWidget {
  final Function refresh;
  const CardProducaoMedica({
    super.key,
    required this.refresh,
  });
  @override
  CardProducaoMedicaState createState() => CardProducaoMedicaState();
}

class CardProducaoMedicaState extends State<CardProducaoMedica> {
  @override
  Widget build(BuildContext context) {
    return CardRefresh(
      title: const Text("Produção Médica - Valor Bruto",
          style: TextStyle(
            color: CooperadoColors.blackText,
            fontSize: 16,
            fontWeight: FontWeight.bold,
          )),
      refresh: _iconRefresh(),
      child: BlocBuilder<MedicalProductionCubit, MedicalProductionState>(
  builder: (context, medicalProductionState) {
    return BlocBuilder<SensitiveDataCubit, SensitiveDataState>(
      builder: (context, sensitiveState) {
        if (medicalProductionState is LoadedMedicalProductionState) {
          return Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: <Widget>[
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: <Widget>[
                  Padding(
                    padding: const EdgeInsets.all(8.0),
                    child: Text(
                      sensitiveState.isSensitiveDataVisible
                          ? medicalProductionState.retornoComparativoCustoVO.first.meses!.first.totalFormatted
                          : '*****',
                      style: const TextStyle(
                        color: CooperadoColors.green,
                        fontWeight: FontWeight.bold,
                        fontSize: 18,
                      ),
                    ),
                  ),
                  LastProductionDateWidget(
                    onPressed: _onPressed,
                    date: context.read<MedicalProductionCubit>().selectedDate,
                  ),
                ],
              ),
              Padding(
                padding: const EdgeInsets.only(top: 8.0),
                child: _buttonDetails(
                  context,
                  medicalProductionState.retornoComparativoCustoVO,
                ),
              )
            ],
          );
        } else if (medicalProductionState is LoadingMedicalProductionState) {
          return const SpinKitCircle(
            color: CooperadoColors.tealGreen,
          );
        } else if (medicalProductionState is ErrorMedicalProductionState) {
          return Column(
            children: <Widget>[
              LastProductionDateWidget(
                onPressed: _onPressed,
                date: context.read<MedicalProductionCubit>().selectedDate,
              ),
              ErrorBanner(message: medicalProductionState.message)
            ],
          );
        } else {
          return Container();
        }
      },
    );
  },
),
    );
  }

  Widget _iconRefresh() {
    return BlocBuilder<MedicalProductionCubit, MedicalProductionState>(
        builder: (context, state) {
      if (state is ErrorMedicalProductionState) {
        return InkWell(
            child: const Icon(Icons.refresh), onTap: () => widget.refresh());
      } else {
        return Container();
      }
    });
  }

  Widget _buttonDetails(context, retornoComparativoCustoVO) {
    return Align(
        child: InkWell(
            onTap: () {
              Navigator.push(
                  context,
                  FadeRoute(
                      page: DetailsScreen(
                          retornoProducaoMedica:
                              retornoComparativoCustoVO.first)));
            },
            child: Container(
                padding: const EdgeInsets.only(
                    top: 12.0, bottom: 12.0, left: 40, right: 40),
                decoration: const BoxDecoration(
                    color: CooperadoColors.tealGreen,
                    borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(20),
                        topRight: Radius.circular(20))),
                child: const Text(
                  "VER MAIS",
                  style: TextStyle(
                    //fontSize: 18,
                    color: Colors.white,
                  ),
                ))));
  }

  void _onPressed(selectedDate) {
    context.read<MedicalProductionCubit>().selectDate(selectedDate);
  }
}
