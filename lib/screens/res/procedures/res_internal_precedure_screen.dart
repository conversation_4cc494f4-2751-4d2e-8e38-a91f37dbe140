import 'package:cooperado_minha_unimed/bloc/auth/auth_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/res/procedures/detail/res_procedures_detail_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/res/procedures/res_procedures_cubit.dart';
import 'package:cooperado_minha_unimed/colors.dart';
import 'package:cooperado_minha_unimed/screens/res/procedures/res_procedure_detail.dart';
import 'package:cooperado_minha_unimed/screens/res/widget/app_bar_res.dart';
import 'package:cooperado_minha_unimed/screens/res/widget/custom_button_card.dart';
import 'package:cooperado_minha_unimed/screens/res/widget/empty_list.dart';
import 'package:cooperado_minha_unimed/screens/res/widget/erro_service.dart';
import 'package:cooperado_minha_unimed/screens/res/widget/expandable_card.dart';
import 'package:cooperado_minha_unimed/screens/res/widget/filters_widget.dart';
import 'package:cooperado_minha_unimed/shared/utils/router_observer.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:intl/intl.dart';

class ResInternalProceduresScreen extends StatefulWidget {
  final String nameBeneficiary;
  final String beneficiaryCard;
  const ResInternalProceduresScreen(
      {super.key,
      required this.nameBeneficiary,
      required this.beneficiaryCard});

  @override
  State<ResInternalProceduresScreen> createState() =>
      _ResInternalAllergiesScreenState();
}

class _ResInternalAllergiesScreenState
    extends State<ResInternalProceduresScreen> with RouteAware {
  final bool _isLoading = false;
  bool loadingDetail = false;
  int? _filterMonthSelected = 0;
  DateTimeRange? _dateRangeToFilter;
  final List<int> _lastMonthsToFilter = [3, 6];

  List<ExpansionTileController> controllers = [];
  int? previusSelectedIndex;

  @override
  void initState() {
    _loadProcedures();
    super.initState();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    routeObserver.subscribe(this, ModalRoute.of(context) as PageRoute);
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: CooperadoColors.backgroundWhiteColor,
      appBar: AppBarRes(
        title: 'Procedimentos',
        nameBeneficiary: widget.nameBeneficiary,
      ),
      body: Padding(
        padding: const EdgeInsets.only(left: 20, right: 20, top: 24),
        child: Column(
          children: [
            _filterDate(),
            BlocBuilder<ResProcedureCubit, ResProceduresState>(
              builder: (context, state) {
                if (state is LoadingResProceduresState) {
                  return const Expanded(
                    child: Center(
                      child: SpinKitCircle(color: CooperadoColors.tealGreen),
                    ),
                  );
                } else if (state is LoadedResProceduresState) {
                  return Expanded(
                      child: RefreshIndicator(
                          onRefresh: () async {
                            _loadProcedures();
                          },
                          child: ListView.builder(
                            physics: const ClampingScrollPhysics(
                                parent: AlwaysScrollableScrollPhysics()),
                            itemCount: state.listResProcedures.length,
                            itemBuilder: (context, index) {
                              ExpansionTileController controller =
                                  ExpansionTileController();

                              controllers.add(controller);
                              return Padding(
                                padding: const EdgeInsets.all(8.0),
                                child: ExpandableCard(
                                  controller: controller,
                                  pathIcon: 'assets/svg/icon_procedure.svg',
                                  title: 'Procedimento',
                                  subtitle: state.listResProcedures[index]
                                      .dataEntradaFormatted,
                                  loading: loadingDetail,
                                  onExpansionChanged: (value) {
                                    setState(() {
                                      if (value &&
                                          previusSelectedIndex != index) {
                                        if (previusSelectedIndex != null) {
                                          controllers[previusSelectedIndex!]
                                              .collapse();
                                        }
                                        previusSelectedIndex = index;
                                      }
                                    });
                                  },
                                  buttons: [
                                    CustomButtonCard(
                                      text: 'Procedimentos',
                                      onPressed: () {
                                        _showDetailModal(state
                                                .listResProcedures[index]
                                                .codigo ??
                                            '');
                                        context
                                            .read<ResProcedureDetailCubit>()
                                            .getProcedureDetail(
                                              crm: context
                                                  .read<AuthCubit>()
                                                  .credentials
                                                  .crm,
                                              card: widget.beneficiaryCard,
                                              code: state
                                                      .listResProcedures[index]
                                                      .codigo ??
                                                  '',
                                              index: index,
                                            );
                                      },
                                    ),
                                  ],
                                  additionalInfo: [
                                    {
                                      'title': 'Tipo',
                                      'description':
                                          state.listResProcedures[index].tipo ??
                                              'Sem dados no momento.'
                                    },
                                    {
                                      'title': 'Local',
                                      'description': state
                                              .listResProcedures[index]
                                              .nomeLocal ??
                                          'Sem dados no momento.'
                                    },
                                  ],
                                ),
                              );
                            },
                          )));
                } else if (state is NoDataResProceduresState) {
                  return Expanded(
                    child: Padding(
                      padding: const EdgeInsets.only(top: 32.0),
                      child: EmptyList(
                        pathIcon: 'assets/svg/icon_file.svg',
                        message: _dateRangeToFilter != null
                            ? 'Sem dados de procedimento para o período de ${DateFormat("dd/MM/yyyy").format(_dateRangeToFilter!.start)} até  ${DateFormat("dd/MM/yyyy").format(_dateRangeToFilter!.end)}'
                            : 'Sem dados de procedimento.',
                      ),
                    ),
                  );
                } else if (state is ErrorResProceduresState) {
                  return Expanded(
                    child: Center(
                      child: ErroService(
                        message: state.message,
                        onPressed: () {
                          _loadProcedures();
                        },
                      ),
                    ),
                  );
                }
                return Container();
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _filterDate() {
    return FiltersWidget(
      isLoading: _isLoading,
      lastMonthsToFilter: _lastMonthsToFilter,
      filterMonthSelected: _filterMonthSelected,
      dateRangeToFilter: _dateRangeToFilter,
      onMonthFilterChanged: (filterSelected) {
        setState(() {
          _filterMonthSelected = filterSelected;
          filterSelected == null
              ? _dateRangeToFilter = null
              : _dateRangeToFilter = DateTimeRange(
                  start: _selectedDataStart(filterSelected),
                  end: DateTime.now(),
                );
        });
        _loadProcedures();
      },
      onClearDateRange: () {
        setState(() {
          _dateRangeToFilter = null;
          _filterMonthSelected = null;
        });
        _loadProcedures();
      },
      selectDateToFilter: _selectDateToFilter,
      onDateRangeSelected: (dateRange) {
        setState(() {
          _filterMonthSelected = null;
          _dateRangeToFilter = dateRange;
        });
        _loadProcedures();
      },
    );
  }

  void _showDetailModal(String code) {
    showModalBottomSheet<void>(
        context: context,
        shape: const RoundedRectangleBorder(
            borderRadius: BorderRadius.vertical(top: Radius.circular(15))),
        builder: (BuildContext context) {
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              BlocProvider(
                create: (context) => ResProcedureDetailCubit()
                  ..getProcedureDetail(
                    crm: context.read<AuthCubit>().credentials.crm,
                    card: widget.beneficiaryCard,
                    code: code,
                  ),
                child: BlocBuilder<ResProcedureDetailCubit,
                    ResProcedureDetailState>(
                  builder: (context, state) {
                    if (state is LoadingResProcedureDetailState) {
                      return Column(
                        children: [
                          Center(
                            child: Container(
                              height: 4,
                              width: 100,
                              margin: const EdgeInsets.symmetric(
                                vertical: 10,
                              ),
                              decoration: BoxDecoration(
                                color: CooperadoColors.grayLight3,
                                borderRadius: BorderRadius.circular(2),
                              ),
                            ),
                          ),
                          const Center(
                            child:
                                SpinKitCircle(color: CooperadoColors.tealGreen),
                          ),
                          const SizedBox(height: 16),
                        ],
                      );
                    } else if (state is LoadedResProcedureDetailState) {
                      return Expanded(
                          child: ResProcedureDetail(
                              resProcedureDetailModel:
                                  state.resProcedureDetailModel));
                    } else if (state is ErrorResProcedureDetailState) {
                      return Padding(
                        padding: const EdgeInsets.all(12.0),
                        child: Center(
                            child: ErroService(
                          message: state.message,
                          onPressed: () {
                            context
                                .read<ResProcedureDetailCubit>()
                                .getProcedureDetail(
                                  crm:
                                      context.read<AuthCubit>().credentials.crm,
                                  card: widget.beneficiaryCard,
                                  code: code,
                                );
                          },
                        )),
                      );
                    } else if (state is NoDataResProcedureDetailState) {
                      return Padding(
                        padding: const EdgeInsets.only(top: 32.0),
                        child: Center(
                            child: EmptyList(
                          pathIcon: 'assets/svg/icon_file.svg',
                          message: _dateRangeToFilter != null
                              ? 'Sem dados de procedimentos para o período de ${DateFormat("dd/MM/yyyy").format(_dateRangeToFilter!.start)} até  ${DateFormat("dd/MM/yyyy").format(_dateRangeToFilter!.end)}'
                              : 'Sem dados de procedimentos.',
                        )),
                      );
                    } else {
                      return Container();
                    }
                  },
                ),
              ),
            ],
          );
        });
  }

  DateTime _getFirstDate() {
    DateTime now = DateTime.now();
    int year = now.year;
    bool isLeapYear = (year % 4 == 0 && year % 100 != 0) || (year % 400 == 0);
    return now.subtract(Duration(days: isLeapYear ? 366 : 365));
  }

  Future<DateTime?> _selectDateToFilter(BuildContext context, String helpText,
      {DateTime? firstDate}) async {
    return await showDatePicker(
      context: context,
      helpText: helpText,
      initialDate: DateTime.now(),
      firstDate: firstDate ?? _getFirstDate(),
      lastDate: DateTime.now(),
      keyboardType: TextInputType.text,
      builder: (BuildContext context, Widget? child) {
        return Theme(
          data: ThemeData.light().copyWith(
            colorScheme: const ColorScheme.light(
              primary: CooperadoColors.tealGreen,
              onPrimary: Colors.white,
              onSurface: Colors.black,
            ),
          ),
          child: child!,
        );
      },
    );
  }

  _loadProcedures() {
    setState(() {
      controllers = [];
      previusSelectedIndex = null;
    });

    context.read<ResProcedureCubit>().listResProcedures(
          crm: context.read<AuthCubit>().credentials.crm,
          card: widget.beneficiaryCard,
          dataRange: _dateRangeToFilter,
        );
  }

  DateTime _selectedDataStart(int meses) {
    DateTime dataAtual = DateTime.now();
    DateTime novaData = DateTime(
      dataAtual.year,
      dataAtual.month - meses,
      dataAtual.day,
    );

    return novaData;
  }
}
