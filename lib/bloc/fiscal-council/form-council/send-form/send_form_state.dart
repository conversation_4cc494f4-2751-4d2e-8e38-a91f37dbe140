part of 'send_form_cubit.dart';

abstract class SendFormState extends Equatable {
  const SendFormState();

  @override
  List<Object?> get props => [];
}

class SendFormInitial extends SendFormState {}

class LoadingSendMessage extends SendFormState {}

class DoneSendMessage extends SendFormState {
  final String? message;
  @override
  List<Object?> get props => [message];
  const DoneSendMessage({this.message});
}

class ErrorSendMessage extends SendFormState {
  final String? message;
  @override
  List<Object?> get props => [message];
  const ErrorSendMessage({this.message});
}
