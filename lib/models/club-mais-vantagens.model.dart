class ClubMaisVantagensModel {
  final bool? registeredUser;
  final String? redirectLink;
  final bool? beneficiary;
  final String? phoneFormatted;
  final String? email;

  ClubMaisVantagensModel({
    this.registeredUser,
    this.redirectLink,
    this.beneficiary,
    this.phoneFormatted,
    this.email,
  });

  ClubMaisVantagensModel.fromJson(Map<String, dynamic> json)
  : registeredUser = json['registeredUser'],
    redirectLink = json['redirectLink'],
    beneficiary = json['beneficiary'],
    email = json['email'],
    phoneFormatted = json['phoneFormatted'];

  Map<String, dynamic> toJson() => {
    'registeredUser': registeredUser,
    'redirectLink': redirectLink,
    'beneficiary': beneficiary,
    'phoneFormatted': phoneFormatted,
    'email': email,
  };
}