import 'package:cooperado_minha_unimed/bloc/auth/auth_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/config-profile/config_profile_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/profile/profile_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/sensitive-data/sensitive_data_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/sensitive-data/sensitive_data_state.dart';
import 'package:cooperado_minha_unimed/colors.dart';
import 'package:cooperado_minha_unimed/screens/config-profile/password/renew_password.dart';
import 'package:cooperado_minha_unimed/screens/config-profile/profile/profile.dart';
import 'package:cooperado_minha_unimed/screens/config-profile/widgets/header_config.dart';
import 'package:cooperado_minha_unimed/shared/screen-transitions/fade.transition.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class ConfigProfileScreen extends StatefulWidget {
  final FirebaseAnalytics analytics;
  final FirebaseAnalyticsObserver observer;

  const ConfigProfileScreen({
    super.key,
    required this.analytics,
    required this.observer,
  });

  @override
  ConfigProfileScreenState createState() => ConfigProfileScreenState();
}

class ConfigProfileScreenState extends State<ConfigProfileScreen> {
  String title = 'Perfil';
  int activePage = 0;

  @override
  void initState() {
    super.initState();

    final crm = context.read<AuthCubit>().credentials.crm;
    context.read<ConfigProfileCubit>().getAllAddresses(crm);

    widget.analytics.logScreenView(
      screenName: 'Configurações Perfil',
      screenClass: 'ConfigProfileScreen',
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(title),
        backgroundColor: CooperadoColors.tealGreenDark,
          actions: [
               if (activePage == 0)
            Padding(
              padding: const EdgeInsets.only(right: 15.0),
              child: IconButton(
                icon: BlocBuilder<SensitiveDataCubit, SensitiveDataState>(
                  builder: (context, state) {
                    return Icon(
                      state.isSensitiveDataVisible ? Icons.visibility : Icons.visibility_off,
                    );
                  },
                ),
                onPressed: () {
                  context.read<SensitiveDataCubit>().toggleSensitiveDataVisibility();
                },
              ),
            ),
        ],
      ),
      body: SafeArea(
        child: RefreshIndicator(
          onRefresh: () async {
            Navigator.pushReplacement(
              context,
              FadeRoute(
                page: ConfigProfileScreen(
                  analytics: widget.analytics,
                  observer: widget.observer,
                ),
              ),
            );
          },
          color: CooperadoColors.tealGreen,
          child: ListView(
            children: [
              HeaderConfigProfile(
                activePage: activePage,
                onPressProfile: () => _updatePage(0),
                onPressNewPass: () => _updatePage(1),
              ),
              _activePageWidget(),
            ],
          ),
        ),
      ),
    );
  }

  _updatePage(page) => setState(() => activePage = page);

  Widget _activePageWidget() {
    if (activePage == 0) {
      final user = context.read<ProfileCubit>().user;
      return Profile(
        user: user,
        analytics: widget.analytics,
        observer: widget.observer,
      );
    } else if (activePage == 1) {
      return RenewPassword(
        analytics: widget.analytics,
        observer: widget.observer,
      );
    }
    return Container();
  }
}
