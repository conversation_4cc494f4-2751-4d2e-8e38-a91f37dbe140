import 'package:cooperado_minha_unimed/bloc/sensitive-data/sensitive_data_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/sensitive-data/sensitive_data_state.dart';
import 'package:cooperado_minha_unimed/colors.dart';
import 'package:cooperado_minha_unimed/shared/utils/graph_utils.dart';
import 'package:cooperado_minha_unimed/shared/widgets/graphics/bar_data.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class ProductionBarGraph extends StatelessWidget {
  const ProductionBarGraph({
    super.key,
    required this.maxY,
    required this.data,
  });
  final double maxY;
  final dynamic data;

  Widget getBottomTiles(double value, TitleMeta meta) {
    Widget text;
    switch (value.toInt()) {
      case 0:
        text = const Icon(
          Icons.people_alt,
          color: CooperadoColors.limaColorDark,
          size: 25,
        );
        break;
      case 1:
        text = const Icon(
          Icons.person,
          color: CooperadoColors.orange,
          size: 25,
        );
        break;

      default:
        text = const Text('');
    }
    return SideTitleWidget(axisSide: meta.axisSide, child: text);
  }

  @override
  Widget build(BuildContext context) {
    BarData myBarData = BarData(
      meAmount: data[2].valor ?? 0.0,
      prodAmount: data[3].valor ?? 0.0,
    );
    myBarData.initializeBarData();
    double formattedMaxY = GraphUtils.nextThousand(maxY);

    return BlocBuilder<SensitiveDataCubit, SensitiveDataState>(
      builder: (context, state) {
        return Padding(
          padding: const EdgeInsets.symmetric(vertical: 3),
          child: BarChart(
            BarChartData(
              barTouchData: BarTouchData(
                allowTouchBarBackDraw: false,
                touchTooltipData: BarTouchTooltipData(
                  tooltipBgColor: Colors.blueGrey,
                  tooltipHorizontalAlignment: FLHorizontalAlignment.right,
                  tooltipMargin: -10,
                  getTooltipItem: (group, groupIndex, rod, rodIndex) {
                    return null;
                  },
                ),
              ),
              maxY: formattedMaxY,
              minY: 0,
              gridData: const FlGridData(show: false, drawVerticalLine: false),
              titlesData: FlTitlesData(
                show: true,
                topTitles:
                    const AxisTitles(sideTitles: SideTitles(showTitles: false)),
                rightTitles:
                    const AxisTitles(sideTitles: SideTitles(showTitles: false)),
                leftTitles: AxisTitles(
                  sideTitles: SideTitles(
                      interval: formattedMaxY,
                      reservedSize: 60,
                      showTitles: state.isSensitiveDataVisible ? true : false),
                ),
                bottomTitles: AxisTitles(
                  sideTitles: SideTitles(
                    showTitles: true,
                    getTitlesWidget: getBottomTiles,
                  ),
                ),
              ),
              borderData: FlBorderData(
                  show: true,
                  border: const Border(
                      top: BorderSide(color: Colors.grey),
                      bottom: BorderSide(color: Colors.grey))),
              barGroups: myBarData.barData
                  .map(
                    (individualbar) => BarChartGroupData(
                      x: individualbar.x, //individualbar.x,

                      barRods: individualbar.x == 0
                          ? [
                              BarChartRodData(
                                toY: individualbar.y,
                                color: CooperadoColors.limaColorDark,
                                width: 30,
                                borderRadius: const BorderRadius.only(
                                  topLeft: Radius.circular(3),
                                  topRight: Radius.circular(3),
                                ),
                              ),
                            ]
                          : [
                              BarChartRodData(
                                toY: individualbar.y,
                                color: CooperadoColors.orange,
                                width: 30,
                                borderRadius: const BorderRadius.only(
                                  topLeft: Radius.circular(3),
                                  topRight: Radius.circular(3),
                                ),
                              ),
                            ],
                    ),
                  )
                  .toList(),
            ),
          ),
        );
      },
    );
  }
}
