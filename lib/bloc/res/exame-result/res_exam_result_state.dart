import 'package:cooperado_minha_unimed/models/res/res_exam_result_model.dart';
import 'package:equatable/equatable.dart';

abstract class ResExamResultState extends Equatable {
  const ResExamResultState();

  @override
  List<Object> get props => [];
}

class InitialResExamResultState extends ResExamResultState {}

class LoadingResExamResultState extends ResExamResultState {
  @override
  List<Object> get props => [];
}

class NoDataResExamResultState extends ResExamResultState {
  @override
  List<Object> get props => [];
}

class ErrorResExamResultState extends ResExamResultState {
  final String message;

  @override
  List<Object> get props => [message];

  const ErrorResExamResultState({required this.message});
}

class LoadedResExamResultState extends ResExamResultState {
  final List<ResExamResultModel> listExamResult;

  @override
  List<Object> get props => [listExamResult];

  const LoadedResExamResultState({required this.listExamResult});
}
