import 'package:cooperado_minha_unimed/bloc/news/read/read_cubit.dart';
import 'package:cooperado_minha_unimed/colors.dart';
import 'package:cooperado_minha_unimed/shared/screen-transitions/fade.transition.dart';
import 'package:cooperado_minha_unimed/shared/utils/logger_print.dart';
import 'package:cooperado_minha_unimed/shared/vo/transparencia/demostrativo_resultado.dart';
import 'package:cooperado_minha_unimed/shared/widgets/alert.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:flutter_html_iframe/flutter_html_iframe.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../shared/widgets/pdf_view/pdf_view_platform.dart';

class ReadNews extends StatefulWidget {
  final Noticia noticiasPortal;
  const ReadNews({super.key, required this.noticiasPortal});
  @override
  ReadNewsState createState() => ReadNewsState();
}

class ReadNewsState extends State<ReadNews> {
  final logger = UnimedLogger(className: 'ReadNews');
  @override
  void initState() {
    context.read<ReadCubit>().readNewsEvent(widget.noticiasPortal);
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.noticiasPortal.titulo!),
      ),
      body: SafeArea(
        child: _readNews(),
      ),
    );
  }

  Widget _readNews() {
    return BlocBuilder<ReadCubit, ReadState>(
      builder: (context, state) {
        if (state is DoneReadNewsState) {
          return SingleChildScrollView(
            child: Html(
              data: widget.noticiasPortal.content,
              extensions: const [
                IframeHtmlExtension(),
              ],
              onLinkTap: (url, attributes, element) {
                if (url != null) _onTapLink(url);
              },
            ),
          );
        } else if (state is ErrorReadNewsState) {
          return Text(
            state.message,
            textAlign: TextAlign.center,
          );
        } else {
          return const SpinKitCircle(
            color: CooperadoColors.tealGreen,
          );
        }
      },
    );
  }

  void _openPDFView(String url) {
    Navigator.push(context, FadeRoute(page: PDFViewPlatform(url)));
  }

  _onTapLink(String url) async {
    try {
      final String urlType = url.split('.').last;
      if (urlType.toLowerCase() == 'pdf') {
        _openPDFView(url);
        return;
      } else {
        await _launchURL(url);
        return;
      }
    } catch (e) {
      showDialog(
        context: context,
        builder: (context) => CooperadoAlertDialog(
          textWidget: const Text('Não foi possível abrir o Link',
              textAlign: TextAlign.center),
          onPressed: () {
            if (Navigator.canPop(context)) {
              Navigator.pop(context);
            }
          },
        ),
      );
    }
  }

  Future _launchURL(String url) async {
    logger.i('launchURL - abrindo url $url');
    if (await canLaunchUrl(Uri.parse(url))) {
      await launchUrl(Uri.parse(url), mode: LaunchMode.externalApplication);
    } else {
      throw 'Unable to open url : $url';
    }
  }
}
