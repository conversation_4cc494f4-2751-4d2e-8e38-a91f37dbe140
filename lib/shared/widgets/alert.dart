// ignore_for_file: must_be_immutable

import 'package:cooperado_minha_unimed/colors.dart';
import 'package:flutter/material.dart';

class Alert {
  static void open(
    BuildContext context, {
    String? title,
    String text = '',
    String textButtonClose = 'Fechar',
    List<String>? lines,
    List<Widget>? actions,
    Function? callbackClose,
    bool barrierDismissible = false,
  }) async {
    List<Widget> texts = [
      Text(
        text,
        style: const TextStyle(
            color: CooperadoColors.grayDark2, fontWeight: FontWeight.bold),
      )
    ];
    List<Widget> actionsList = [];

    bool disableButton = false;
    if (text.isNotEmpty && lines != null && lines.isNotEmpty) {
      texts.addAll(
        lines.map(
          (l) => Text(
            l,
            style: TextStyle(
              color: unimedGreen.shade900,
            ),
          ),
        ),
      );
    }

    if (actions != null && actions.isNotEmpty) {
      actionsList = actions;
    }

    actionsList.add(
      StatefulBuilder(
        builder: (context, setState) {
          return ElevatedButton(
            style: ElevatedButton.styleFrom(
                textStyle: const TextStyle(color: Colors.white),
                backgroundColor: CooperadoColors.tealGreen,
                disabledBackgroundColor:
                    CooperadoColors.tealGreen.withOpacity(0.4)),
            onPressed: disableButton
                ? null
                : () {
                    setState(
                      () {
                        disableButton = !disableButton;
                      },
                    );
                    Navigator.of(context).pop();

                    if (callbackClose != null) {
                      callbackClose();
                    }
                  },
            child: Text(textButtonClose),
          );
        },
      ),
    );

    return showGeneralDialog<void>(
      context: context,
      barrierDismissible: barrierDismissible,
      barrierColor: Colors.black.withOpacity(0.5),
      transitionDuration: const Duration(milliseconds: 400),
      pageBuilder: (BuildContext context, Animation animation,
          Animation secondaryAnimation) {
        return Container();
      },
      transitionBuilder: (BuildContext context, a1, a2, widget) {
        final curvedValue = Curves.easeInOutBack.transform(a1.value) - 1.0;

        return PopScope(
          canPop: barrierDismissible,
          child: Transform(
            transform: Matrix4.translationValues(0.0, curvedValue * 200, 0.0),
            child: Opacity(
              opacity: a1.value,
              child: AlertDialog(
                backgroundColor: cooperadoTealGreen.shade50,
                title: Text(
                  '$title',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: cooperadoTealGreen.shade900,
                  ),
                ),
                content: SingleChildScrollView(
                  child: Wrap(
                    alignment: WrapAlignment.center,
                    children: texts,
                  ),
                ),
                actions: actionsList,
              ),
            ),
          ),
        );
      },
    );
  }

  static void openAlternativeLayout(
    BuildContext context, {
    Widget? title,
    String text = '',
    String textButtonClose = 'Fechar',
    List<String>? lines,
    Widget? action,
    Function? callbackClose,
    bool barrierDismissible = false,
  }) async {
    List<Widget> texts = [
      Text(
        text,
        textAlign: TextAlign.center,
        style: const TextStyle(color: CooperadoColors.grayDark2),
      )
    ];
    List<Widget> actions = [];
    if (text.isNotEmpty && lines != null && lines.isNotEmpty) {
      texts.addAll(lines.map((l) => Text(
            l,
            style: TextStyle(
              color: unimedGreen.shade900,
            ),
          )));
    }

    if (action != null) {
      actions.add(action);
    }
    actions.add(const SizedBox(
      width: 8,
    ));
    actions.add(
      Expanded(
          child: ElevatedButton(
        style: ElevatedButton.styleFrom(
          foregroundColor: Colors.white,
          backgroundColor: CooperadoColors.redCancel,
        ),
        child: Text(textButtonClose),
        onPressed: () {
          Navigator.of(context).pop();

          if (callbackClose != null) {
            callbackClose();
          }
        },
      )),
    );

    List<Widget> rowActions = [];
    rowActions.add(Padding(
        padding: const EdgeInsets.symmetric(horizontal: 8),
        child: Row(children: actions)));

    return showGeneralDialog<void>(
      context: context,
      barrierDismissible: barrierDismissible,
      barrierColor: Colors.black.withOpacity(0.5),
      transitionDuration: const Duration(milliseconds: 400),
      pageBuilder: (BuildContext context, Animation animation,
          Animation secondaryAnimation) {
        return Container();
      },
      transitionBuilder: (BuildContext context, a1, a2, widget) {
        final curvedValue = Curves.easeInOutBack.transform(a1.value) - 1.0;

        return PopScope(
          canPop: barrierDismissible,
          child: Transform(
            transform: Matrix4.translationValues(0.0, curvedValue * 200, 0.0),
            child: Opacity(
              opacity: a1.value,
              child: AlertDialog(
                //backgroundColor: unimedGreen.shade50,
                title: title,
                content: SingleChildScrollView(
                  child: Wrap(
                    alignment: WrapAlignment.center,
                    children: texts,
                  ),
                ),
                actions: rowActions,
              ),
            ),
          ),
        );
      },
    );
  }
}

class CooperadoAlertDialog extends StatelessWidget {
  CooperadoAlertDialog({
    super.key,
    required this.onPressed,
    required this.textWidget,
    this.iconData = Icons.info_outline,
    this.textButton = "Ok",
    this.colorIcon = CooperadoColors.tealGreen,
    this.title,
  });

  final IconData iconData;
  final VoidCallback onPressed;
  final Widget textWidget;
  final String textButton;
  final Color colorIcon;
  Widget? title;

  @override
  Widget build(context) {
    return AlertDialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(15.0)),
      title:
          title ?? Center(child: Icon(iconData, size: 70.0, color: colorIcon)),
      content: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: <Widget>[
            textWidget,
            const Divider(
              height: 5.0,
              color: Colors.transparent,
            ),
            const SizedBox(height: 10.0),
            ElevatedButton(
                style: ElevatedButton.styleFrom(
                  textStyle: const TextStyle(
                    color: Colors.white,
                  ),
                  backgroundColor: CooperadoColors.green,
                ),
                onPressed: onPressed,
                child: Text(textButton)),
          ]),
      backgroundColor: Colors.white,
    );
  }
}
