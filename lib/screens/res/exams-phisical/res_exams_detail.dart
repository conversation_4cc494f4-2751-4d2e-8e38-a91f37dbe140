
import 'package:cooperado_minha_unimed/colors.dart';
import 'package:cooperado_minha_unimed/models/res/res_brasil_detail_exam_phisical_model.dart';
import 'package:flutter/material.dart';

class ResExamsDetail extends StatelessWidget {
  final List<ResDetailExameFisicoModel> resExamesFisicosDetailModel;

  const ResExamsDetail({super.key, required this.resExamesFisicosDetailModel});

  @override
  Widget build(BuildContext context) {
   return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 10),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Center(
            child: Container(
              height: 4,
              width: 100,
              margin: const EdgeInsets.symmetric(
                vertical: 10,
              ),
              decoration: BoxDecoration(
                color: CooperadoColors.grayLight3,
                borderRadius: BorderRadius.circular(2),
              ),
            ),
          ),
          const SizedBox(height: 10),
          const Padding(
              padding: EdgeInsets.only(top: 0, left: 10),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Exame físico',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: CooperadoColors.blackText,
                    ),
                  )
                ],
              )),
          const SizedBox(height: 5),
          Expanded(
            child: Padding(
              padding: const EdgeInsets.all(10.0),
              child: Scrollbar(
                trackVisibility: true,
                thumbVisibility: true,
                child: SingleChildScrollView(
                  child: Wrap(
                    
                    spacing: 25,
                    runSpacing: 10,

                    children: resExamesFisicosDetailModel.map((detail) {
                      return Padding(
                        padding: const EdgeInsets.only(right:  10.0),
                        child: Column(
                         crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                          Text.rich(
  TextSpan(
    children: [
      TextSpan(
        text: '${detail.nome}: ',
        style: const TextStyle(
          color: CooperadoColors.blackText,
          fontSize: 15,
          fontWeight: FontWeight.w600,
          height: 3,
        ),
      ),
      TextSpan(
        text: detail.valor.isNotEmpty == true ? detail.valor : ' -- ',
        style: const TextStyle(
          color: CooperadoColors.blackText,
          fontSize: 15,
          fontWeight: FontWeight.normal,
          height: 3,
        ),
      ),
      if (detail.unidadeMedida.isNotEmpty == true)
        TextSpan(
          text: ' ${detail.unidadeMedida}  ',
          style: const TextStyle(
            color: CooperadoColors.blackText,
            fontSize: 15,
            fontWeight: FontWeight.normal,
            height: 3,
          ),
        ),
    ],
  ),
),
                          ],
                        ),
                      );
                    }).toList(),
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
