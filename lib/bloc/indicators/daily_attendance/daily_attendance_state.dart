part of 'daily_attendance_cubit.dart';

abstract class DailyAttendanceState extends Equatable {
  const DailyAttendanceState();

  @override
  List<Object?> get props => [];
}

class InitialDailyAttendance extends DailyAttendanceState {}

class LoadingDailyAttendanceState extends DailyAttendanceState {
  @override
  List<Object> get props => [];
}

class ErrorDailyAttendanceState extends DailyAttendanceState {
  final String? message;
  @override
  List<Object?> get props => [message];
  const ErrorDailyAttendanceState(this.message);
}

class LoadedDailyAttendanceState extends DailyAttendanceState {
  final List<ResponseDailyAttendance> list;
  final int services;
  @override
  List<Object> get props => [list, services];

  const LoadedDailyAttendanceState(this.list, this.services);
}
