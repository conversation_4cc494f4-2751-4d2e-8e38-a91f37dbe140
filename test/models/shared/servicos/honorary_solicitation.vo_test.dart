import 'package:cooperado_minha_unimed/shared/vo/servicos/honorary_solicitation.vo.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('HonorarySolicitation', () {
    test('fromJ<PERSON> should parse correctly', () {
      final json = {
        'numeroSolicitacao': 123,
        'prestadorPF': '<PERSON>',
        'participacao': {'codigo': 1, 'descricao': 'Participação Teste'},
        'dataSolicitacao': '2023-01-01',
        'observacaoPrestador': 'Observação Teste',
        'situacao': 'Aprovado',
        'dataAuditoria': '2023-01-02',
        'observacaoAuditor': 'Auditor Teste',
        'numeroNota': 456,
        'guiaReferencia': 789,
        'servicosSolicitados': [
          {
            'codigo': 1,
            'servico': 'Serviço Teste',
            'quantSolicitada': 10,
            'quantAuditada': 8
          }
        ],
        'hospital': 'Hospital Teste'
      };

      final solicitation = HonorarySolicitation.fromJson(json);

      expect(solicitation.numeroSolicitacao, 123);
      expect(solicitation.prestadorPF, '<PERSON>');
      expect(solicitation.participacao?.codigo, 1);
      expect(solicitation.participacao?.descricao, 'Participação Teste');
      expect(solicitation.dataSolicitacao, '2023-01-01');
      expect(solicitation.observacaoPrestador, 'Observação Teste');
      expect(solicitation.situacao, 'Aprovado');
      expect(solicitation.dataAuditoria, '2023-01-02');
      expect(solicitation.observacaoAuditor, 'Auditor Teste');
      expect(solicitation.numeroNota, 456);
      expect(solicitation.guiaReferencia, 789);
      expect(solicitation.servicosSolicitados?.length, 1);
      expect(solicitation.servicosSolicitados?.first.codigo, 1);
      expect(solicitation.servicosSolicitados?.first.servico, 'Serviço Teste');
      expect(solicitation.servicosSolicitados?.first.quantSolicitada, 10);
      expect(solicitation.servicosSolicitados?.first.quantAuditada, 8);
      expect(solicitation.hospital, 'Hospital Teste');
    });

    test('toJson should serialize correctly', () {
      final solicitation = HonorarySolicitation(
        numeroSolicitacao: 123,
        prestadorPF: 'John Doe',
        participacao: Participacao(codigo: 1, descricao: 'Participação Teste'),
        dataSolicitacao: '2023-01-01',
        observacaoPrestador: 'Observação Teste',
        situacao: 'Aprovado',
        dataAuditoria: '2023-01-02',
        observacaoAuditor: 'Auditor Teste',
        numeroNota: 456,
        guiaReferencia: 789,
        servicosSolicitados: [
          ServicosSolicitados(
              codigo: 1,
              servico: 'Serviço Teste',
              quantSolicitada: 10,
              quantAuditada: 8)
        ],
        hospital: 'Hospital Teste',
      );

      final json = solicitation.toJson();

      expect(json['numeroSolicitacao'], 123);
      expect(json['prestadorPF'], 'John Doe');
      expect(json['participacao']['codigo'], 1);
      expect(json['participacao']['descricao'], 'Participação Teste');
      expect(json['dataSolicitacao'], '2023-01-01');
      expect(json['observacaoPrestador'], 'Observação Teste');
      expect(json['situacao'], 'Aprovado');
      expect(json['dataAuditoria'], '2023-01-02');
      expect(json['observacaoAuditor'], 'Auditor Teste');
      expect(json['numeroNota'], 456);
      expect(json['guiaReferencia'], 789);
      expect(json['servicosSolicitados'].length, 1);
      expect(json['servicosSolicitados'][0]['codigo'], 1);
      expect(json['servicosSolicitados'][0]['servico'], 'Serviço Teste');
      expect(json['servicosSolicitados'][0]['quantSolicitada'], 10);
      expect(json['servicosSolicitados'][0]['quantAuditada'], 8);
      expect(json['hospital'], 'Hospital Teste');
    });
  });

  group('Participacao', () {
    test('fromJson should parse correctly', () {
      final json = {'codigo': 1, 'descricao': 'Participação Teste'};

      final participacao = Participacao.fromJson(json);

      expect(participacao.codigo, 1);
      expect(participacao.descricao, 'Participação Teste');
    });

    test('toJson should serialize correctly', () {
      final participacao =
          Participacao(codigo: 1, descricao: 'Participação Teste');

      final json = participacao.toJson();

      expect(json['codigo'], 1);
      expect(json['descricao'], 'Participação Teste');
    });
  });

  group('ServicosSolicitados', () {
    test('fromJson should parse correctly', () {
      final json = {
        'codigo': 1,
        'servico': 'Serviço Teste',
        'quantSolicitada': 10,
        'quantAuditada': 8
      };

      final servico = ServicosSolicitados.fromJson(json);

      expect(servico.codigo, 1);
      expect(servico.servico, 'Serviço Teste');
      expect(servico.quantSolicitada, 10);
      expect(servico.quantAuditada, 8);
    });

    test('toJson should serialize correctly', () {
      final servico = ServicosSolicitados(
        codigo: 1,
        servico: 'Serviço Teste',
        quantSolicitada: 10,
        quantAuditada: 8,
      );

      final json = servico.toJson();

      expect(json['codigo'], 1);
      expect(json['servico'], 'Serviço Teste');
      expect(json['quantSolicitada'], 10);
      expect(json['quantAuditada'], 8);
    });
  });
}
