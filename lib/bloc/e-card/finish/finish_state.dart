import 'package:cooperado_minha_unimed/models/e-card/finish.model.dart';
import 'package:equatable/equatable.dart';

abstract class EcardFinishState extends Equatable {
  const EcardFinishState();

  @override
  List<Object> get props => [];
}

class InitialEcardFinishState extends EcardFinishState {}

class LoadingEcardFinishState extends EcardFinishState {
  @override
  List<Object> get props => [];
}

class ErrorEcardFinishState extends EcardFinishState {
  final String message;

  @override
  List<Object> get props => [message];

  const ErrorEcardFinishState({required this.message});
}

class LoadedEcardFinishState extends EcardFinishState {
  final EcardFinishCardModel ecardFinishCardModel;

  @override
  List<Object> get props => [ecardFinishCardModel];

  const LoadedEcardFinishState({required this.ecardFinishCardModel});
}
