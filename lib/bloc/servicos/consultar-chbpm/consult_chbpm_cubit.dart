import 'package:bloc/bloc.dart';
import 'package:cooperado_minha_unimed/shared/api/services.api.dart';
import 'package:cooperado_minha_unimed/shared/locator.dart';
import 'package:cooperado_minha_unimed/shared/vo/servicos/procedure_list.vo.dart';
import 'package:flutter/material.dart';
import 'consult_chbpm_state.dart';

class ConsultChbpmCubit extends Cubit<ConsultChbpmState> {
  ConsultChbpmCubit() : super(ConsultChbpmInitial());

  getConsultChbpmEvent(String keySearch, int? pagina) async {
    emit(LoadingGetConsultChbpmState());
    try {
      final ProcedureList procedureList = await Locator.instance!<ServicesApi>()
          .getMedicProcedures(keySearch: keySearch, pagina: pagina);

      emit(DoneGetConsultChbpmState(procedureList));
    } catch (ex) {
      debugPrint(ex.toString());
      emit(ErrorGetConsultChbpmState('$ex'));
    }
  }

  setInitialState() async {
    emit(ConsultChbpmInitial());
  }
}
