import 'package:bloc/bloc.dart';
import 'package:cooperado_minha_unimed/colors.dart';
import 'package:cooperado_minha_unimed/screens/indicadores/daily-attendance/daily_graphic.dart';
import 'package:cooperado_minha_unimed/shared/api/indicadores.api.dart';
import 'package:cooperado_minha_unimed/shared/locator.dart';
import 'package:cooperado_minha_unimed/shared/vo/indicadores/daily_attendance.dart';
import 'package:equatable/equatable.dart';
import 'package:intl/intl.dart';

part 'daily_attendance_state.dart';

class DailyAttendanceCubit extends Cubit<DailyAttendanceState> {
  DailyAttendanceCubit() : super(InitialDailyAttendance());

  List<DailyAttendanceSerie> _listDaily = [];
  List<DailyAttendanceSerie> get listDaily => _listDaily;

  List<ResponseDailyAttendance> _listDailyAttendance = [];
  int _services = 0;
  double services = 0.0, consults = 0.0, fees = 0.0;

  String? dateBegin;
  String? dateEnd;
  listDailyAttendance() async {
    try {
      emit(LoadingDailyAttendanceState());
      _initialize();

      final dateNow = DateTime.now();
      dateBegin = "01-${DateFormat("MM-yyyy").format(dateNow)}";
      dateEnd = DateFormat("dd-MM-yyyy").format(dateNow);

      _listDailyAttendance = await Locator.instance!<IndicadoresApi>()
          .getDailyAttendance(dateBegin: dateBegin!, dateEnd: dateEnd!);

      if (_listDailyAttendance.isEmpty) {
        _listDailyAttendance = await _getFromBeforeMonth(dateNow);
      }

      for (ResponseDailyAttendance value in _listDailyAttendance) {
        _mount(value);
      }

      emit(LoadedDailyAttendanceState(_listDailyAttendance, _services));
    } catch (e) {
      emit(ErrorDailyAttendanceState(e.toString()));
    }
  }

  Future<List<ResponseDailyAttendance>> _getFromBeforeMonth(dateNow) async {
    final lastDay = DateTime(dateNow.year, dateNow.month, 0).day;
    dateBegin =
        "01-${DateFormat("MM-yyyy").format(DateTime(dateNow.year, dateNow.month - 1, dateNow.day))}";

    dateEnd = DateFormat("dd-MM-yyyy").format(
      DateTime(dateNow.year, dateNow.month - 1, lastDay),
    );
    final listDaily = await Locator.instance!<IndicadoresApi>()
        .getDailyAttendance(dateBegin: dateBegin!, dateEnd: dateEnd!);
    return listDaily;
  }

  void _initialize() {
    _services = 0;
    services = 0.0;
    consults = 0.0;
    fees = 0.0;
    _listDaily.clear();
    _listDaily = <DailyAttendanceSerie>[
      DailyAttendanceSerie(
        quantity: 0,
        title: "Serviços Diversos",
        type: 1,
        barColor: CooperadoColors.tealGreen,
      ),
      /* charts.ColorUtil.fromDartColor(CooperadoColors.purple)) */
      DailyAttendanceSerie(
          quantity: 0,
          title: "Consultas",
          type: 2,
          barColor: CooperadoColors.greenLight4),
      DailyAttendanceSerie(
          quantity: 0,
          title: "Honorário",
          type: 3,
          barColor: CooperadoColors.orange),
    ];
  }

  void _mount(ResponseDailyAttendance responseDailyAttendance) {
    responseDailyAttendance.procedimentos!.asMap().forEach((index, element) {
      if (element.tipo!.descricao == 'SERVIÇOS DIVERSOS') {
        services += element.quantidade!;
      } else if (element.tipo!.descricao == 'HONORÁRIOS') {
        fees += element.quantidade!;
      } else {
        consults += element.quantidade!;
      }
      _services += element.quantidade!;
    });

    if (services > 0) {
      _listDaily.replaceRange(0, 1, [
        DailyAttendanceSerie(
            title: "Serviço Diversos",
            quantity: services,
            type: 1,
            barColor: CooperadoColors.tealGreen)
      ]);
    }
    if (consults > 0) {
      _listDaily.replaceRange(1, 2, [
        DailyAttendanceSerie(
            title: "Consultas",
            quantity: consults,
            type: 2,
            barColor: CooperadoColors.greenLight4)
      ]);
    }

    if (fees > 0) {
      _listDaily.replaceRange(2, 3, [
        DailyAttendanceSerie(
            title: "Honorário",
            quantity: fees,
            type: 3,
            barColor: CooperadoColors.green)
      ]);
    }
  }
}
