import 'package:cooperado_minha_unimed/colors.dart';
import 'package:flutter/material.dart';

class HeaderConfigButtom extends StatefulWidget {
  final IconData? icon;
  final String? label;
  final bool? active;
  final Function? action;

  const HeaderConfigButtom(
      {super.key, this.active, this.action, this.icon, this.label});
  @override
  HeaderConfigButtomState createState() => HeaderConfigButtomState();
}

class HeaderConfigButtomState extends State<HeaderConfigButtom> {
  @override
  Widget build(BuildContext context) {
    final Size size = MediaQuery.of(context).size;

    return Material(
      borderRadius: const BorderRadius.only(
        bottomLeft: Radius.circular(10),
        topLeft: Radius.circular(10),
        topRight: Radius.circular(10),
      ),
      child: InkWell(
        onTap: widget.action as void Function()?,
        borderRadius: const BorderRadius.only(
          bottomLeft: Radius.circular(10),
          topLeft: Radius.circular(10),
          topRight: Radius.circular(10),
        ),
        child: Ink(
          height: 100,
          width: size.width * 0.3,
          padding: const EdgeInsets.symmetric(vertical: 8.0, horizontal: 4.0),
          decoration: const BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.only(
              bottomLeft: Radius.circular(10),
              topLeft: Radius.circular(10),
              topRight: Radius.circular(10),
            ),
          ),
          child: Stack(
            alignment: Alignment.center,
            children: [
              Column(
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children: [
                  Icon(
                    widget.icon,
                    size: 35,
                    color: CooperadoColors.tealGreen,
                  ),
                  Text(widget.label!, textAlign: TextAlign.center),
                ],
              ),
              if (!widget.active!)
                Opacity(
                  opacity: 0.7,
                  child: Container(
                    height: 100,
                    decoration: const BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.only(
                        bottomLeft: Radius.circular(10),
                        topLeft: Radius.circular(10),
                        topRight: Radius.circular(10),
                      ),
                    ),
                  ),
                )
            ],
          ),
        ),
      ),
    );
  }
}
