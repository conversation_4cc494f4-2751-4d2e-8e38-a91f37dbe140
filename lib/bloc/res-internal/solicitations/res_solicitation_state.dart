part of 'res_solicitation_cubit.dart';

abstract class ResSolicitationState extends Equatable {
  const ResSolicitationState();

  @override
  List<Object> get props => [];
}

class InitialResSolicitationState extends ResSolicitationState {}

class LoadingResSolicitationState extends ResSolicitationState {
  @override
  List<Object> get props => [];
}

class ErrorResSolicitationState extends ResSolicitationState {
  final String message;

  @override
  List<Object> get props => [message];

  const ErrorResSolicitationState({required this.message});
}

class LoadedResSolicitationState extends ResSolicitationState {
  final List<ResSolicitationModel> listSolicitations;

  @override
  List<Object> get props => [listSolicitations];

  const LoadedResSolicitationState({required this.listSolicitations});
}
