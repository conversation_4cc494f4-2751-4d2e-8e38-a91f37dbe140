import 'dart:convert';

import 'package:cooperado_minha_unimed/models/user.model.dart';
import 'package:cooperado_minha_unimed/shared/api/auth.api.dart';
import 'package:cooperado_minha_unimed/shared/exceptions.dart';
import 'package:cooperado_minha_unimed/shared/flavor_config.dart';
import 'package:cooperado_minha_unimed/shared/locator.dart';
import 'package:cooperado_minha_unimed/shared/messages.exceptions.dart';
import 'package:cooperado_minha_unimed/shared/utils/logger_print.dart';
import 'package:cooperado_minha_unimed/shared/utils/string_utils.dart';
import 'package:cooperado_minha_unimed/shared/vo/transparencia/custos_assistenciais.vo.dart';
import 'package:cooperado_minha_unimed/shared/vo/transparencia/demostrativo_resultado.dart';
import 'package:cooperado_minha_unimed/shared/vo/transparencia/indices.dart';
import 'package:cooperado_minha_unimed/shared/vo/transparencia/producao_medica_resumida.vo.dart';
import 'package:cooperado_minha_unimed/shared/vo/transparencia/quantitativo_beneficiarios.vo.dart';
import 'package:http_client/http_client.dart';
import 'package:intl/intl.dart';

class TransparenciaApi {
  final UnimedHttpClient httpClient;

  TransparenciaApi(this.httpClient);

  final logger = UnimedLogger(className: 'TransparenciaApi');
  final Duration timeout = const Duration(seconds: 30);

  Future<List<RetornoProducaoMedica>> getHistoricoProducao({
    String? tipoProducao,
    required DateTime? dataInicio,
    DateTime? dataFim,
  }) async {
    UserCredentials? credentials =
        await Locator.instance!.get<AuthApi>().getCredentials();

    final token = credentials != null
        ? await User.createToken(credentials: credentials)
        : "";
    final tokenPerfilApps =
        await Locator.instance!.get<AuthApi>().tokenPerfilApps();

    final startDateFormatted = DateFormat('dd-MM-yyyy').format(dataInicio!);

    try {
      final url =
          '${FlavorConfig.instance!.values.profilePermissions.url}app/cooperado/transparencia/producao-medica-resumida/inicio/$startDateFormatted/${dataFim == null ? '' : 'fim/${DateFormat('dd-MM-yyyy').format(dataFim)}/'}tipo/$tipoProducao?tokenPortal=$token&crm=${credentials?.crm}';

      final response = await httpClient.get(
        Uri.parse(url),
        headers: {
          "Content-Type": "application/json",
          "Authorization": "Bearer $tokenPerfilApps",
        },
      );

      final data = jsonDecode(utf8.decode(response.bodyBytes));
      if (response.statusCode == 200) {
        final collection = (data["retorno"] as List)
            .map<RetornoProducaoMedica>(
                (e) => RetornoProducaoMedica.fromJson(e))
            .toList();

        logger.d('getHistoricoProducao success list ${collection.length}');
        return collection;
      } else if (response.statusCode == 503) {
        logger.e(
            'getHistoricoProducao statusCode : ${response.statusCode} ${response.body}');
        throw TransparenciaException(
            'Não foi possível atender sua solicitação no momento. Tente novamente mais tarde.');
      } else {
        final message = data['message'] ?? 'Não foi possível no momento.';
        logger.e(
            'getHistoricoProducao statusCode : ${response.statusCode} ${response.body}');
        throw TransparenciaException('$message');
      }
    } on UnimedException catch (ex) {
      logger.e('getHistoricoProducao ${ex.runtimeType}: $ex');
      throw IndicadoresException(ex.message);
    } catch (ex) {
      logger.e('getHistoricoProducao exception : $ex');
      throw TransparenciaException('Não foi possível no momento.');
    }
  }

  Future<VOIndicatorModel> getIndices(String descricao) async {
    UserCredentials? credentials =
        await Locator.instance!.get<AuthApi>().getCredentials();
    final token = credentials != null
        ? await User.createToken(credentials: credentials)
        : "";

    try {
      final tokenPerfilApps =
          await Locator.instance!.get<AuthApi>().tokenPerfilApps();
      final url =
          '${FlavorConfig.instance!.values.profilePermissions.url}app/cooperado/transparencia/indices/descricao/$descricao?tokenPortal=$token&crm=${credentials?.crm}';

      final response = await httpClient.get(
        Uri.parse(url),
        headers: {
          "Content-Type": "application/json",
          "Authorization": "Bearer $tokenPerfilApps",
        },
      );

      if (response.statusCode == 200) {
        final data = VOIndicatorModel.fromJson(
            jsonDecode(utf8.decode(response.bodyBytes))['retorno']);

        logger.d(
            'getIndices success ${StringUtils.limitString(response.body, 200)}');

        return data;
      } else if (response.statusCode == 503) {
        logger.e(
            'getIndices statusCode : ${response.statusCode} ${response.body}');
        throw TransparenciaException(
            'Não foi possível atender sua solicitação no momento. Tente novamente mais tarde.');
      } else {
        String message =
            jsonDecode(utf8.decode(response.bodyBytes))['message'] ??
                MessageException.general;
        logger.e(
            'getIndices statusCode : ${response.statusCode} body: ${response.body}');
        throw TransparenciaException(message);
      }
    } on UnimedException catch (ex) {
      logger.e('getIndices ${ex.runtimeType} : $ex');
      throw TransparenciaException(ex.message);
    } catch (ex) {
      logger.e('getIndices exception : $ex');
      throw ServicesException("Não foi possível no momento.");
    }
  }

  Future<DemonstrativoResultadosVO> getDemonstrativoResultados(
      {int? pagina}) async {
    UserCredentials? credentials =
        await Locator.instance!.get<AuthApi>().getCredentials();
    final token = credentials != null
        ? await User.createToken(credentials: credentials)
        : "";

    try {
      final tokenPerfilApps =
          await Locator.instance!.get<AuthApi>().tokenPerfilApps();
      final url =
          '${FlavorConfig.instance!.values.profilePermissions.url}app/cooperado/transparencia/noticias/demonstrativo-resultado/pagina/$pagina?tokenPortal=$token&crm=${credentials?.crm}';

      final response = await httpClient.get(
        Uri.parse(url),
        headers: {
          "Content-Type": "application/json",
          "Authorization": "Bearer $tokenPerfilApps",
        },
      );
      final data = jsonDecode(utf8.decode(response.bodyBytes));
      if (response.statusCode == 200) {
        final demonstrative =
            DemonstrativoResultadosVO.fromJson(data['retorno']);

        logger.d(
            "getDemonstrativoResultados success ${StringUtils.limitString(response.body, 200)}");

        return demonstrative;
      } else if (response.statusCode == 503) {
        logger.e(
            'getDemonstrativoResultados statusCode : ${response.statusCode} $data');
        throw TransparenciaException(
            'Não foi possível atender sua solicitação no momento. Tente novamente mais tarde.');
      } else {
        logger.e(
            'getDemonstrativoResultados statusCode : ${response.statusCode} ${response.body}');
        throw TransparenciaException(data["message"] ??
            data["mensagem"] ??
            'Não foi possível no momento.');
      }
    } on UnimedException catch (ex) {
      logger.e('getDemonstrativoResultados ${ex.runtimeType} : $ex');
      throw TransparenciaException(ex.message);
    } catch (ex) {
      logger.e('getDemonstrativoResultados exception : $ex');
      throw ServicesException("Não foi possível no momento.");
    }
  }

  Future<CustosAssistenciaisVO> getCustosAssistenciais(
      {required String dataRelatorio}) async {
    UserCredentials? credentials =
        await Locator.instance!.get<AuthApi>().getCredentials();
    final token = credentials != null
        ? await User.createToken(credentials: credentials)
        : "";

    try {
      final tokenPerfilApps =
          await Locator.instance!.get<AuthApi>().tokenPerfilApps();
      final url =
          '${FlavorConfig.instance!.values.profilePermissions.url}app/cooperado/custo-assistencial/data/01-$dataRelatorio?tokenPortal=$token&crm=${credentials?.crm}';
      final response = await httpClient.get(
        Uri.parse(url),
        headers: {
          "Content-Type": "application/json",
          "Authorization": "Bearer $tokenPerfilApps",
        },
      );

      if (response.statusCode == 200) {
        final data = CustosAssistenciaisVO.fromJson(
            jsonDecode(utf8.decode(response.bodyBytes))['retorno']);

        logger.d(
            'getCustosAssistenciais success ${StringUtils.limitString(response.body, 200)}');

        return data;
      } else if (response.statusCode == 503) {
        logger.e(
            'getCustosAssistenciais statusCode : ${response.statusCode} ${response.body}');
        throw TransparenciaException(
            'Não foi possível atender sua solicitação no momento. Tente novamente mais tarde.');
      } else {
        final message =
            jsonDecode(utf8.decode(response.bodyBytes))["message"] ??
                MessageException.general;
        logger.e(
            'getCustosAssistenciais statusCode : ${response.statusCode} ${response.body}');
        throw TransparenciaException('$message');
      }
    } on UnimedException catch (ex) {
      logger.e('getCustosAssistenciais ${ex.runtimeType} : $ex');
      throw TransparenciaException(ex.message);
    } catch (ex) {
      logger.e('getCustosAssistenciais exception : $ex');
      throw ServicesException("Não foi possível no momento.");
    }
  }

  Future<QuantitativoBeneficiariosVO> getQuantitativoBeneficiarios() async {
    UserCredentials? credentials =
        await Locator.instance!.get<AuthApi>().getCredentials();
    final token = credentials != null
        ? await User.createToken(credentials: credentials)
        : "";

    try {
      final tokenPerfilApps =
          await Locator.instance!.get<AuthApi>().tokenPerfilApps();
      final url =
          '${FlavorConfig.instance!.values.profilePermissions.url}app/cooperado/transparencia/quantitativo-beneficiarios?tokenPortal=$token&crm=${credentials?.crm}';
      final response = await httpClient.get(
        Uri.parse(url),
        headers: {
          "Content-Type": "application/json",
          "Authorization": "Bearer $tokenPerfilApps",
        },
      );

      if (response.statusCode == 200) {
        final data = QuantitativoBeneficiariosVO.fromJson(
            jsonDecode(utf8.decode(response.bodyBytes))['retorno']);

        logger.d(
            'getQuantitativoBeneficiarios success ${StringUtils.limitString(response.body, 200)}');

        return data;
      } else {
        final message = jsonDecode(response.body)['message'] ??
            'Não foi possível atender sua solicitação no momento. Tente novamente mais tarde.';
        logger.e(
            'getQuantitativoBeneficiarios error - statusCode: ${response.statusCode} - ${response.body}');
        throw TransparenciaException('$message');
      }
    } on UnimedException catch (ex) {
      logger.e('getQuantitativoBeneficiarios ${ex.runtimeType} : $ex');
      throw TransparenciaException(ex.message);
    } catch (ex) {
      logger.e('getQuantitativoBeneficiarios exception : $ex');
      throw ServicesException("Não foi possível no momento.");
    }
  }
}
