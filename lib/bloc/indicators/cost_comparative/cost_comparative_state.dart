part of 'cost_comparative_cubit.dart';

abstract class CostComparativeState extends Equatable {
  const CostComparativeState();
}

class InitialCostComparativeState extends CostComparativeState {
  @override
  List<Object> get props => [];
}

class LoadingCostComparativeState extends CostComparativeState {
  @override
  List<Object> get props => [];
}

class ErrorCostComparativeState extends CostComparativeState {
  final String? message;
  @override
  List<Object?> get props => [message];
  const ErrorCostComparativeState(this.message);
}

class LoadedCostComparativeState extends CostComparativeState {
  final RetornoComparativoCustoVO costComparativeVO;
  @override
  List<Object> get props => [costComparativeVO];

  const LoadedCostComparativeState(this.costComparativeVO);
}

class LoadingCostComparativeSelectDate extends CostComparativeState {
  @override
  List<Object> get props => [];
}

class CostComparativeSelectDate extends CostComparativeState {
  final DateTime? date;
  @override
  List<Object?> get props => [date];

  const CostComparativeSelectDate(this.date);
}
