import 'package:bloc/bloc.dart';
import 'package:cooperado_minha_unimed/models/fiscal_council.model.dart';
import 'package:cooperado_minha_unimed/shared/api/fiscal_council.api.dart';
import 'package:cooperado_minha_unimed/shared/locator.dart';
import 'package:equatable/equatable.dart';

part 'send_form_state.dart';

class SendFormCubit extends Cubit<SendFormState> {
  SendFormCubit() : super(SendFormInitial());

  sendForm(String message, CouncilTopics councilTopics, String fone,
      bool anonymous) async {
    emit(LoadingSendMessage());
    try {
      final retorno = await Locator.instance!<FiscalCouncilApi>()
          .sendMessageCouncil(
              message: message,
              councilTopics: councilTopics,
              fone: fone,
              anonymous: anonymous);

      emit(DoneSendMessage(message: retorno));
    } catch (ex) {
      emit(ErrorSendMessage(message: '$ex'));
    }
  }
}
