part of 'res_exam_result_detail_cubit.dart';

abstract class ResExamResultDetailState extends Equatable {
  const ResExamResultDetailState();

  @override
  List<Object> get props => [];
}

class InitialResExamResultDetailState extends ResExamResultDetailState {}

class LoadingResExamResultDetailState extends ResExamResultDetailState {
  @override
  List<Object> get props => [];
}

class NoDataResExamResultDetailState extends ResExamResultDetailState {
  @override
  List<Object> get props => [];
}

class ErrorResExamResultDetailState extends ResExamResultDetailState {
  final String message;

  @override
  List<Object> get props => [message];

  const ErrorResExamResultDetailState({required this.message});
}

class LoadedResExamResultLaboratoryDetailState
    extends ResExamResultDetailState {
  final List<ResExamResultLaboratoryDetailModel>
      resExamResultLaboratoryDetailModel;
  final int index;

  @override
  List<Object> get props => [resExamResultLaboratoryDetailModel, index];

  const LoadedResExamResultLaboratoryDetailState(
      {required this.resExamResultLaboratoryDetailModel, required this.index});
}

class LoadedResExamResultImageDetailState extends ResExamResultDetailState {
  final List<ResExamResultImageDetailModel> resExamResultImageDetailModel;

  @override
  List<Object> get props => [resExamResultImageDetailModel];

  const LoadedResExamResultImageDetailState(
      {required this.resExamResultImageDetailModel});
}
