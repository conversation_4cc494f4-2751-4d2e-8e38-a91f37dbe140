import 'package:cooperado_minha_unimed/bloc/indicators/historical_production/historical_production_cubit.dart';
import 'package:cooperado_minha_unimed/colors.dart';
import 'package:cooperado_minha_unimed/screens/indicadores/historical-production/line_chart_historic.dart';
import 'package:cooperado_minha_unimed/shared/widgets/card_refresh.dart';
import 'package:cooperado_minha_unimed/shared/widgets/error-banner/error_banner.component.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';

class ProductionHistoryScreen extends StatefulWidget {
  final DateTime lastDate;
  const ProductionHistoryScreen({super.key, required this.lastDate});

  @override
  ProductionHistoryScreenState createState() => ProductionHistoryScreenState();
}

class ProductionHistoryScreenState extends State<ProductionHistoryScreen> {
  double total = 0.0;
  DateTime? selectedDateTime;

  @override
  void initState() {
    _init();
    super.initState();
  }

  void _init() {
    context
        .read<HistoricalProductionCubit>()
        .listHistoricalProduction(widget.lastDate);
  }

  @override
  Widget build(BuildContext context) {
    return CardRefresh(
      title: const Text("Histórico de Produção - Valor líquido",
          style: TextStyle(
            color: CooperadoColors.blackText,
            fontSize: 16,
            fontWeight: FontWeight.bold,
          )),
      refresh: _iconRefresh(),
      child: Column(
        children: [
          BlocBuilder<HistoricalProductionCubit, HistoricalProductionState>(
            builder: (context, state) {
              if (state is LoadedHistoricalProductionState) {
                return LineChartHistoric(data: state.list, animate: true);
              } else if (state is LoadingHistoricalProductionState) {
                return const SpinKitCircle(color: CooperadoColors.tealGreen);
              } else if (state is ErrorHistoricalProductionState) {
                return ErrorBanner(message: state.message);
              } else {
                return Container();
              }
            },
          ),
          const SizedBox(height: 10)
        ],
      ),
    );
  }

  Widget _iconRefresh() {
    return BlocBuilder<HistoricalProductionCubit, HistoricalProductionState>(
        builder: (context, state) {
      if (state is ErrorHistoricalProductionState) {
        return InkWell(child: const Icon(Icons.refresh), onTap: () => _init());
      } else {
        return Container();
      }
    });
  }
}
