import 'package:bloc/bloc.dart';
import 'package:cooperado_minha_unimed/shared/api/indicadores.api.dart';
import 'package:cooperado_minha_unimed/shared/exceptions.dart';
import 'package:cooperado_minha_unimed/shared/locator.dart';
import 'package:cooperado_minha_unimed/shared/vo/indicadores/production_history.dart';
import 'package:equatable/equatable.dart';

part 'historical_production_state.dart';

class HistoricalProductionCubit extends Cubit<HistoricalProductionState> {
  HistoricalProductionCubit() : super(InitialHistoricalProductionState());

  listHistoricalProduction(DateTime lastProduction) async {
    try {
      emit(LoadingHistoricalProductionState());
      final productionHistory = await Locator.instance!<IndicadoresApi>()
          .getProductionHistory(lastProduction);
      emit(LoadedHistoricalProductionState(productionHistory));
    } on IndicadoresException catch (e) {
      emit(ErrorHistoricalProductionState(e.message));
    } catch (e) {
      emit(ErrorHistoricalProductionState('$e'));
    }
  }
}
