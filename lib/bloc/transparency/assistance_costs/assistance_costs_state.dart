part of 'assistance_costs_cubit.dart';

abstract class AssistanceCostsState extends Equatable {
  const AssistanceCostsState();
}

class InitialAssistanceCostsState extends AssistanceCostsState {
  @override
  List<Object> get props => [];
}

class LoadingAssistanceCostsState extends AssistanceCostsState {
  @override
  List<Object> get props => [];
}

class ErrorAssistanceCostsState extends AssistanceCostsState {
  final String? message;
  @override
  List<Object?> get props => [message];
  const ErrorAssistanceCostsState(this.message);
}

class LoadedAssistanceCostsState extends AssistanceCostsState {
  final CustosAssistenciaisVO custosAssistenciais;
  final DateTime? searchedDate;
  @override
  List<Object> get props => [custosAssistenciais];

  const LoadedAssistanceCostsState(this.custosAssistenciais, this.searchedDate);
}
