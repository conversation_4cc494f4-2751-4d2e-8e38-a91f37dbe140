import 'package:auto_size_text/auto_size_text.dart';
import 'package:cooperado_minha_unimed/bloc/res-internal/image_result/res_image_exam_result_cubit.dart';
import 'package:cooperado_minha_unimed/colors.dart';
import 'package:cooperado_minha_unimed/models/res-internal/image_result.model.dart';
import 'package:cooperado_minha_unimed/shared/utils/logger_print.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:url_launcher/url_launcher.dart';

class ResImageExamResultScreen extends StatefulWidget {
  final String card;
  const ResImageExamResultScreen({super.key, required this.card});

  @override
  State<ResImageExamResultScreen> createState() =>
      _ResImageExamResultScreenState();
}

class _ResImageExamResultScreenState extends State<ResImageExamResultScreen> {
  final logger = UnimedLogger(className: '_ResImageExamResultScreenState');
  TextEditingController controllerSearch = TextEditingController();

  bool loadingDetail = false;

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      color: CooperadoColors.grayLight3,
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16),
        child: BlocConsumer<ResImageExamResultCubit, ResImageExamResultState>(
          listener: (context, state) {},
          builder: (context, state) {
            if (state is LoadingResImageExamResultState) {
              return const Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  SpinKitCircle(color: CooperadoColors.tealGreen),
                  SizedBox(height: 10),
                  AutoSizeText(
                    'Buscando resultados de exames...',
                    style: TextStyle(
                      color: CooperadoColors.tealGreen,
                      fontSize: 12,
                    ),
                  ),
                ],
              );
            } else if (state is ErrorResImageExamResultState) {
              return Center(
                child: Text(
                  state.message,
                  textAlign: TextAlign.center,
                ),
              );
              
            } else if (state is LoadedResImageExamResultState) {
              return Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Padding(
                    padding: const EdgeInsets.all(8.0),
                    child: Container(
                      decoration: const BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.all(
                          Radius.circular(24),
                        ),
                      ),
                      child: _serchImageExamResult(),
                    ),
                  ),
                  Expanded(
                    child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const SizedBox(height: 20),
                          AutoSizeText(
                            '${state.listImageExamResults.length} ${state.listImageExamResults.length > 1 ? 'exames encontrados' : 'exame encontrado'}',
                            style: const TextStyle(
                              color: CooperadoColors.tealGreen,
                              fontSize: 12,
                            ),
                          ),
                          const SizedBox(height: 5),
                          Expanded(
                            child: ListView.builder(
                              physics: const ClampingScrollPhysics(
                                  parent: AlwaysScrollableScrollPhysics()),
                              itemCount: state.listImageExamResults.length,
                              itemBuilder: (context, index) {
                                final imageExamResult =
                                    state.listImageExamResults[index];
                                return _buildImageExamResultItem(
                                    imageExamResultModel: imageExamResult);
                              },
                            ),
                          ),
                        ]),
                  ),
                ],
              );
            }
            return const Center();
          },
        ),
      ),
    );
  }

  Widget _serchImageExamResult() {
    return TextFormField(
      enableInteractiveSelection: false,
      autofocus: false,
      controller: controllerSearch,
      textInputAction: TextInputAction.search,
      style: const TextStyle(color: Colors.green),
      onChanged: (query) {
        debugPrint('query: $query');
        context.read<ResImageExamResultCubit>().searchListImageExamResults(
              searchKey: query,
            );
      },
      maxLines: 1,
      decoration: InputDecoration(
        hintText: 'Buscar exame',
        hintMaxLines: null,
        suffixIcon: IconButton(
          onPressed: () {
            controllerSearch.clear();
            context.read<ResImageExamResultCubit>().searchListImageExamResults(
                  searchKey: '',
                );
          },
          icon: const Icon(
            Icons.clear,
            size: 16,
          ),
        ),
        prefixIcon: const Icon(
          Icons.search,
          size: 24,
          color: CooperadoColors.green,
        ),
        contentPadding: const EdgeInsets.only(
          top: 12,
          left: 10,
          right: 10,
          bottom: 6,
        ),
        border: InputBorder.none,
      ),
    );
  }

  Widget _buildImageExamResultItem(
      {required ResImageExamResultModel imageExamResultModel}) {
    return Hero(
      tag: imageExamResultModel.codOrder,
      child: Card(
        margin: const EdgeInsets.symmetric(vertical: 5),
        child: Row(
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Padding(
                    padding: const EdgeInsets.all(8.0),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const Text("Pedido: "),
                        Text(imageExamResultModel.codOrder,
                            style: const TextStyle(
                                fontSize: 15,
                                fontWeight: FontWeight.bold,
                                color: CooperadoColors.green)),
                      ],
                    ),
                  ),
                  const Padding(
                    padding: EdgeInsets.symmetric(horizontal: 16),
                    child: Divider(
                      color: CooperadoColors.grayLight,
                      thickness: 1,
                    ),
                  ),
                  _buildItemText(
                    title: 'Unidade',
                    value: imageExamResultModel.descriptionExam,
                  ),
                  _buildItemText(
                    title: 'Nome do paciente',
                    value: imageExamResultModel.namePatient,
                  ),
                  _buildItemText(
                    title: 'Data do pedido',
                    value: imageExamResultModel.solicitationDateFormatted,
                  ),
                  const SizedBox(height: 20),
                  _buttonImage(imageExamResultModel),
                  const SizedBox(height: 20),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildItemText({required String title, required String? value}) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          AutoSizeText(
            '$title: ',
            style: const TextStyle(
              color: CooperadoColors.blackText,
              fontSize: 12,
            ),
          ),
          AutoSizeText(
            value ?? 'Não informado',
            style: const TextStyle(
              color: CooperadoColors.blackText,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buttonImage(ResImageExamResultModel resImageExamResultModel) {
    return resImageExamResultModel.imageLinks != null
        ? InkWell(
            onTap: () => {
              _launchURL(resImageExamResultModel.imageLinks![0].link),
            },
            child: const Center(
              child: AutoSizeText(
                'Ver imagem',
                style: TextStyle(
                  color: CooperadoColors.tealGreen,
                  fontSize: 14,
                  decoration: TextDecoration.underline,
                ),
                textAlign: TextAlign.center,
              ),
            ),
          )
        : Container();
  }

  Future _launchURL(String url) async {
    logger.i('launchURL - abrindo url $url');
    if (await canLaunchUrl(Uri.parse(url))) {
      await launchUrl(Uri.parse(url), mode: LaunchMode.externalApplication);
    } else {
      throw 'Unable to open url : $url';
    }
  }
}
