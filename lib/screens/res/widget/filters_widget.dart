import 'package:cooperado_minha_unimed/colors.dart';
import 'package:cooperado_minha_unimed/screens/res/widget/panel_date_filter.dart';
import 'package:evaluation/colors.dart';
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';

class FiltersWidget extends StatelessWidget {
  final bool isLoading;
  final List<int> lastMonthsToFilter;
  final int? filterMonthSelected;
  final DateTimeRange? dateRangeToFilter;
  final ValueChanged<int?> onMonthFilterChanged;
  final VoidCallback? onClearDateRange;
  final Future<DateTime?> Function(BuildContext context, String title,
      {DateTime? firstDate}) selectDateToFilter;
  final ValueChanged<DateTimeRange> onDateRangeSelected;

  const FiltersWidget({
    super.key,
    required this.isLoading,
    required this.lastMonthsToFilter,
    required this.filterMonthSelected,
    required this.dateRangeToFilter,
    required this.onMonthFilterChanged,
    this.onClearDateRange,
    required this.selectDateToFilter,
    required this.onDateRangeSelected,
  });

  @override
  Widget build(BuildContext context) {
    return isLoading
        ? const Padding(
            padding: EdgeInsets.symmetric(vertical: 30),
            child: SpinKitThreeBounce(
              color: UnimedColors.green,
              size: 20,
            ),
          )
        : Column(
            children: [
              AnimatedContainer(
                height: dateRangeToFilter != null ? 70 : 0,
                curve: Curves.easeIn,
                duration: const Duration(milliseconds: 300),
                child: dateRangeToFilter != null
                    ? PanelDateFilter(
                        callendarFirstDate:
                            DateTime.now().subtract(const Duration(days: 365)),
                        dateStartFilter:
                            dateRangeToFilter?.start ?? DateTime.now(),
                        dateEndFilter: dateRangeToFilter?.end ?? DateTime.now(),
                        onSelectDates: (p0, p1) => onDateRangeSelected(
                          DateTimeRange(
                            start: p0,
                            end: p1,
                          ),
                        ),
                      )
                    : SizedBox.fromSize(),
              ),
              Container(
                margin: EdgeInsets.zero,
                padding: EdgeInsets.zero,
                width: MediaQuery.of(context).size.width,
                child: SizedBox(
                  width: MediaQuery.of(context).size.width,
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Expanded(
                        child: SingleChildScrollView(
                          scrollDirection: Axis.horizontal,
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: lastMonthsToFilter.map((key) {
                              return _chipFilter(context, key);
                            }).toList(),
                          ),
                        ),
                      ),
                      if (onClearDateRange != null)
                        IconButton(
                          icon: Icon(
                            dateRangeToFilter == null
                                ? Icons.calendar_month_outlined
                                : Icons.clear_outlined,
                          ),
                          color: CooperadoColors.greenDark,
                          onPressed: dateRangeToFilter != null
                              ? onClearDateRange
                              : () async {
                                  DateTime? dateStart;
                                  DateTime? dateEnd;
                                  dateStart = await selectDateToFilter(
                                    context,
                                    'Selecione a data inicial',
                                    firstDate: DateTime(2000),
                                  );
                                  if (dateStart != null && context.mounted) {
                                    dateEnd = await selectDateToFilter(
                                      context,
                                      'Selecione a data final',
                                      firstDate: dateStart,
                                    );
                                  }
                                  if (dateStart != null && dateEnd != null) {
                                    if (dateStart.isBefore(dateEnd)) {
                                      onDateRangeSelected(DateTimeRange(
                                        start: dateStart,
                                        end: dateEnd,
                                      ));
                                    } else {
                                      if (context.mounted) {
                                        _showMessage(
                                          context,
                                          message:
                                              'A data inicial deve ser anterior a data final',
                                        );
                                      }
                                    }
                                  }
                                },
                        ),
                    ],
                  ),
                ),
              ),
            ],
          );
  }

  Widget _chipFilter(BuildContext context, int filterSelected) {
    return Padding(
      padding: const EdgeInsets.only(right: 10),
      child: InkWell(
        customBorder: const RoundedRectangleBorder(
          borderRadius: BorderRadius.only(
            topRight: Radius.circular(10),
            topLeft: Radius.circular(10),
            bottomLeft: Radius.circular(10),
          ),
        ),
        onTap: () {
          if (filterSelected == filterMonthSelected) {
            onMonthFilterChanged(null);
          } else {
            onMonthFilterChanged(filterSelected);
          }
        },
        child: Chip(
          shape: const RoundedRectangleBorder(
            side: BorderSide(
              color: CooperadoColors.greenDark,
            ),
            borderRadius: BorderRadius.only(
              topRight: Radius.circular(10),
              topLeft: Radius.circular(10),
              bottomLeft: Radius.circular(10),
            ),
          ),
          label: Text(
            'Últimos $filterSelected meses',
            style: TextStyle(
              color: filterSelected == filterMonthSelected
                  ? CooperadoColors.grayLight
                  : CooperadoColors.greenDark,
              fontWeight: FontWeight.bold,
            ),
          ),
          backgroundColor: filterSelected == filterMonthSelected
              ? CooperadoColors.greenDark
              : Colors.transparent,
        ),
      ),
    );
  }

  void _showMessage(BuildContext context, {required String message}) {
    ScaffoldMessenger.of(context).hideCurrentSnackBar();
    SchedulerBinding.instance.addPostFrameCallback((_) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: Colors.red,
        ),
      );
    });
  }
}
