import 'package:json_annotation/json_annotation.dart';
part 'version_remote.model.g.dart';

@JsonSerializable(anyMap: true)
class VersionRemoteModel {
  @Json<PERSON>ey(name: 'force_update')
  final bool? forceUpdate;
  @J<PERSON><PERSON>ey(name: 'build_number')
  final int? buildNumber;
  @J<PERSON><PERSON>ey(name: 'version_number')
  final String? versionNumber;

  VersionRemoteModel({this.forceUpdate, this.buildNumber, this.versionNumber});

  factory VersionRemoteModel.fromJson(Map json) =>
      _$VersionRemoteModelFromJson(json);
  Map<String, dynamic> toJson() => _$VersionRemoteModelToJson(this);
}
