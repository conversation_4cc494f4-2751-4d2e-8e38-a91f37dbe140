// ignore_for_file: non_constant_identifier_names, avoid_types_as_parameter_names

import 'package:auto_size_text/auto_size_text.dart';
import 'package:cooperado_minha_unimed/bloc/notificao/notificacao_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/notificao/notificacao_state.dart';
import 'package:cooperado_minha_unimed/bloc/profile/profile_cubit.dart';
import 'package:cooperado_minha_unimed/colors.dart';
import 'package:cooperado_minha_unimed/models/notificacao/notification.model.dart';
import 'package:cooperado_minha_unimed/screens/notificacao/notification_card.dart';
import 'package:cooperado_minha_unimed/shared/widgets/error-banner/error_banner.component.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';

class NotificacaoScreen extends StatefulWidget {
  const NotificacaoScreen({super.key});

  @override
  State<NotificacaoScreen> createState() => _NotificacaoScreenState();
}

class _NotificacaoScreenState extends State<NotificacaoScreen> {
  final _notificationsCount = ValueNotifier<int?>(null);
  final _scrollController = ScrollController();
  int _currentPage = 1;
  // final int _perPage = 20;
  int? _totalPages;
  List<NotificationModel> _notifications = [];

  void _updateData({bool isPullRefresh = false}) {
    if (isPullRefresh) {
      _currentPage = 1;
      _notifications = [];
      _totalPages = null;
    }

    context.read<NotificacaoCubit>().listNotificacoesResult(
          codPrestador:
              context.read<ProfileCubit>().user.codPrestador.toString(),

          /// TODO - Para ativar a paginação, descomente as linhas abaixo
          // page: _currentPage,
          // perPage: _perPage,
        );
  }

  @override
  void initState() {
    super.initState();
    _updateData();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: CooperadoColors.backgroundColor,
      appBar: AppBar(
        centerTitle: true,
        title: ValueListenableBuilder(
          valueListenable: _notificationsCount,
          builder: (context, int? value, child) {
            return Text(
              'Notificações${value != null && value != 0 ? ' ($value)' : ''}',
              style: const TextStyle(
                color: Colors.white,
                fontSize: 18,
                fontWeight: FontWeight.w600,
              ),
            );
          },
        ),
      ),
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 12),
          child: BlocConsumer<NotificacaoCubit, NotificacaoState>(
            listener: (context, state) {
              if (state is NotificacaoStateLoaded) {
                _totalPages = state.totalPages;
                setState(() {
                  _notifications.addAll(state.notificacoes);
                });
              } else if (state is NotificacaoStateError) {
                _notificationsCount.value = null;
              } else if (state is DoneDeleteNotificacaoState) {
                _updateData(isPullRefresh: true);
              }
            },
            builder: (context, state) {
              if (state is NotificacaoStateLoading && _notifications.isEmpty) {
                return const SpinKitCircle(color: CooperadoColors.tealGreen);
              } else if (state is NotificacaoStateError) {
                return RefreshIndicator(
                  onRefresh: () async {
                    _updateData(isPullRefresh: true);
                  },
                  child: SingleChildScrollView(
                    physics: const AlwaysScrollableScrollPhysics(),
                    child: Center(
                      child: ErrorBanner(
                        message: state.message,
                        backgroundColor: CooperadoColors.backgroundColor,
                      ),
                    ),
                  ),
                );
              }

              if (_notifications.isEmpty) {
                return const Center(
                  child: AutoSizeText('Nenhuma notificação encontrada.'),
                );
              }
              WidgetsBinding.instance.addPostFrameCallback((Timestamped) {
                _notificationsCount.value = _notifications
                    .where((element) => element.readAt == null)
                    .length;
              });

              _sortNotifications();

              return Column(
                children: [
                  Expanded(
                    child: _buildNotificationList(
                      notifications: _notifications,
                    ),
                  ),
                  if (state is NotificacaoStateLoading)
                    const Padding(
                      padding: EdgeInsets.symmetric(vertical: 10),
                      child: SpinKitCircle(
                        color: CooperadoColors.tealGreen,
                      ),
                    ),
                ],
              );
            },
          ),
        ),
      ),
    );
  }

  void _sortNotifications() {
    _notifications.sort((a, b) {
      // Primeiro, compara se são fixadas ou não
      if (a.pinned != b.pinned) {
        return (a.pinned ?? false) ? -1 : 1;
      }
      // Se ambos forem fixados ou ambos não forem, compara a data
      // Converter a string em DateTime
      DateTime dateA = DateTime.parse(a.createdAt ?? '');
      DateTime dateB = DateTime.parse(b.createdAt ?? '');
      return dateB.compareTo(dateA);
    });
  }

  Widget _buildNotificationList(
      {required List<NotificationModel> notifications}) {
    return RefreshIndicator(
      onRefresh: () async => _updateData(isPullRefresh: true),
      child: ListView.builder(
        controller: _scrollController
          ..addListener(
            () {
              if (_scrollController.position.pixels ==
                  _scrollController.position.maxScrollExtent) {
                if (_currentPage < (_totalPages ?? 0)) {
                  _currentPage++;
                  _updateData();
                }
              }
            },
          ),
        shrinkWrap: true,
        itemCount: notifications.length,
        padding: const EdgeInsets.only(top: 10),
        itemBuilder: (context, index) {
          final notification = notifications[index];

          return AnimatedContainer(
            duration: Duration(milliseconds: 400 + index * 150),
            curve: Curves.easeInOut,
            child: ExpandableNotificationItem(
              notification: notification,
              updateNotificationCount: () {
                _notificationsCount.value = _notificationsCount.value! - 1;
              },
              togglePinNotificationStatus: (notification) {
                WidgetsBinding.instance.addPostFrameCallback((Timestamped) {
                  setState(() {
                    notification.pinned = !(notification.pinned ?? false);
                    _sortNotifications();
                  });
                });
              },
            ),
          );
        },
      ),
    );
  }
}
