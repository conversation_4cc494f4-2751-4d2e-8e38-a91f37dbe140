import 'package:cooperado_minha_unimed/bloc/indicators/last_production/last_production_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/sensitive-data/sensitive_data_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/sensitive-data/sensitive_data_state.dart';
import 'package:cooperado_minha_unimed/screens/servicos/card_services_description.dart';
import 'package:cooperado_minha_unimed/screens/servicos/declaracao-ir/card_relatorio_producao_medica.dart';
import 'package:cooperado_minha_unimed/screens/servicos/extrato-quota-parte/card_extrac_quota_part.dart';
import 'package:cooperado_minha_unimed/screens/servicos/status-solicitacao-participacao/card_solicitation_status.dart';
import 'package:cooperado_minha_unimed/shared/screen-transitions/fade.transition.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../colors.dart';
import 'consultar-cbhpm/card_consultar_cbhpm.dart';
import 'consultar-cid/card_consultar_cid.dart';
import 'consultar-opme/card_consultar_opme.dart';

class ServicesScreen extends StatefulWidget {
  final FirebaseAnalytics analytics;
  final FirebaseAnalyticsObserver observer;

  const ServicesScreen(
      {super.key, required this.analytics, required this.observer});

  @override
  ServicesScreenState createState() => ServicesScreenState();
}

class ServicesScreenState extends State<ServicesScreen> {
  @override
  void initState() {
    super.initState();
    widget.analytics.logScreenView(
      screenName: 'Serviços',
      screenClass: 'ServicesScreen',
    );
  }

  @override
  Widget build(BuildContext context) {
    final DateTime lastDate =
        context.read<LastProductionCubit>().lastDate ?? DateTime.now();
    return Scaffold(
      backgroundColor: CooperadoColors.backgroundColor,
      appBar: AppBar(
        centerTitle: true,
        title: const Text(
          "Serviços",
        ),
        backgroundColor: CooperadoColors.tealGreenDark,
        actions: [
          Padding(
            padding: const EdgeInsets.only(right: 15.0),
            child: IconButton(
              icon: BlocBuilder<SensitiveDataCubit, SensitiveDataState>(
                builder: (context, state) {
                  return Icon(
                    state.isSensitiveDataVisible
                        ? Icons.visibility
                        : Icons.visibility_off,
                  );
                },
              ),
              onPressed: () {
                context
                    .read<SensitiveDataCubit>()
                    .toggleSensitiveDataVisibility();
              },
            ),
          ),
        ],
      ),
      body: RefreshIndicator(
        onRefresh: () async {
          Navigator.pushReplacement(
            context,
            FadeRoute(
              page: ServicesScreen(
                analytics: widget.analytics,
                observer: widget.observer,
              ),
            ),
          );
        },
        color: CooperadoColors.tealGreen,
        child: SafeArea(
          child: Container(
            padding: const EdgeInsets.all(8.0),
            child: ListView(
              children: <Widget>[
                const CardServicesDescription(),
                const SizedBox(height: 10.0),
                CardExtractQuotaPart(lastDate: lastDate),
                const CardIRDeclaration(),
                const CardSolicitationParticipationStatus(),
                const CardConsultarCbhpmPart(),
                const CardConsultarOpme(),
                const CardConsultarCid(),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
