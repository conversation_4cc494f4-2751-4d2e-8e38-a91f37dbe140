// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'zipcode.model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ZipCodeModel _$ZipCodeModelFromJson(Map json) => ZipCodeModel(
      adressZipCodeModel: (json['retorno'] as List<dynamic>?)
          ?.map((e) => AddressZipCodeModel.fromJson(e as Map))
          .toList(),
    );

Map<String, dynamic> _$ZipCodeModelToJson(ZipCodeModel instance) =>
    <String, dynamic>{
      'retorno': instance.adressZipCodeModel,
    };

AddressZipCodeModel _$AddressZipCodeModelFromJson(Map json) =>
    AddressZipCodeModel(
      cep: json['cep'] as int?,
      codUf: json['codUf'] as String?,
      codCidade: json['codCidade'] as int?,
      nomeCidade: json['nomeCidade'] as String?,
      codLogradouro: json['codLogradouro'] as int?,
      codTipoLogradouro: json['codTipoLogradouro'] as String?,
      nomeTipoLogradouro: json['nomeTipoLogradouro'] as String?,
      nomeLogradouro: json['nomeLogradouro'] as String?,
      codBairro: json['codBairro'] as int?,
      nomeBairro: json['nomeBairro'] as String?,
      nomeUf: json['nomeUf'] as String?,
      complementoLogradouro: json['complementoLogradouro'] as String?,
    );

Map<String, dynamic> _$AddressZipCodeModelToJson(
        AddressZipCodeModel instance) =>
    <String, dynamic>{
      'cep': instance.cep,
      'codUf': instance.codUf,
      'codCidade': instance.codCidade,
      'nomeCidade': instance.nomeCidade,
      'codLogradouro': instance.codLogradouro,
      'codTipoLogradouro': instance.codTipoLogradouro,
      'nomeTipoLogradouro': instance.nomeTipoLogradouro,
      'nomeLogradouro': instance.nomeLogradouro,
      'codBairro': instance.codBairro,
      'nomeBairro': instance.nomeBairro,
      'nomeUf': instance.nomeUf,
      'complementoLogradouro': instance.complementoLogradouro,
    };
