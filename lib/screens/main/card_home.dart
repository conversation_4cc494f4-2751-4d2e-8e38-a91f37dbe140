import 'package:cooperado_minha_unimed/colors.dart';
import 'package:cooperado_minha_unimed/screens/news/read_news.dart';
import 'package:cooperado_minha_unimed/shared/screen-transitions/fade.transition.dart';
import 'package:cooperado_minha_unimed/shared/vo/transparencia/demostrativo_resultado.dart';
import 'package:cooperado_minha_unimed/shared/widgets/unimed_icon_widget.dart';
import 'package:flutter/material.dart';

class CardHome extends StatefulWidget {
  final Noticia noticia;
  final double width;
  final double height;
  const CardHome(
      {super.key,
      required this.noticia,
      required this.width,
      required this.height});
  @override
  CardHomeState createState() => CardHomeState();
}

class CardHomeState extends State<CardHome> {
  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () {
        Navigator.push(
            context,
            FadeRoute(
                page: ReadNews(
              noticiasPortal: widget.noticia,
            )));
      },
      child: Padding(
        padding: const EdgeInsets.all(8.0),
        child: SizedBox(
          height: widget.height,
          width: widget.width,
          child: Stack(
            children: <Widget>[
              Positioned.fill(
                child: Hero(
                  tag: 'noticia-image-${widget.noticia.id}',
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(10),
                    child: FittedBox(
                      fit: BoxFit.cover,
                      child: Image.network(widget.noticia.seo!.image!,
                          loadingBuilder: (context, child,
                              ImageChunkEvent? loadingProgress) {
                        if (loadingProgress == null) return child;
                        return Padding(
                          padding: const EdgeInsets.all(40.0),
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            value: loadingProgress.expectedTotalBytes != null
                                ? loadingProgress.cumulativeBytesLoaded /
                                    loadingProgress.expectedTotalBytes!
                                : null,
                          ),
                        );
                      }, errorBuilder:
                              (context, exception, StackTrace? stackTrace) {
                        return UnimedIconWidget(
                            width: widget.width / 2, height: widget.height / 2);
                      }),
                    ),
                  ),
                ),
              ),
              Positioned(
                bottom: 0,
                child: Container(
                  width: widget.width,
                  height: widget.height / 3,
                  decoration: const BoxDecoration(
                    color: CooperadoColors.tealGreen,
                    borderRadius: BorderRadius.only(
                      bottomLeft: Radius.circular(10),
                      bottomRight: Radius.circular(10),
                    ),
                  ),
                  child: Padding(
                    padding: const EdgeInsets.only(left: 10, right: 10, top: 5),
                    child: Wrap(
                      alignment: WrapAlignment.start,
                      children: <Widget>[
                        Text(
                          '${widget.noticia.titulo}',
                          softWrap: true,
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                          style: const TextStyle(
                              color: Colors.white, fontWeight: FontWeight.bold),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
              Positioned(
                left: 10,
                bottom: widget.height / 3 - 5,
                child: Center(
                  child: Text(
                    '${widget.noticia.tipoNoticia?[0]}',
                    style: const TextStyle(
                      backgroundColor: CooperadoColors.orange,
                      fontWeight: FontWeight.w900,
                      fontSize: 10,
                      color: Colors.white,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
