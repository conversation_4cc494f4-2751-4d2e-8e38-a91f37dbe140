class ResBrazilBeneficiaryModel {
  late final String name;
  String? socialName;
  late final String cpf;
  late final String card;
  List<PermissionsBeneficiaryModel> permissions = [];

  ResBrazilBeneficiaryModel({
    required this.name,
    this.socialName,
    required this.cpf,
    required this.card,
    required this.permissions,
  });

  ResBrazilBeneficiaryModel.fromJson(Map<String, dynamic> json) {
    name = json['name'];
    socialName = json['socialName'];
    cpf = json['cpf'];
    card = json['card'];
    if (json['permissions'] != null) {
      permissions = <PermissionsBeneficiaryModel>[];
      json['permissions'].forEach((v) {
        permissions.add(PermissionsBeneficiaryModel.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['name'] = name;
    data['socialName'] = socialName;
    data['cpf'] = cpf;
    data['card'] = card;
    data['permissions'] = permissions.map((v) => v.toJson()).toList();
    return data;
  }
}

class PermissionsBeneficiaryModel {
  late String name;
  late bool value;

  PermissionsBeneficiaryModel({required this.name, required this.value});

  PermissionsBeneficiaryModel.fromJson(Map<String, dynamic> json) {
    name = json['name'];
    value = json['value'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['name'] = name;
    data['value'] = value;
    return data;
  }
}

extension PermissionExtension on ResBrazilBeneficiaryModel {
  bool get resExternalService =>
      _getPermissionValueByName('resExternalService');
  bool get resExternalAllergy =>
      _getPermissionValueByName('resExternalAllergy');
  bool get resExternalProcedure =>
      _getPermissionValueByName('resExternalProcedure');
  bool get resExternalDocument =>
      _getPermissionValueByName('resExternalDocument');
  bool get resExternalDiagnostic =>
      _getPermissionValueByName('resExternalDiagnostic');

  bool _getPermissionValueByName(String name) {
    try {
      return permissions
          .firstWhere((permission) => permission.name == name)
          .value;
    } catch (e) {
      return false;
    }
  }
}
