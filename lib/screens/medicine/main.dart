import 'package:auto_size_text/auto_size_text.dart';
import 'package:cooperado_minha_unimed/bloc/medicines/medicines_cubit.dart';
import 'package:cooperado_minha_unimed/bloc/medicines/search-medicine/search_medicines_cubit.dart';
import 'package:cooperado_minha_unimed/colors.dart';
import 'package:cooperado_minha_unimed/models/medicine.model.dart';
import 'package:cooperado_minha_unimed/screens/medicine/details/main.dart';
import 'package:cooperado_minha_unimed/screens/medicine/search_medicine_widget.dart';
import 'package:cooperado_minha_unimed/shared/widgets/error-banner/error_banner.component.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';

class MedicineScreen extends StatefulWidget {
  const MedicineScreen({super.key});

  @override
  State<MedicineScreen> createState() => _MedicineScreenState();
}

class _MedicineScreenState extends State<MedicineScreen> {
  @override
  void initState() {
    super.initState();
    BlocProvider.of<SearchMedicinesCubit>(context).setToInitial();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        centerTitle: true,
        title: const Text("Medicamentos"),
        backgroundColor: CooperadoColors.tealGreenDark,
        actions: [
          IconButton(
            onPressed: () {
              if (BlocProvider.of<MedicinesCubit>(context).state is LoadingMedicinesState) {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Sincronização em andamento...'),
                    duration: Duration(seconds: 5),
                    backgroundColor: CooperadoColors.redCancel,
                  ),
                );
              } else {
                BlocProvider.of<MedicinesCubit>(context).syncAllMedicines();
              }
            },
            icon: const Icon(Icons.update),
          ),
        ],
      ),
      body: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16),
        child: BlocConsumer<MedicinesCubit, MedicinesState>(
          listener: (context, state) {},
          builder: (context, state) {
            if (state is LoadingMedicinesState) {
              return const Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  SpinKitCircle(color: CooperadoColors.tealGreen),
                  SizedBox(height: 10),
                  AutoSizeText(
                    'Sincronizando medicamentos...',
                    style: TextStyle(
                      color: CooperadoColors.tealGreen,
                      fontSize: 12,
                    ),
                  ),
                ],
              );
            } else if (state is ErrorMedicinesState) {
              return ErrorBanner(message: state.message);
            } else if (state is LoadedListMedicinesState) {
              return Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    decoration: const BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(10.0),
                        bottomLeft: Radius.circular(10.0),
                      ),
                    ),
                    child: const SearchMedicineWidget(),
                  ),
                  Expanded(
                    child: BlocBuilder<SearchMedicinesCubit, SearchMedicinesState>(
                      builder: (context, searchState) {
                        if (searchState is LoadingSearchMedicinesState) {
                          return const Center(
                            child: SpinKitCircle(color: CooperadoColors.tealGreen),
                          );
                        } else if (searchState is ErrorSearchMedicinesState) {
                          return ErrorBanner(message: searchState.message);
                        } else if (searchState is SearchMedicinesInitial) {
                          return const Center(
                            child: AutoSizeText(
                              'Digite o nome do medicamento ou princípio ativo',
                              style: TextStyle(
                                color: CooperadoColors.tealGreen,
                                fontSize: 14,
                              ),
                              textAlign: TextAlign.center,
                            ),
                          );
                        } else if (searchState is LoadedSearchListMedicinesState) {
                          return Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const SizedBox(height: 20),
                              AutoSizeText(
                                '${searchState.medicines.length} ${searchState.medicines.length > 1 ? 'medicamentos encontrados' : 'medicamento encontrado'}',
                                style: const TextStyle(
                                  color: CooperadoColors.tealGreen,
                                  fontSize: 12,
                                ),
                              ),
                              const SizedBox(height: 5),
                              Expanded(
                                child: ListView.builder(
                                  itemCount: searchState.medicines.length,
                                  itemBuilder: (context, index) {
                                    final medicine = searchState.medicines[index];
                                    return _buildMedicineItem(medicine: medicine);
                                  },
                                ),
                              ),
                            ],
                          );
                        }
                        return const Center();
                      },
                    ),
                  ),
                ],
              );
            }
            return const Center();
          },
        ),
      ),
    );
  }

  Widget _buildMedicineItem({required MedicineModel medicine}) {
    return Hero(
      tag: medicine.id,
      child: Card(
        margin: const EdgeInsets.symmetric(vertical: 5),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            _buildItemText(
              title: 'Valor da última compra',
              value: medicine.valorUltimaEntradaFormatado,
              setBackgroundColor: false,
              isPrice: true,
            ),
            _buildItemText(
              title: 'Produto',
              value: medicine.descricaoProduto,
              setBackgroundColor: true,
            ),
            _buildItemText(
              title: 'Unidade',
              value: medicine.descricaoUnidade,
              setBackgroundColor: false,
            ),
            _buildItemText(
              title: 'Princípio ativo',
              value: medicine.descricaoSubstancia,
              setBackgroundColor: true,
            ),
            const SizedBox(height: 20),
            InkWell(
              onTap: () => Navigator.push(context, MaterialPageRoute(builder: (context) => MedicineDetailsScreen(medicine: medicine))),
              child: const Center(
                child: AutoSizeText(
                  'Ver detalhes',
                  style: TextStyle(
                    color: CooperadoColors.tealGreen,
                    fontSize: 14,
                    decoration: TextDecoration.underline,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
            ),
            const SizedBox(height: 20),
          ],
        ),
      ),
    );
  }

  Widget _buildItemText({required String title, required String? value, required bool setBackgroundColor, bool isPrice = false}) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      color: setBackgroundColor ? CooperadoColors.aliceBlueLight : Colors.white,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          AutoSizeText(
            '$title: ',
            style: const TextStyle(
              color: CooperadoColors.blackText,
              fontSize: 16,
            ),
          ),
          Expanded(
            child: AutoSizeText(
              value ?? 'Não informado',
              style: TextStyle(
                color: isPrice ? CooperadoColors.green : CooperadoColors.blackText,
                fontWeight: FontWeight.bold,
                fontSize: isPrice ? 20 : 16,
              ),
              textAlign: TextAlign.end,
            ),
          ),
        ],
      ),
    );
  }
}
