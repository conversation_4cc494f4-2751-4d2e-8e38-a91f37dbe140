import 'dart:convert';

import 'package:cooperado_minha_unimed/models/channel_ethics.model.dart';
import 'package:cooperado_minha_unimed/models/user.model.dart';
import 'package:cooperado_minha_unimed/shared/api/auth.api.dart';
import 'package:cooperado_minha_unimed/shared/exceptions.dart';
import 'package:cooperado_minha_unimed/shared/flavor_config.dart';
import 'package:cooperado_minha_unimed/shared/locator.dart';
import 'package:cooperado_minha_unimed/shared/utils/logger_print.dart';
import 'package:http_client/http_client.dart';

class ChannelEthicsApi {
  final UnimedHttpClient httpClient;
  final logger = UnimedLogger(className: 'ChannelEthicsApi');

  ChannelEthicsApi(this.httpClient);

  Future<ChannelEthicsResponsive> getChannelEthics() async {
    //capturar token do crm
    UserCredentials? credentials =
        await Locator.instance!.get<AuthApi>().getCredentials();
    final token = credentials != null
        ? await User.createToken(credentials: credentials)
        : "";

    try {
      final url =
          '${FlavorConfig.instance!.values.portal.url}canal-etica?token=${token.toUpperCase()}';
      final response = await httpClient.post(
        Uri.parse(url),
       headers: {
        "Content-Type": "application/json",
       }
      );

      if (response.statusCode == 200 ) {
        final retorno =
            ChannelEthicsResponsive.fromJson(jsonDecode(utf8.decode(response.bodyBytes)));
        logger.i(
            'getChannelEthics sucess: ${response.statusCode} | ${response.body}');
      
        return retorno;
      } else {
        final message = jsonDecode(response.body);
        logger.e(
            'getChannelEthics error statusCode: ${response.statusCode} ${response.body}');
        throw UnimedException('$message');
      }
    } on UnimedException catch (ex) {
      logger.e('getChannelEthics ${ex.runtimeType} : $ex');
      throw ChannelEthicsException(ex.message);
    } catch (ex) {
      logger.e('getChannelEthics exception : $ex');
      throw ChannelEthicsException('Não foi possível no momento.');
    }
  }
}
