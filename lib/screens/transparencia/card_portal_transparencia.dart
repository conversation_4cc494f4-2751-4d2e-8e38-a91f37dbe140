import 'package:cooperado_minha_unimed/bloc/auth/auth_cubit.dart';
import 'package:cooperado_minha_unimed/colors.dart';
import 'package:cooperado_minha_unimed/shared/constants.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class CardPortalTransparencia extends StatefulWidget {
  const CardPortalTransparencia({super.key});

  @override
  CardPortalTransparenciaState createState() => CardPortalTransparenciaState();
}

class CardPortalTransparenciaState extends State<CardPortalTransparencia> {
  double total = 0.0;
  DateTime? selectedDateTime;
  String description = "";
  bool _isExpanded = false;

  @override
  void initState() {
    description = context.read<AuthCubit>().getMessageConfig(transparencia);

    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8),
      margin: const EdgeInsets.only(top: 10, left: 10, right: 10),
      decoration: BoxDecoration(
        color:
            _isExpanded ? CooperadoColors.aliceBlue : CooperadoColors.tealGreen,
        borderRadius: BorderRadius.circular(15),
      ),
      child: Theme(
        data: ThemeWidgets.expansionTile(
          context,
          colorArrow: _isExpanded ? CooperadoColors.tealGreen : Colors.white,
        ),
        child: ListTileTheme(
          contentPadding: const EdgeInsets.symmetric(horizontal: 8),
          child: ExpansionTile(
            onExpansionChanged: (value) {
              setState(() {
                _isExpanded = value;
              });
            },
            title: Text(
              'Portal da Transparência',
              style: TextStyle(
                color: _isExpanded ? CooperadoColors.blackText : Colors.white,
              ),
            ),
            children: <Widget>[
              Padding(
                padding: const EdgeInsets.all(8),
                child: Text(
                  description,
                  style: TextStyle(
                    color:
                        _isExpanded ? CooperadoColors.blackText : Colors.white,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
