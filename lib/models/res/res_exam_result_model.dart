class ResExamResultModel {
  String? url;
  String? date;
  String? codeTUSS;
  String? codeReport;
  late String codeDetail;
  String? type;

  ResExamResultModel(
      {this.url,
      this.date,
      this.codeTUSS,
      this.codeReport,
      required this.codeDetail,
      this.type});

  ResExamResultModel.fromJson(Map<String, dynamic> json) {
    url = json['url'];
    date = json['date'];
    codeTUSS = json['codeTUSS'];
    codeReport = json['codeReport'];
    codeDetail = json['codeDetail'];
    type = json['type'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['url'] = url;
    data['date'] = date;
    data['codeTUSS'] = codeTUSS;
    data['codeReport'] = codeReport;
    data['codeDetail'] = codeDetail;
    data['type'] = type;
    return data;
  }
}
