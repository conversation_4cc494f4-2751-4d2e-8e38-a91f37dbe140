part of 'read_cubit.dart';

abstract class ReadState extends Equatable {
  const ReadState();

  @override
  List<Object?> get props => [];
}

class ReadInitial extends ReadState {}

class LoadingReadNewsState extends ReadState {
  @override
  List<Object> get props => [];
}

class ErrorReadNewsState extends ReadState {
  final String message;
  @override
  List<Object> get props => [message];
  const ErrorReadNewsState(this.message);
}

class DoneReadNewsState extends ReadState {
  final String? message;
  @override
  List<Object?> get props => [message];
  const DoneReadNewsState({this.message});
}
